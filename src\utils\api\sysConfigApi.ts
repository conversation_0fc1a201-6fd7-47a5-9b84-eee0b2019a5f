import { DataField } from '../../types';
import { buildEndpointUrl } from './apiConfig';
import { useAuthStore } from '../../store/authStore';

/**
 * 獲取配置
 * @param key 配置鍵名
 * @returns 配置值
 */
export async function getConfig<T>(key: string): Promise<T> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('configs', key), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error(`獲取配置失敗: Unauthorized`);
      }
      throw new Error(`獲取配置失敗: ${response.statusText}`);
    }
    
    return await response.json() as T;
  } catch (error) {
    console.error(`獲取配置失敗 (${key}):`, error);
    throw error;
  }
}

/**
 * 保存配置
 * @param key 配置鍵名
 * @param value 配置值
 */
export async function saveConfig<T>(key: string, value: T): Promise<void> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('configs', key), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(value),
      credentials: 'include', // 包含 cookie
    });
    
    if (!response.ok) {
      throw new Error(`保存配置失敗: ${response.statusText}`);
    }
  } catch (error) {
    console.error(`保存配置失敗 (${key}):`, error);
    throw error;
  }
}

// 系統配置介面定義
export interface SystemConfig {
  timeout?: number;
  retryCount?: number;
  bufferSize?: number;
  maxBindingDataCount?: number;
  [key: string]: any;
}

/**
 * 獲取所有配置
 * @returns 所有配置的物件
 */
export async function getAllConfigs(): Promise<Record<string, any>> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('configs'), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`獲取所有配置失敗: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('獲取所有配置失敗:', error);
    throw error;
  }
}

/**
 * 獲取系統配置
 * @returns 系統配置物件
 */
export async function getSysConfig(): Promise<SystemConfig> {
  try {
    return await getConfig<SystemConfig>('system');
  } catch (error) {
    console.error('獲取系統配置失敗:', error);
    
    // 使用集中管理的默認系統配置
    const { getDefaultConfig } = await import('./sysConfig');
    const defaultConfig = getDefaultConfig('system');
    
    // 嘗試創建新的系統配置
    try {
      console.log('找不到系統配置，嘗試創建默認配置');
      await saveConfig('system', defaultConfig);
      console.log('成功創建默認系統配置');
      return defaultConfig;
    } catch (saveError) {
      console.error('創建默認系統配置失敗:', saveError);
      return defaultConfig; // 返回默認配置，即使保存失敗
    }
  }
}

/**
 * 更新系統配置
 * @param config 系統配置物件
 */
export async function updateSysConfig(config: SystemConfig): Promise<void> {
  try {
    await saveConfig('system', config);
  } catch (error) {
    console.error('更新系統配置失敗:', error);
    throw error;
  }
}

/**
 * 刪除配置
 * @param key 配置鍵名
 */
export async function deleteConfig(key: string): Promise<void> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();
    
    const response = await fetch(buildEndpointUrl('configs', key), {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });
    
    if (!response.ok && response.status !== 204) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`刪除配置失敗: ${response.statusText}`);
    }
  } catch (error) {
    console.error(`刪除配置失敗 (${key}):`, error);
    throw error;
  }
}

// 針對資料欄位視圖配置的工具函數
/**
 * 欄位視圖配置接口
 */
export interface FieldsViewConfig {
  visibleFields: string[];
  columnOrder: string[];
  columnWidths: Record<string, number>;
}

/**
 * 保存欄位顯示設定
 * @param config 欄位顯示設定
 */
export async function saveFieldsViewConfig(config: any): Promise<void> {
  return saveConfig('fieldsView', config);
}

/**
 * 獲取欄位顯示設定
 * @returns 欄位顯示設定
 */
export async function getFieldsViewConfig(): Promise<any> {
      return getConfig<any>('fieldsView');
  }
