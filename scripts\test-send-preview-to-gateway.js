/**
 * 測試發送預覽圖到網關功能
 * 此腳本用於測試將設備的預覽圖發送到網關的功能
 */

const { ObjectId } = require('mongodb');
const { MongoClient } = require('mongodb');
const WebSocket = require('ws');
const sendPreviewToGateway = require('../services/sendPreviewToGateway');

// 連接到MongoDB
const connectDB = async () => {
  const uri = 'mongodb://localhost:27017';
  const client = new MongoClient(uri);
  await client.connect();
  const db = client.db('epd-manager');
  return { client, db };
};

// 主函數
async function main() {
  try {
    console.log('開始測試發送預覽圖到網關功能...');
    
    // 初始化服務
    sendPreviewToGateway.initDB(connectDB);
    
    // 獲取資料庫連接
    const { db, client } = await connectDB();
    
    // 獲取一個有預覽圖和數據綁定的設備
    const device = await db.collection('devices').findOne({
      previewImage: { $exists: true },
      $or: [
        { dataBindings: { $exists: true } },
        { dataId: { $exists: true, $ne: '' } }
      ],
      primaryGatewayId: { $exists: true }
    });
    
    if (!device) {
      console.error('找不到符合條件的設備（需要有預覽圖、數據綁定和主要網關）');
      await client.close();
      return;
    }
    
    console.log(`找到符合條件的設備: ${device._id} (MAC: ${device.macAddress})`);
    console.log(`主要網關ID: ${device.primaryGatewayId}`);
    console.log(`是否有預覽圖: ${device.previewImage ? '是' : '否'}`);
    console.log(`是否有數據綁定: ${device.dataBindings ? '是' : '否'}`);
    
    // 手動模擬一個網關WebSocket連接
    // 注意：這只是模擬用，實際運行時應該連接到真實的網關
    const wss = new WebSocket.Server({ port: 8080 });
    
    wss.on('connection', (ws) => {
      console.log('模擬網關連接成功');
      
      // 監聽消息
      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message.toString());
          console.log('收到消息:', data.type);
            // 如果是更新預覽圖命令
          if (data.type === 'update_preview') {
            console.log('收到更新預覽圖命令:');
            console.log(`  設備MAC: ${data.deviceMac}`);
            console.log(`  圖像數據長度: ${data.imageData ? data.imageData.length : 0} 字符`);
            console.log(`  圖像校驗碼: ${data.imageCode}`);
            console.log(`  時間戳: ${data.timestamp}`);
          }
        } catch (error) {
          console.error('解析消息失敗:', error);
        }
      });
      
      // 模擬網關發送訊息
      ws.send(JSON.stringify({
        type: 'connected',
        message: '模擬網關已連接',
        gatewayId: device.primaryGatewayId.toString(),
        timestamp: Date.now()
      }));
    });
    
    // 等待WebSocket服務器啟動
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 創建一個測試用的WebSocket客戶端以模擬網關
    const ws = new WebSocket('ws://localhost:8080');
    
    ws.on('open', async () => {
      console.log('已連接到模擬WebSocket服務器');
      
      // 模擬將設備預覽圖發送到網關
      try {
        console.log(`準備發送設備 ${device._id} 的預覽圖到網關 ${device.primaryGatewayId}...`);
        
        // 模擬websocketService.isGatewayOnline和sendCommandToGateway函數
        const websocketService = {
          isGatewayOnline: () => true,
          sendCommandToGateway: (gatewayId, command) => {
            console.log(`發送命令到網關 ${gatewayId}`);
            ws.send(JSON.stringify(command));
            return { success: true, message: '命令已發送' };
          },
          logDeviceEvent: () => Promise.resolve()
        };
        
        // 替換sendPreviewToGateway中的websocketService引用
        sendPreviewToGateway.__proto__.websocketService = websocketService;          // 計算 imageData 的 CRC 驗證碼
        const calculateCRC = (data) => {
          // 簡單的 CRC32 實現，確保與前端和服務器端計算邏輯一致
          let crc = 0;
          const str = typeof data === 'string' ? data : JSON.stringify(data);
          for(let i = 0; i < str.length; i++) {
            crc = ((crc << 5) + crc) ^ str.charCodeAt(i);
          }
          // 轉換為 4 字節的十六進制字符串
          return (crc >>> 0).toString(16).padStart(8, '0');
        };
        
        // 手動發送預覽圖到網關
        const imageData = device.previewImage;
        const imageCode = calculateCRC(imageData);
        
        const message = {
          type: 'update_preview',
          deviceMac: device.macAddress,
          imageData: imageData,
          imageCode: imageCode,
          timestamp: new Date().toISOString()
        };
        
        // 發送消息
        websocketService.sendCommandToGateway(device.primaryGatewayId.toString(), message);
        
        console.log('預覽圖已發送到模擬網關');
        
        // 等待一段時間後關閉連接
        setTimeout(() => {
          ws.close();
          wss.close();
          client.close();
          console.log('測試完成，連接已關閉');
        }, 3000);
      } catch (error) {
        console.error('發送預覽圖失敗:', error);
        ws.close();
        wss.close();
        client.close();
      }
    });
    
    ws.on('error', (error) => {
      console.error('WebSocket連接錯誤:', error);
      wss.close();
      client.close();
    });
  } catch (error) {
    console.error('測試過程中發生錯誤:', error);
  }
}

// 執行主函數
main().catch(console.error);
