import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { DataFieldTab } from './DataFieldTab';
import { UserInfoTab } from './UserInfoTab';
import { ResourceManagementTab } from './ResourceManagementTab';
import { GatewaySettingsTab } from './GatewaySettingsTab';
import { ParamSettingTab } from './ParamSettingTab';
import { KeyToolTab } from './KeyToolTab';
import { LanguageTest } from '../ui/LanguageTest';

type TabType = 'dataField' | 'userInfo' | 'resourceManagement' | 'gatewaySettings' | 'paramSetting' | 'keyTool' | 'language';

export function SystemConfigPage() {
  const [activeTab, setActiveTab] = useState<TabType>('dataField');

  const { t } = useTranslation();

  const tabs = [
    { id: 'dataField' as TabType, label: t('systemConfig.dataField') },
    { id: 'userInfo' as TabType, label: t('systemConfig.userInfo') },
    { id: 'resourceManagement' as TabType, label: t('systemConfig.resourceManagement') },
    { id: 'gatewaySettings' as TabType, label: t('systemConfig.gatewaySettings') },
    { id: 'paramSetting' as TabType, label: t('systemConfig.paramSetting') },
    { id: 'keyTool' as TabType, label: t('systemConfig.keyTool') },
    { id: 'language' as TabType, label: t('systemConfig.language') },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dataField':
        return <DataFieldTab />;
      case 'userInfo':
        return <UserInfoTab />;
      case 'resourceManagement':
        return <ResourceManagementTab />;
      case 'gatewaySettings':
        return <GatewaySettingsTab />;
      case 'paramSetting':
        return <ParamSettingTab />;
      case 'keyTool':
        return <KeyToolTab />;
      case 'language':
        return (
          <div className="p-6">
            <h2 className="text-xl font-bold mb-4">{t('systemConfig.language')}</h2>
            <LanguageTest />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="p-8 lg:px-4 py-2">
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="border-b">
            <nav className="flex">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-6 py-3 text-sm font-medium transition-colors
                    ${activeTab === tab.id
                      ? 'bg-orange-500 text-white'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                    }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
          <div className="min-h-[600px]">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
}