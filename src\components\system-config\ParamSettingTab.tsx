import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { bindingCore } from '../../utils/dataBinding/bindingCore';
import { getSysConfig, updateSysConfig } from '../../utils/api/sysConfigApi';
import { AlertCircle } from 'lucide-react';

export function ParamSettingTab() {
  const { t } = useTranslation();
  const [timeout, setTimeout] = useState<number>(5000);
  const [retryCount, setRetryCount] = useState<number>(3);
  const [bufferSize, setBufferSize] = useState<number>(1024);
  const [maxBindingDataCount, setMaxBindingDataCount] = useState<number>(8);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // 加載系統配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setIsLoading(true);
        const config = await getSysConfig();
        if (config) {
          setTimeout(config.timeout || 5000);
          setRetryCount(config.retryCount || 3);
          setBufferSize(config.bufferSize || 1024);
          setMaxBindingDataCount(config.maxBindingDataCount || 8);
        }
      } catch (err) {
        console.error('載入系統配置失敗:', err);
        setError('載入系統配置失敗，請重試');
      } finally {
        setIsLoading(false);
      }
    };

    loadConfig();
  }, []);

  // 保存系統配置
  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError(null);
      setSuccess(null);

      await updateSysConfig({
        timeout,
        retryCount,
        bufferSize,
        maxBindingDataCount
      });

      // 同時更新綁定核心的最大綁定數量
      bindingCore.setMaxBindingDataCount(maxBindingDataCount);

      setSuccess('參數設定已保存');
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      console.error('保存系統配置失敗:', err);
      setError('保存系統配置失敗，請重試');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold mb-4">{t('systemConfig.paramSetting')}</h2>

      {/* 錯誤提示 */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700 flex items-center">
          <AlertCircle className="w-5 h-5 mr-2" />
          <span>{error}</span>
          <button
            onClick={() => setError(null)}
            className="ml-auto text-red-700 hover:text-red-900"
          >
            ×
          </button>
        </div>
      )}

      {/* 成功提示 */}
      {success && (
        <div className="mb-4 p-3 bg-green-100 border-l-4 border-green-500 text-green-700">
          {success}
        </div>
      )}

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">{t('systemConfig.connectionTimeout')}</label>
          <input
            type="number"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="5000"
            value={timeout}
            onChange={(e) => setTimeout(parseInt(e.target.value) || 0)}
            disabled={isLoading || isSaving}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">{t('systemConfig.retryCount')}</label>
          <input
            type="number"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="3"
            value={retryCount}
            onChange={(e) => setRetryCount(parseInt(e.target.value) || 0)}
            disabled={isLoading || isSaving}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">{t('systemConfig.bufferSize')}</label>
          <input
            type="number"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="1024"
            value={bufferSize}
            onChange={(e) => setBufferSize(parseInt(e.target.value) || 0)}
            disabled={isLoading || isSaving}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">{t('systemConfig.maxBindingDataCount')}</label>
          <input
            type="number"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="8"
            min={1}
            max={20}
            value={maxBindingDataCount}
            onChange={(e) => setMaxBindingDataCount(parseInt(e.target.value) || 8)}
            disabled={isLoading || isSaving}
          />
          <p className="mt-1 text-sm text-gray-500">{t('systemConfig.maxBindingDataCountDesc')}</p>
        </div>

        <div className="pt-4">
          <button
            onClick={handleSave}
            disabled={isLoading || isSaving}
            className={`px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 ${(isLoading || isSaving) ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isSaving ? t('common.saving') : t('systemConfig.saveSettings')}
          </button>
        </div>
      </div>
    </div>
  );
}