// WebSocket 測試客戶端 (從複製的WebSocket資訊啟動)
const WebSocket = require('ws');
const readline = require('readline');
const fs = require('fs');
const path = require('path');

// 用於存儲設備的 imageCode，模擬真實網關的行為
const deviceImageCodes = {};

// 建立命令行界面
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 提示用戶輸入
const prompt = (query) => new Promise((resolve) => rl.question(query, resolve));

// 建立 WebSocket 連接
function connectWebSocket(wsInfo) {
  // 用於存儲用戶添加的自定義設備
  const customDevices = [];

  // 生成三個固定的設備MAC地址（在連接建立時生成一次）
  const device1Mac = generateRandomMac();
  const device2Mac = generateRandomMac();
  const device3Mac = generateRandomMac();

  // 初始化預設設備的 imageCode（如果尚未設置）
  [device1Mac, device2Mac, device3Mac].forEach(mac => {
    if (!deviceImageCodes[mac]) {
      deviceImageCodes[mac] = Math.floor(Math.random() * 100000000).toString();
    }
  });

  // 構建 WebSocket URL
  const url = wsInfo.url + (wsInfo.url.includes('?') ? '&' : '?') + 'token=' + wsInfo.token;
  console.log(`嘗試連接到 WebSocket 服務器: ${url}`);

  const ws = new WebSocket(url);
  let pingInterval, deviceStatusInterval, gatewayInfoTimeout;

  // 清理定時器
  function clearIntervals() {
    if (pingInterval) clearInterval(pingInterval);
    if (deviceStatusInterval) clearInterval(deviceStatusInterval);
    if (gatewayInfoTimeout) clearInterval(gatewayInfoTimeout);
  }

  ws.on('open', () => {
    console.log('連接已建立');

    // 發送 ping 消息
    const pingMessage = {
      type: 'ping',
      timestamp: Date.now()
    };

    const pingMessageStr = JSON.stringify(pingMessage);
    console.log('\n===== 發送消息 =====');
    console.log('類型: ping 消息');
    console.log('內容:', pingMessage);
    console.log('JSON格式:', pingMessageStr);
    console.log('=====================\n');
    ws.send(pingMessageStr);

    // 注意：網關信息消息將在收到welcome消息後發送

    // 定期發送心跳 (ping) 消息，確保連接保持活躍
    pingInterval = setInterval(() => {
      const pingMessage = {
        type: 'ping',
        timestamp: Date.now()
      };
      const pingMessageStr = JSON.stringify(pingMessage);
      console.log('\n===== 發送消息 =====');
      console.log('類型: 定期 ping 消息');
      console.log('內容:', pingMessage);
      console.log('JSON格式:', pingMessageStr);
      console.log('=====================\n');
      ws.send(pingMessageStr);
    }, 25000);  // 設置為25秒，低於服務器端的30秒心跳檢查

    // 顯示命令幫助
    function showHelp() {
      console.log('\n可用命令:');
      console.log('  help - 顯示此幫助信息');
      console.log('  q - 退出程序');
      console.log('  add - 添加自定義設備');
      console.log('  list - 列出所有當前模擬的設備');
      console.log('  remove <序號> - 移除指定序號的自定義設備');
      console.log('  request-image <裝置MAC> - 請求特定裝置的預覽圖像');
      console.log('\n說明:');
      console.log('  - 設備回報不包含 dataId（由前端或API控制的欄位）');
      console.log('  - 設備回報不包含 imageCode（符合 server 架構）');
      console.log('  - 當收到 server 的圖像更新時，會自動更新本地 imageCode');
      console.log('  - 下次設備狀態回報時會包含更新後的 imageCode');
    }

    // 添加自定義設備
    async function addCustomDevice() {
      try {
        const mac = await prompt('請輸入設備 MAC 地址 (格式如 AA:BB:CC:DD:EE:FF): ');
        if (!mac.match(/^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$/)) {
          console.log('錯誤: MAC 地址格式不正確，請使用格式 AA:BB:CC:DD:EE:FF');
          return;
        }

        const status = (await prompt('請輸入設備狀態 (online/offline，默認 online): ')) || 'online';
        const size = await prompt('請輸入設備尺寸 (例如 2.9", 4.2"，默認 2.9"): ') || '2.9"';
        const imageCode = await prompt('請輸入設備 imageCode (可選，用於模擬設備回報): ') || '';
        const colorType = await prompt('請輸入設備顏色類型 (BW/BWR/BWRY，默認 BW): ') || 'BW';

        // 如果用戶提供了 imageCode，存儲到本地映射中
        if (imageCode) {
          deviceImageCodes[mac] = imageCode;
          console.log(`已將 imageCode ${imageCode} 存儲到設備 ${mac} 的本地映射中`);
        }

        // 創建自定義設備（不包含 dataId 和 imageCode，符合 server 架構要求）
        const customDevice = {
          macAddress: mac,
          status: status,
          // 注意：不包含 dataId，因為這是由前端或API控制的欄位，不是由裝置自己回報的欄位
          data: {
            size: size,
            battery: Math.floor(Math.random() * 100),
            rssi: -1 * Math.floor(Math.random() * 100),
            // 注意：不包含 imageCode，因為裝置回報不應該包含此字段
            colorType: colorType
          }
        };

        // 添加到自定義設備列表
        customDevices.push(customDevice);
        console.log(`成功添加自定義設備: MAC=${mac}, 尺寸=${size}`);
        console.log(`目前共有 ${customDevices.length} 個自定義設備`);
      } catch (err) {
        console.error('添加設備時出錯:', err.message);
      }
    }

    // 列出所有設備
    function listDevices() {
      console.log('\n設備列表:');
      console.log('預設設備:');
      console.log(`1. 2.9吋 BWR 設備 - MAC: ${device1Mac}, 狀態: online`);
      console.log(`   本地 imageCode: ${deviceImageCodes[device1Mac] || 'N/A'}`);
      console.log(`2. 6吋 BW 設備 - MAC: ${device2Mac}, 狀態: online`);
      console.log(`   本地 imageCode: ${deviceImageCodes[device2Mac] || 'N/A'}`);
      console.log(`3. 3.7吋 BWRY 設備 - MAC: ${device3Mac}, 狀態: online`);
      console.log(`   本地 imageCode: ${deviceImageCodes[device3Mac] || 'N/A'}`);

      if (customDevices.length === 0) {
        console.log('尚未添加任何自定義設備');
      } else {
        console.log('自定義設備:');
        customDevices.forEach((device, index) => {
          console.log(`${index + 4}. MAC: ${device.macAddress}, 狀態: ${device.status}`);
          console.log(`   尺寸: ${device.data?.size || 'N/A'}, 本地 imageCode: ${deviceImageCodes[device.macAddress] || 'N/A'}, colorType: ${device.data?.colorType || 'N/A'}`);
        });
      }
    }

    // 移除自定義設備
    function removeDevice(index) {
      const deviceIndex = parseInt(index) - 4; // 調整索引（因為有3個預設設備，索引從4開始）
      if (isNaN(deviceIndex) || deviceIndex < 0 || deviceIndex >= customDevices.length) {
        console.log('錯誤: 無效的設備序號');
        listDevices(); // 顯示正確的設備列表供參考
        return;
      }

      const removedDevice = customDevices.splice(deviceIndex, 1)[0];
      console.log(`已移除設備: MAC=${removedDevice.macAddress}`);
    }

    // 請求設備圖片預覽
    function requestDeviceImage(macAddress) {
      if (!macAddress || !macAddress.match(/^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$/)) {
        console.log('錯誤: MAC 地址格式不正確，請使用格式 AA:BB:CC:DD:EE:FF');
        return;
      }

      // 發送請求圖片預覽的消息
      const requestImageMessage = {
        type: 'requestPreviewImage',
        macAddress: macAddress,
        timestamp: Date.now()
      };

      const requestImageMessageStr = JSON.stringify(requestImageMessage);
      console.log('\n===== 發送消息 =====');
      console.log(`類型: 圖片預覽請求`);
      console.log('內容:');
      console.log(JSON.stringify(requestImageMessage, null, 2));
      console.log('JSON格式:');
      console.log(requestImageMessageStr);
      console.log('=====================\n');

      ws.send(requestImageMessageStr);
    }

    // 顯示初始幫助信息
    console.log('\n連接成功！您可以輸入命令來管理模擬設備。');
    showHelp();

    // 監聽用戶輸入
    rl.on('line', async (input) => {
      const command = input.trim();

      if (command === 'q') {
        console.log('正在關閉連接並退出...');
        clearIntervals();
        ws.close();
        setTimeout(() => process.exit(0), 1000);
      } else if (command === 'help') {
        showHelp();
      } else if (command === 'add') {
        await addCustomDevice();
      } else if (command === 'list') {
        listDevices();
      } else if (command.startsWith('remove ')) {
        const index = command.split(' ')[1];
        removeDevice(index);
      } else if (command.startsWith('request-image ')) {
        const macAddress = command.split(' ')[1];
        requestDeviceImage(macAddress);
      } else if (command) {
        console.log('未知命令。輸入 help 顯示可用命令。');
      }
    });

    // 每 5 秒發送一次設備狀態消息
    deviceStatusInterval = setInterval(() => {
      // 設備1: 2.9吋 BWR
      const device1 = {
        macAddress: device1Mac,
        status: 'online',
        // 注意：不包含 dataId，因為這是由前端或API控制的欄位，不是由裝置自己回報的欄位
        data: {
          size: '2.9"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          colorType: 'BWR'
        }
      };

      // 設備2: 6吋 BW
      const device2 = {
        macAddress: device2Mac,
        status: 'online',
        // 注意：不包含 dataId，因為這是由前端或API控制的欄位，不是由裝置自己回報的欄位
        data: {
          size: '6"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          colorType: 'BW'
        }
      };

      // 設備3: 3.7吋 BWRY
      const device3 = {
        macAddress: device3Mac,
        status: 'online',
        // 注意：不包含 dataId，因為這是由前端或API控制的欄位，不是由裝置自己回報的欄位
        data: {
          size: '3.7"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          colorType: 'BWRY'
        }
      };

      // 如果有本地存儲的 imageCode，添加到設備數據中
      [device1, device2, device3].forEach(device => {
        if (deviceImageCodes[device.macAddress]) {
          device.data.imageCode = deviceImageCodes[device.macAddress];
        }
      });

      // 處理自定義設備，添加本地存儲的 imageCode
      const processedCustomDevices = customDevices.map(device => {
        const processedDevice = { ...device };
        if (deviceImageCodes[device.macAddress]) {
          processedDevice.data = {
            ...processedDevice.data,
            imageCode: deviceImageCodes[device.macAddress]
          };
        }
        return processedDevice;
      });

      // 合併預設設備和處理後的自定義設備
      const allDevices = [device1, device2, device3, ...processedCustomDevices];

      const deviceStatusMessage = {
        type: 'deviceStatus',
        devices: allDevices
      };

      const deviceStatusMessageStr = JSON.stringify(deviceStatusMessage);
      console.log('\n===== 發送消息 =====');
      console.log(`類型: 設備狀態消息 (共 ${allDevices.length} 個設備)`);
      console.log('內容:');
      console.log(JSON.stringify(deviceStatusMessage, null, 2));
      console.log('JSON格式:');
      console.log(deviceStatusMessageStr);
      console.log('=====================\n');

      // 如果有自定義設備，顯示詳細信息
      if (customDevices.length > 0) {
        console.log('設備列表:');
        allDevices.forEach((device, index) => {
          console.log(`  ${index + 1}. MAC: ${device.macAddress}, 狀態: ${device.status}`);
          console.log(`     尺寸: ${device.data?.size || 'N/A'}, 回報 imageCode: ${device.data?.imageCode || 'N/A'}, colorType: ${device.data?.colorType || 'N/A'}`);
        });
      }

      ws.send(deviceStatusMessageStr);
    }, 5000);

    // 每 30 秒重新發送一次網關信息消息
    gatewayInfoTimeout = setInterval(() => {
      const gatewayInfoMessage = {
        type: 'gatewayInfo',
        info: {
          macAddress: wsInfo.macAddress || 'AA:BB:CC:DD:EE:FF',
          model: wsInfo.model || 'Gateway Model 003',
          wifiFirmwareVersion: wsInfo.wifiFirmwareVersion || '1.0.0',
          btFirmwareVersion: wsInfo.btFirmwareVersion || '2.0.0',
          ipAddress: wsInfo.ipAddress || '*************'
        }
      };

      const gatewayInfoMessageStr = JSON.stringify(gatewayInfoMessage);
      console.log('\n===== 發送消息 =====');
      console.log('類型: 網關信息消息 (定期更新)');
      console.log('內容:');
      console.log(JSON.stringify(gatewayInfoMessage, null, 2));
      console.log('JSON格式:');
      console.log(gatewayInfoMessageStr);
      console.log('=====================\n');

      ws.send(gatewayInfoMessageStr);
    }, 30000);
  });

  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data);

      console.log('\n===== 收到消息 =====');
      console.log('原始數據長度:', data.length, '字節');
      console.log('消息類型:', message.type || '未知類型');

      // 格式化顯示消息內容
      console.log('消息內容:');
      console.log(JSON.stringify(message, null, 2));

      // 特別處理歡迎消息
      if (message.type === 'welcome') {
        console.log('說明: 收到歡迎消息，連接成功建立');

        // 收到welcome消息後發送網關信息
        const gatewayInfoMessage = {
          type: 'gatewayInfo',
          info: {
            macAddress: wsInfo.macAddress || 'AA:BB:CC:DD:EE:FF',
            model: wsInfo.model || 'Gateway Model 003',
            wifiFirmwareVersion: wsInfo.wifiFirmwareVersion || '1.0.0',
            btFirmwareVersion: wsInfo.btFirmwareVersion || '2.0.0',
            ipAddress: wsInfo.ipAddress || '*************'
          }
        };

        const gatewayInfoMessageStr = JSON.stringify(gatewayInfoMessage);
        console.log('\n===== 發送消息 =====');
        console.log('類型: 網關信息消息 (收到welcome後發送)');
        console.log('內容:');
        console.log(JSON.stringify(gatewayInfoMessage, null, 2));
        console.log('JSON格式:');
        console.log(gatewayInfoMessageStr);
        console.log('=====================\n');

        ws.send(gatewayInfoMessageStr);
      }

      // 特別處理確認消息
      if (message.type === 'pong') {
        console.log('說明: 收到服務器 pong 回應');
      }

      // 特別處理網關信息確認消息
      if (message.type === 'gatewayInfoAck') {
        if (message.success === false && message.fatal === true) {
          console.error('嚴重錯誤: 服務器因安全問題強制中斷連線');
          console.error('錯誤原因:', message.message);
          console.error('連線將被終止，請檢查網關配置');
        } else if (message.success === false) {
          console.warn('網關信息更新失敗:', message.message);
        } else {
          console.log('網關信息更新成功');
        }
      }

      // 處理圖像更新消息
      if (message.type === 'update_preview') {
        console.log('說明: 收到圖像更新消息');

        const deviceMac = message.deviceMac;
        const imageCode = message.imageCode;
        const imageData = message.imageData;

        if (deviceMac && imageCode) {
          // 更新本地存儲的 imageCode
          deviceImageCodes[deviceMac] = imageCode;
          console.log(`已更新設備 ${deviceMac} 的本地 imageCode 為: ${imageCode}`);

          // 保存圖像數據（如果有）
          if (imageData && typeof imageData === 'string') {
            const imageDataLength = imageData.length;
            console.log(`圖像數據長度: ${imageDataLength} 字符`);

            if (imageData.startsWith('data:image')) {
              console.log('圖像格式: Data URL (base64)');
              console.log('圖像數據將保存到 saved_images 目錄');
              saveBase64Image(imageData, deviceMac);
            } else {
              console.log('圖像格式: 未知格式，無法處理');
            }
          }
        }
      }

      // 處理圖像數據（向後兼容）
      if (message.hasOwnProperty('imageData') && message.type !== 'update_preview') {
        console.log('說明: 檢測到消息中包含 imageData 欄位');

        // 檢查 imageData 格式
        if (typeof message.imageData === 'string') {
          const imageDataLength = message.imageData.length;
          console.log(`圖像數據長度: ${imageDataLength} 字符`);

          if (message.imageData.startsWith('data:image')) {
            console.log('圖像格式: Data URL (base64)');
            console.log('圖像數據將保存到 saved_images 目錄');
            saveBase64Image(message.imageData, message.deviceMac || 'unknown', message.imageCode);
          } else {
            console.log('圖像格式: 未知格式，無法處理');
          }
        } else {
          console.log('圖像數據類型不是字符串，無法處理');
        }
      }

      // 處理原始數據 (rawdata)
      if (message.hasOwnProperty('rawdata')) {
        console.log('說明: 檢測到消息中包含 rawdata 欄位');

        try {
          saveRawData(message.rawdata, message.deviceMac || 'unknown', message.imageCode);
        } catch (err) {
          console.error('處理 rawdata 時出錯:', err.message);
        }
      }

      console.log('=====================\n');
    } catch (error) {
      console.error('\n===== 收到消息處理錯誤 =====');
      console.error('錯誤:', error.message);
      console.error('原始數據:', data.toString().substring(0, 100) + (data.toString().length > 100 ? '...' : ''));
      console.error('================================\n');
    }
  });

  // 檢查 base64 字符串是否有效
  function isValidBase64(base64Str) {
    const base64Regex = /^[A-Za-z0-9+/]+={0,2}$/;
    return base64Regex.test(base64Str) && base64Str.length % 4 === 0;
  }

  // 保存 Base64 編碼的圖像
  function saveBase64Image(base64Data, deviceMac, imageCode = null) {
    try {
      console.log(`準備處理 imageData 欄位的數據並保存為圖片，裝置 MAC: ${deviceMac}`);

      // 如果有 imageCode，記錄關聯
      if (imageCode) {
        console.log(`關聯的 imageCode: ${imageCode}`);
      }

      // 從 Data URL 中提取 base64 數據部分
      const matches = base64Data.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);

      if (!matches || matches.length !== 3) {
        console.log('無效的 data URL 格式');
        return;
      }

      // 提取 MIME 類型和 base64 數據
      const mimeType = matches[1];
      const base64Buffer = matches[2];
      const extension = mimeType.split('/')[1] || 'png';

      // 檢查提取出的 base64 字符串是否有效
      if (!isValidBase64(base64Buffer.replace(/\s/g, ''))) {
        console.warn('從 Data URL 提取的 base64 字符串無效，跳過保存');
        return;
      }

      // 創建圖像數據的 buffer
      const imageBuffer = Buffer.from(base64Buffer, 'base64');

      // 建立保存圖像的目錄
      const saveDir = path.join(__dirname, 'saved_images');
      if (!fs.existsSync(saveDir)) {
        fs.mkdirSync(saveDir, { recursive: true });
      }

      // 創建文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `preview_${deviceMac.replace(/:/g, '')}_${timestamp}.${extension}`;
      const filePath = path.join(saveDir, fileName);

      // 寫入文件
      fs.writeFileSync(filePath, imageBuffer);

      console.log(`已成功將圖像保存到: ${filePath}`);
    } catch (err) {
      console.error('保存圖像文件時出錯:', err.message);
    }
  }

  // 保存原始數據為 bin 檔案
  function saveRawData(rawdata, deviceMac = 'unknown', imageCode = null) {
    try {
      console.log(`準備保存原始數據為 bin 檔案，裝置 MAC: ${deviceMac}`);

      let rawBuffer;

      // 處理不同格式的 rawdata
      if (typeof rawdata === 'string') {
        // 如果是 base64 字符串，先解碼
        if (isValidBase64(rawdata.replace(/\s/g, ''))) {
          console.log('處理 base64 編碼的原始數據');
          rawBuffer = Buffer.from(rawdata, 'base64');
        } else {
          console.warn('rawdata 不是有效的 base64 字符串');
          return;
        }
      } else if (Buffer.isBuffer(rawdata)) {
        // 如果已經是 Buffer，直接使用
        rawBuffer = rawdata;
      } else if (Array.isArray(rawdata)) {
        // 如果是數組，轉換為 Buffer
        rawBuffer = Buffer.from(rawdata);
      } else {
        console.error('不支援的 rawdata 格式:', typeof rawdata);
        return;
      }

      // 檢查 buffer 是否有效
      if (!Buffer.isBuffer(rawBuffer) || rawBuffer.length === 0) {
        console.warn('無效的原始數據 buffer，跳過保存');
        return;
      }

      // 建立保存原始數據的目錄（與圖像保存在同一位置）
      const saveDir = path.join(__dirname, 'saved_images');
      if (!fs.existsSync(saveDir)) {
        fs.mkdirSync(saveDir, { recursive: true });
      }

      // 創建文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `rawdata_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
      const filePath = path.join(saveDir, fileName);

      // 寫入文件
      fs.writeFileSync(filePath, rawBuffer);

      console.log(`已成功將原始數據保存到: ${filePath}`);
      console.log(`原始數據大小: ${rawBuffer.length} 字節`);

      // 如果提供了 imageCode，記錄關聯
      if (imageCode) {
        console.log(`關聯的 imageCode: ${imageCode}`);
      }

      // 顯示原始數據的前幾個字節（用於調試）
      const previewBytes = rawBuffer.slice(0, Math.min(16, rawBuffer.length));
      console.log(`原始數據前 ${previewBytes.length} 字節 (hex): ${previewBytes.toString('hex')}`);

    } catch (err) {
      console.error('保存原始數據時出錯:', err.message);
    }
  }

  ws.on('error', (error) => {
    console.error('WebSocket 錯誤:', error);
    clearIntervals();
  });

  ws.on('close', (code, reason) => {
    console.log(`連接已關閉，代碼: ${code}，原因: ${reason}`);
    clearIntervals();

    // 詢問用戶是否要重連
    promptReconnect(wsInfo);
  });

  // 處理程序結束時，關閉 WebSocket 連接
  process.on('SIGINT', () => {
    console.log('關閉 WebSocket 連接...');
    clearIntervals();
    ws.close();
    rl.close();
    process.exit();
  });

  return ws;
}

// 提示用戶是否重新連接
async function promptReconnect(wsInfo) {
  const answer = await prompt('連接已斷開，是否嘗試重新連接? (y/n): ');
  if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
    console.log('嘗試重新連接...');
    connectWebSocket(wsInfo);
  } else {
    console.log('不再嘗試重新連接。程序將退出。');
    rl.close();
    process.exit();
  }
}

// 生成隨機MAC地址
function generateRandomMac() {
  const hexDigits = "0123456789ABCDEF";
  let mac = "";
  for (let i = 0; i < 6; i++) {
    let part = "";
    for (let j = 0; j < 2; j++) {
      part += hexDigits.charAt(Math.floor(Math.random() * 16));
    }
    mac += part;
    if (i < 5) mac += ":";
  }
  return mac;
}

// 主函數
async function main() {
  try {
    console.log('EPD 網關模擬器（從複製的WebSocket資訊啟動）');

    // 生成並顯示模擬MAC地址和網關信息
    const defaultMac = generateRandomMac();
    const defaultDeviceMac = generateRandomMac();
    const randomIp = `192.168.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}`;

    console.log('\n===== 模擬設備資訊 =====');
    console.log(`網關 MAC 地址: ${defaultMac}`);
    console.log(`網關 IP 地址: ${randomIp}`);
    console.log(`網關型號: Gateway Model XYZ`);
    console.log(`WiFi固件版本: 1.0.0`);
    console.log(`藍芽固件版本: 2.0.0`);
    console.log(`預設設備 MAC 地址: ${defaultDeviceMac}`);
    console.log('請先在server端新增這些設備，然後再繼續\n');
    console.log('===========================\n');

    console.log('請從網關管理頁面複製WebSocket登入資訊（點擊鑰匙圖標按鈕）');

    const wsInfoStr = await prompt('\n請貼上WebSocket登入資訊 (JSON格式): ');

    let wsInfo;
    try {
      wsInfo = JSON.parse(wsInfoStr);
    } catch (error) {
      throw new Error('無法解析WebSocket登入資訊，請確保格式正確的JSON');
    }

    // 驗證必要的字段
    if (!wsInfo.url || !wsInfo.token) {
      throw new Error('WebSocket登入資訊缺少必要的字段 (url, token)');
    }

    // 添加網關信息到wsInfo，使用一開始生成的 MAC 地址
    wsInfo.macAddress = defaultMac;
    wsInfo.defaultDeviceMac = defaultDeviceMac;
    wsInfo.ipAddress = randomIp;
    wsInfo.model = 'Gateway Model 003';
    wsInfo.wifiFirmwareVersion = '1.0.0';
    wsInfo.btFirmwareVersion = '2.0.0';
    wsInfo.name = `Gateway-${defaultMac.substring(9).replace(/:/g, '')}`;

    console.log('\n已解析WebSocket登入資訊:');
    console.log(`URL: ${wsInfo.url}`);
    console.log(`Token: ${wsInfo.token.substring(0, 10)}...`);
    console.log(`Protocol: ${wsInfo.protocol || 'json'}`);
    console.log(`網關 MAC: ${wsInfo.macAddress}`);
    console.log(`網關名稱: ${wsInfo.name}`);
    console.log(`網關 IP: ${wsInfo.ipAddress}`);
    console.log(`網關型號: ${wsInfo.model}`);
    console.log(`WiFi固件版本: ${wsInfo.wifiFirmwareVersion}`);
    console.log(`藍芽固件版本: ${wsInfo.btFirmwareVersion}`);
    console.log(`預設設備 MAC: ${wsInfo.defaultDeviceMac}`);

    // 顯示原始WebSocket登入資訊（單行格式）
    console.log('\n原始WebSocket登入資訊 (可複製):');
    console.log(JSON.stringify(wsInfo));

    // 連接到 WebSocket 服務器
    console.log('\n正在連接到 WebSocket 服務器...');
    connectWebSocket(wsInfo);

    console.log('\n使用說明:');
    console.log('1. 此測試客戶端會自動嘗試連接到WebSocket服務器');
    console.log('2. 會自動發送 ping、設備狀態和網關信息');
    console.log('3. 模擬三種不同型號的設備:');
    console.log('   - 2.9吋 BWR 設備');
    console.log('   - 6吋 BW 設備');
    console.log('   - 3.7吋 BWRY 設備');
    console.log('   - 每個設備使用固定的隨機MAC地址（連接時生成）');
    console.log('4. 設備回報格式符合最新 server 架構要求:');
    console.log('   - 設備回報不包含 dataId（由前端或API控制的欄位）');
    console.log('   - 設備回報不包含 imageCode（避免覆蓋資料庫值）');
    console.log('   - 當收到 server 圖像更新時，會自動更新本地 imageCode');
    console.log('   - 下次設備狀態回報時會包含更新後的 imageCode');
    console.log('5. 使用以下命令管理模擬設備:');
    console.log('   - help: 顯示命令幫助');
    console.log('   - add: 新增自定義設備');
    console.log('   - list: 列出所有模擬設備');
    console.log('   - remove <序號>: 移除指定序號的自定義設備');
    console.log('   - request-image <裝置MAC>: 請求特定裝置的預覽圖像');
    console.log('   - q: 退出程序');
    console.log('6. 如果連接斷開，系統會詢問您是否重連');
    console.log('7. 按 Ctrl+C 也可以終止程序');
    console.log('8. 收到的數據會自動保存到 saved_images 目錄:');
    console.log('   - 圖片數據 (imageData) 保存為 .png/.jpg 等圖片格式');
    console.log('   - 原始數據 (rawdata) 保存為 .bin 二進制檔案\n');

  } catch (error) {
    console.error('錯誤:', error.message);
    rl.close();
    process.exit(1);
  }
}

// 啟動程序
main().catch(error => {
  console.error('未處理的錯誤:', error);
  rl.close();
  process.exit(1);
});
