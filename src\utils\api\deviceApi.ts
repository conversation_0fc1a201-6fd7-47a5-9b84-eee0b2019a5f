import { Device } from '../../types/device';
import { buildEndpointUrl } from './apiConfig';
import { useAuthStore } from '../../store/authStore';

// 獲取所有設備
export async function getAllDevices(storeId?: string): Promise<Device[]> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建 URL，如果提供了 storeId，則添加到查詢參數中
    const url = storeId
      ? `${buildEndpointUrl('devices')}?storeId=${encodeURIComponent(storeId)}`
      : buildEndpointUrl('devices');

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('獲取設備列表失敗:', error);
    throw error;
  }
}

// 獲取單個設備詳情
export async function getDevice(id: string, storeId?: string): Promise<Device> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('devices', id);

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`獲取設備詳情失敗 (ID: ${id}):`, error);
    throw error;
  }
}

// 新增設備
export async function createDevice(device: Omit<Device, '_id' | 'createdAt' | 'updatedAt'>): Promise<Device> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('devices'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(device),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('新增設備失敗:', error);
    throw error;
  }
}

// 更新設備
export async function updateDevice(id: string, device: Partial<Device>, storeId?: string): Promise<Device> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('devices', id);

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(device),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`更新設備失敗 (ID: ${id}):`, error);
    throw error;
  }
}

// 刪除設備
export async function deleteDevice(id: string, storeId?: string): Promise<void> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('devices', id);

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
  } catch (error) {
    console.error(`刪除設備失敗 (ID: ${id}):`, error);
    throw error;
  }
}

// 同步設備狀態
export async function syncDevices(storeId?: string): Promise<boolean> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('devices', 'sync');

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return (await response.json()).success;
  } catch (error) {
    console.error('同步設備狀態失敗:', error);
    throw error;
  }
}

// 檢查 MAC 地址是否已存在
export async function checkMacAddressExists(macAddress: string, storeId?: string): Promise<boolean> {
  try {
    const devices = await getAllDevices(storeId);
    return devices.some(device => device.macAddress === macAddress);
  } catch (error) {
    console.error('檢查 MAC 地址失敗:', error);
    throw error;
  }
}

// 獲取設備事件歷史
export async function getDeviceEvents(
  deviceId: string,
  options?: {
    limit?: number;
    skip?: number;
    eventType?: string;
  }
): Promise<import('../../types/deviceEvent').DeviceEventsResponse> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('devices', `${deviceId}/events`);

    // 添加查詢參數
    const searchParams = new URLSearchParams();
    if (options?.limit) {
      searchParams.append('limit', options.limit.toString());
    }
    if (options?.skip) {
      searchParams.append('skip', options.skip.toString());
    }
    if (options?.eventType) {
      searchParams.append('eventType', options.eventType);
    }

    // 如果有查詢參數，添加到URL
    const queryString = searchParams.toString();
    if (queryString) {
      url = `${url}?${queryString}`;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();  } catch (error) {
    console.error(`獲取設備事件歷史失敗 (ID: ${deviceId}):`, error);
    throw error;
  }
}

// 設置主要網關
export async function setDevicePrimaryGateway(
  deviceId: string,
  gatewayId: string,
  storeId?: string
): Promise<Device> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('devices', `${deviceId}/set-primary-gateway`);

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify({ gatewayId }),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`設置主要網關失敗 (設備 ID: ${deviceId}, 網關 ID: ${gatewayId}):`, error);
    throw error;
  }
}

// 設置設備網關選擇模式
export async function setDeviceGatewayMode(
  deviceId: string,
  mode: 'auto' | 'manual',
  storeId?: string
): Promise<Device> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('devices', `${deviceId}/set-gateway-mode`);

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify({ mode }),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`設置設備網關選擇模式失敗 (設備 ID: ${deviceId}, 模式: ${mode}):`, error);
    throw error;
  }
}

// 從設備的其他網關列表中移除網關
export async function removeDeviceOtherGateway(
  deviceId: string,
  gatewayId: string,
  storeId?: string
): Promise<Device> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('devices', `${deviceId}/remove-other-gateway`);

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify({ gatewayId }),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`從設備的其他網關列表中移除網關失敗 (設備 ID: ${deviceId}, 網關 ID: ${gatewayId}):`, error);
    throw error;
  }
}

/**
 * 發送設備預覽圖到網關
 * @param deviceId 設備ID
 * @param options 選項
 * @returns Promise<object> 發送結果
 */
export async function sendDevicePreviewToGateway(
  deviceId: string,
  options: {
    sendToAllGateways?: boolean;
    storeId?: string;
  } = {}
): Promise<any> {
  try {
    const { token } = useAuthStore.getState();
    const { sendToAllGateways = false, storeId } = options;

    // 構建 URL
    let url = `${buildEndpointUrl('devices')}/${deviceId}/send-preview`;
    if (storeId) {
      url += `?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify({ sendToAllGateways }),
      credentials: 'include',
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      if (response.status === 403) {
        throw new Error('沒有權限操作此設備');
      }
      if (response.status === 404) {
        throw new Error('設備不存在');
      }
      const errorData = await response.json();
      throw new Error(errorData.error || `API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('發送預覽圖到網關失敗:', error);
    throw error;
  }
}

/**
 * 批量發送多個設備預覽圖到網關
 * @param deviceIds 設備ID陣列
 * @param options 選項
 * @returns Promise<object> 發送結果
 */
export async function sendMultipleDevicePreviewsToGateways(
  deviceIds: string[],
  options: {
    sendToAllGateways?: boolean;
    storeId?: string;
  } = {}
): Promise<any> {
  try {
    const { token } = useAuthStore.getState();
    const { sendToAllGateways = false, storeId } = options;

    // 構建 URL
    let url = `${buildEndpointUrl('devices')}/send-multiple-previews`;
    if (storeId) {
      url += `?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify({ deviceIds, sendToAllGateways }),
      credentials: 'include',
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      if (response.status === 403) {
        throw new Error('沒有權限操作部分設備');
      }
      if (response.status === 404) {
        throw new Error('部分設備不存在');
      }
      const errorData = await response.json();
      throw new Error(errorData.error || `API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('批量發送預覽圖到網關失敗:', error);
    throw error;
  }
}

/**
 * 更新設備數據綁定
 * @param deviceId 設備ID
 * @param bindingData 綁定數據
 * @param storeId 門店ID (可選)
 * @param options 選項 (可選)
 * @param options.sendToGateway 是否自動發送到網關 (默認為 true)
 * @returns Promise<Device> 更新後的設備
 */
export async function updateDeviceDataBindings(
  deviceId: string,
  bindingData: {
    templateId?: string;
    dataBindings?: Record<string, string> | string;
    dataId?: string;
  },
  storeId?: string,
  options?: {
    sendToGateway?: boolean;
  }
): Promise<Device> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('devices', `${deviceId}/update-data-bindings`);

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    // 準備請求數據，包括 sendToGateway 選項
    const requestData = {
      ...bindingData,
      // 如果明確設置為 false，則不發送到網關
      sendToGateway: options?.sendToGateway
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(requestData),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      if (response.status === 403) {
        throw new Error('沒有權限操作此設備');
      }
      if (response.status === 404) {
        throw new Error('設備不存在');
      }
      const errorData = await response.json();
      throw new Error(errorData.error || `API error: ${response.status}`);
    }

    const result = await response.json();
    return result.device;
  } catch (error) {
    console.error(`更新設備數據綁定失敗 (設備 ID: ${deviceId}):`, error);
    throw error;
  }
}
