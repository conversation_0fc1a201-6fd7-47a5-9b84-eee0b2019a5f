import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Building, Search, Plus, ChevronRight, Edit, Trash2, AlertCircle, LogIn } from 'lucide-react';
import { Store } from '../types/store';
import { getAllStores, deleteStore } from '../utils/api/storeApi';
import { AddStoreModal } from './AddStoreModal';
import { EditStoreModal } from './EditStoreModal';
import { useAuthStore } from '../store/authStore';

// 門店卡片組件
interface StoreCardProps {
  store: Store;
  onClick: () => void;
  onEdit: (e: React.MouseEvent) => void;
  onDelete: (e: React.MouseEvent) => void;
}

const StoreCard: React.FC<StoreCardProps> = ({ store, onClick, onEdit, onDelete }) => {
  return (
    <div
      className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow relative"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center cursor-pointer" onClick={onClick}>
          <Building className="text-blue-500 mr-3" size={24} />
          <h3 className="text-lg font-semibold text-gray-800">{store.name || '未命名門店'}</h3>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={onEdit}
            className="p-1 rounded-full hover:bg-gray-100"
            title="編輯門店"
          >
            <Edit size={18} className="text-gray-600" />
          </button>
          <button
            onClick={onDelete}
            className="p-1 rounded-full hover:bg-gray-100"
            title="刪除門店"
          >
            <Trash2 size={18} className="text-red-500" />
          </button>
          <button
            onClick={onClick}
            className="p-1 rounded-full hover:bg-gray-100"
            title="查看門店詳情"
          >
            <ChevronRight className="text-gray-400" size={20} />
          </button>
        </div>
      </div>
      <div className="text-sm text-gray-600 mb-2">
        <span className="font-medium">ID:</span> {store.id || '無ID'}
      </div>
      <div className="text-sm text-gray-600">
        <span className="font-medium">位置:</span> {store.address || '無地址'}
      </div>
    </div>
  );
};

interface StoreManagementPageProps {
  onSelectStore: (store: Store) => void;
}

export const StoreManagementPage: React.FC<StoreManagementPageProps> = ({ onSelectStore }) => {
  const { t } = useTranslation();
  const { isAuthenticated } = useAuthStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [stores, setStores] = useState<Store[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [storeToDelete, setStoreToDelete] = useState<Store | null>(null);

  // 獲取門店數據
  const fetchStores = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 只有在已登入狀態下才獲取門店數據
      if (isAuthenticated) {
        const data = await getAllStores();
        setStores(data);
      } else {
        // 未登入時清空門店列表
        setStores([]);
        setError('請先登入以查看門店數據');
      }
    } catch (err: any) {
      console.error('獲取門店數據失敗:', err);
      if (err.message === '未登入或登入已過期') {
        setError('請先登入以查看門店數據');
      } else {
        setError('獲取門店數據失敗，請重試');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // 初始加載和認證狀態變更時重新獲取數據
  useEffect(() => {
    fetchStores();
  }, [isAuthenticated]);

  // 處理搜索
  const filteredStores = stores.filter(store =>
    (store.name && store.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (store.id && store.id.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (store.address && store.address.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // 處理刪除門店
  const handleDeleteStore = async () => {
    if (!storeToDelete) return;

    try {
      await deleteStore(storeToDelete._id || storeToDelete.id);
      // 重新獲取門店列表
      fetchStores();
      // 關閉確認對話框
      setShowDeleteConfirm(false);
      setStoreToDelete(null);
    } catch (err) {
      console.error('刪除門店失敗:', err);
      setError('刪除門店失敗，請重試');
    }
  };

  // 處理編輯門店
  const handleEditStore = (store: Store) => {
    setSelectedStore(store);
    setShowEditModal(true);
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">

        {/* 錯誤提示 */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700 flex items-center">
            <AlertCircle className="mr-2" size={20} />
            <span>{error}</span>
          </div>
        )}

        {/* 未登入提示 */}
        {!isAuthenticated && (
          <div className="mb-6 p-6 bg-blue-50 border border-blue-200 rounded-lg text-center">
            <LogIn className="mx-auto mb-2 text-blue-500" size={32} />
            <h3 className="text-lg font-semibold text-blue-700 mb-2">請先登入</h3>
            <p className="text-blue-600 mb-4">您需要登入後才能查看和管理門店</p>
          </div>
        )}

        {/* 搜索欄 */}
        <div className="relative mb-6">
          <input
            type="text"
            placeholder={t('common.search')}
            className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            disabled={!isAuthenticated}
          />
          <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
        </div>

        {/* 新增門店按鈕 */}
        <div className="flex justify-end mb-4">
          <button
            className={`flex items-center px-4 py-2 rounded-md ${
              isAuthenticated
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
            onClick={() => isAuthenticated && setShowAddModal(true)}
            disabled={!isAuthenticated}
          >
            <Plus size={18} className="mr-1" />
            {t('storeManagement.addStore')}
          </button>
        </div>

        {/* 門店卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {isLoading ? (
            <div className="col-span-3 text-center py-8">載入中...</div>
          ) : isAuthenticated && filteredStores.length > 0 ? (
            filteredStores.map(store => (
              <StoreCard
                key={store._id || store.id}
                store={store}
                onClick={() => onSelectStore(store)}
                onEdit={(e) => {
                  e.stopPropagation();
                  handleEditStore(store);
                }}
                onDelete={(e) => {
                  e.stopPropagation();
                  setStoreToDelete(store);
                  setShowDeleteConfirm(true);
                }}
              />
            ))
          ) : (
            <div className="col-span-3 text-center py-8 text-gray-500">
              {!isAuthenticated
                ? '請先登入以查看門店數據'
                : searchTerm
                  ? '沒有符合搜索條件的門店'
                  : '尚未添加任何門店'
              }
            </div>
          )}
        </div>
      </div>

      {/* 新增門店模態窗口 */}
      <AddStoreModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={() => {
          fetchStores();
          setShowAddModal(false);
        }}
      />

      {/* 編輯門店模態窗口 */}
      {selectedStore && (
        <EditStoreModal
          isOpen={showEditModal}
          store={selectedStore}
          onClose={() => {
            setShowEditModal(false);
            setSelectedStore(null);
          }}
          onSuccess={() => {
            fetchStores();
            setShowEditModal(false);
            setSelectedStore(null);
          }}
        />
      )}

      {/* 刪除確認對話框 */}
      {showDeleteConfirm && storeToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-bold mb-4">確認刪除</h3>
            <p className="mb-6">
              您確定要刪除門店 "{storeToDelete.name || '未命名門店'}" 嗎？此操作無法撤銷。
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300"
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setStoreToDelete(null);
                }}
              >
                取消
              </button>
              <button
                className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                onClick={handleDeleteStore}
              >
                確認刪除
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
