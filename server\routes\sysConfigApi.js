const express = require('express');
const router = express.Router();
const { authenticate, checkPermission } = require('../middleware/auth');

// MongoDB 連接信息
const collectionName = 'sysConfigs';

// 使用共享的 MongoDB 連接
let getDbConnection = null;

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

// 獲取數據集合
const getCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection(collectionName);
  return { collection, client };
};

// 獲取配置
router.get('/configs/:key', authenticate, checkPermission(['system:view', 'store-data:view']), async (req, res) => {
  try {
    const { key } = req.params;
    console.log(`嘗試獲取配置：${key}`);

    const { collection } = await getCollection();
    console.log(`成功連接到 ${collectionName} 集合`);

    const config = await collection.findOne({ key });

    if (!config) {
      console.log(`找不到配置：${key}`);
      return res.status(404).json({ error: `找不到配置: ${key}` });
    }

    console.log(`成功獲取配置：${key}`, config.value);
    res.json(config.value);
  } catch (error) {
    console.error(`獲取配置失敗 (${req.params.key}):`, error);
    res.status(500).json({ error: '獲取配置失敗' });
  }
});

// 更新或創建配置
router.put('/configs/:key', authenticate, checkPermission(['system:update', 'store-data:update']), async (req, res) => {
  try {
    const { key } = req.params;
    const value = req.body;

    const { collection } = await getCollection();

    // 使用 upsert 操作 - 如果不存在則創建，存在則更新
    const result = await collection.updateOne(
      { key },
      { $set: { key, value, updatedAt: new Date() } },
      { upsert: true }
    );

    if (result.upsertedCount > 0) {
      res.status(201).json({ key, value, message: '配置已創建' });
    } else {
      res.status(200).json({ key, value, message: '配置已更新' });
    }
  } catch (error) {
    console.error(`更新配置失敗 (${req.params.key}):`, error);
    res.status(500).json({ error: '更新配置失敗' });
  }
});

// 刪除配置
router.delete('/configs/:key', authenticate, checkPermission(['system:delete']), async (req, res) => {
  try {
    const { key } = req.params;
    const { collection } = await getCollection();

    const result = await collection.deleteOne({ key });

    if (result.deletedCount === 0) {
      return res.status(404).json({ error: `找不到配置: ${key}` });
    }

    res.status(204).send();
  } catch (error) {
    console.error(`刪除配置失敗 (${req.params.key}):`, error);
    res.status(500).json({ error: '刪除配置失敗' });
  }
});

// 獲取所有配置
router.get('/configs', authenticate, checkPermission(['system:view', 'store-data:view']), async (req, res) => {
  try {
    const { collection } = await getCollection();
    const configs = await collection.find().toArray();

    // 轉換為方便使用的物件格式
    const configObj = {};
    configs.forEach(item => {
      configObj[item.key] = item.value;
    });

    res.json(configObj);
  } catch (error) {
    console.error('獲取所有配置失敗:', error);
    res.status(500).json({ error: '獲取所有配置失敗' });
  }
});

module.exports = { router, initDB };
