// 測試 WebSocket 設備狀態消息格式
const exampleDeviceStatusMessage = {
  type: 'deviceStatus',
  gatewayId: 'test_gateway_id',
  storeId: 'test_store_id',
  devices: [
    {
      macAddress: 'AA:BB:CC:DD:EE:FF',
      name: 'Test EPD Device',
      type: 'EPD',
      status: 'online',
      data: {
        battery: 85,
        rssi: -65,
        size: '10.3'
      }
    }
  ]
};

// 打印測試消息以供參考
console.log(JSON.stringify(exampleDeviceStatusMessage, null, 2));
