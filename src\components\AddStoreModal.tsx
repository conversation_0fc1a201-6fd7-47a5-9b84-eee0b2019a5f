import React, { useState } from 'react';
import { X, AlertCircle } from 'lucide-react';
import { createStore, checkStoreIdExists } from '../utils/api/storeApi';
import { useTranslation } from 'react-i18next';

interface AddStoreModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function AddStoreModal({ isOpen, onClose, onSuccess }: AddStoreModalProps) {
  const { t } = useTranslation();
  const initialFormData = {
    id: '',
    name: '',
    address: '',
    phone: '',
    managerId: '',
    status: 'active' as 'active' | 'inactive',
    importSystemData: true // 預設勾選導入系統數據
  };

  const [formData, setFormData] = useState(initialFormData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCheckingId, setIsCheckingId] = useState(false);
  const [idExists, setIdExists] = useState(false);

  // 當欄位值變更時
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // 清除該欄位的錯誤
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }

    // 如果是 ID 欄位，且值不為空，則檢查 ID 是否已存在
    if (field === 'id' && value && value.trim() !== '') {
      checkIdExistence(value);
    } else if (field === 'id') {
      // 如果 ID 欄位為空，重置檢查狀態
      setIdExists(false);
    }
  };

  // 處理勾選框變更
  const handleCheckboxChange = (field: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked
    }));
  };

  // 檢查 ID 是否已存在
  const checkIdExistence = async (id: string) => {
    try {
      setIsCheckingId(true);
      // 清除之前的錯誤
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.id;
        return newErrors;
      });

      const exists = await checkStoreIdExists(id);
      setIdExists(exists);

      if (exists) {
        setErrors(prev => ({
          ...prev,
          id: '門店 ID 已存在，請使用其他 ID'
        }));
      }
    } catch (error) {
      console.error('檢查 ID 失敗:', error);
      setErrors(prev => ({
        ...prev,
        id: '檢查 ID 時發生錯誤，請重試'
      }));
    } finally {
      setIsCheckingId(false);
    }
  };

  // 表單提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 驗證表單
    const newErrors: Record<string, string> = {};

    // 檢查必填欄位
    if (!formData.id || formData.id.trim() === '') {
      newErrors.id = 'ID 為必填欄位';
    }

    if (!formData.name || formData.name.trim() === '') {
      newErrors.name = '名稱為必填欄位';
    }

    // 如果有錯誤，顯示錯誤並停止提交
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // 提交前再次檢查 ID 是否已存在
    if (formData.id && formData.id.trim() !== '') {
      try {
        const exists = await checkStoreIdExists(formData.id);
        if (exists) {
          setIdExists(true);
          setErrors(prev => ({
            ...prev,
            id: '門店 ID 已存在，請使用其他 ID'
          }));
          return;
        }
      } catch (error) {
        console.error('提交前檢查 ID 失敗:', error);
      }
    }

    // 如果 ID 已存在，停止提交
    if (idExists) {
      setErrors(prev => ({
        ...prev,
        id: '門店 ID 已存在，請使用其他 ID'
      }));
      return;
    }

    try {
      setIsSubmitting(true);

      // 呼叫 API 創建門店，傳遞 importSystemData 參數
      await createStore({
        id: formData.id,
        name: formData.name,
        address: formData.address,
        phone: formData.phone,
        managerId: formData.managerId,
        status: formData.status,
        importSystemData: formData.importSystemData
      });

      // 重置表單並關閉模態窗
      setFormData(initialFormData);
      onSuccess();
    } catch (err: any) {
      console.error('創建門店失敗:', err);

      // 處理特定欄位錯誤
      if (err.field) {
        setErrors(prev => ({
          ...prev,
          [err.field]: err.message
        }));

        // 如果是 ID 重複錯誤，設置 idExists 狀態
        if (err.field === 'id' && err.code === 'DUPLICATE_STORE_ID') {
          setIdExists(true);
        }
      } else {
        // 一般錯誤
        setErrors({
          form: err.message || '創建門店失敗，請重試'
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // 如果模態窗口不開啟，不渲染任何內容
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
        {/* 標題欄 */}
        <div className="flex justify-between items-center border-b border-gray-200 px-6 py-4">
          <h2 className="text-xl font-bold text-gray-800">新增門店</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        {/* 表單 */}
        <form onSubmit={handleSubmit} className="px-6 py-4">
          {/* 一般錯誤信息 */}
          {errors.form && (
            <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700">
              {errors.form}
            </div>
          )}

          {/* ID 欄位 */}
          <div className="mb-4">
            <label
              htmlFor="store-id"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              門店 ID <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="text"
                id="store-id"
                value={formData.id}
                onChange={(e) => handleChange('id', e.target.value)}
                className={`w-full px-3 py-2 border ${
                  errors.id ? 'border-red-500' : idExists ? 'border-red-500' : formData.id && !isCheckingId && !idExists ? 'border-green-500' : 'border-gray-300'
                } rounded-md focus:outline-none focus:ring-2 ${
                  errors.id || idExists ? 'focus:ring-red-500' : formData.id && !isCheckingId && !idExists ? 'focus:ring-green-500' : 'focus:ring-blue-500'
                }`}
                placeholder="例如: TP001"
              />
              {isCheckingId && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin h-4 w-4 border-2 border-blue-500 rounded-full border-t-transparent"></div>
                </div>
              )}
              {!isCheckingId && formData.id && !errors.id && !idExists && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </div>
            {errors.id && (
              <div className="flex items-center mt-1">
                <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
                <p className="text-sm text-red-500">{errors.id}</p>
              </div>
            )}
            {!errors.id && idExists && (
              <div className="flex items-center mt-1">
                <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
                <p className="text-sm text-red-500">門店 ID 已存在，請使用其他 ID</p>
              </div>
            )}
            <p className="text-xs text-gray-500 mt-1">門店 ID 必須是唯一的識別碼，創建後不建議修改</p>
          </div>

          {/* 名稱欄位 */}
          <div className="mb-4">
            <label
              htmlFor="store-name"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              門店名稱 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="store-name"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className={`w-full px-3 py-2 border ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
              placeholder="例如: 台北總店"
            />
            {errors.name && (
              <div className="flex items-center mt-1">
                <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
                <p className="text-sm text-red-500">{errors.name}</p>
              </div>
            )}
          </div>

          {/* 地址欄位 */}
          <div className="mb-4">
            <label
              htmlFor="store-address"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              門店地址
            </label>
            <input
              type="text"
              id="store-address"
              value={formData.address}
              onChange={(e) => handleChange('address', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="例如: 台北市信義區101號"
            />
          </div>

          {/* 導入系統數據勾選框 */}
          <div className="mb-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="import-system-data"
                checked={formData.importSystemData}
                onChange={(e) => handleCheckboxChange('importSystemData', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label
                htmlFor="import-system-data"
                className="ml-2 block text-sm text-gray-700"
              >
                {t('stores.importSystemData')}
              </label>
            </div>
            <p className="text-xs text-gray-500 mt-1 ml-6">{t('stores.importSystemDataDescription')}</p>
          </div>

          {/* 按鈕 */}
          <div className="flex justify-end gap-3 mt-6 border-t border-gray-200 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
              disabled={isSubmitting}
            >
              取消
            </button>
            <button
              type="submit"
              className={`px-4 py-2 bg-blue-600 text-white rounded-md ${
                isSubmitting ? 'opacity-75 cursor-not-allowed' : 'hover:bg-blue-700'
              }`}
              disabled={isSubmitting}
            >
              {isSubmitting ? '提交中...' : '確認新增'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
