import { BindingInfo, DataField, DataFieldType, TemplateElement } from "../../types";
import { getSysConfig } from "../api/sysConfigApi";

/**
 * 資料綁定核心模組
 * 負責處理資料欄位綁定相關的核心邏輯
 */
export class DataBindingCore {
  private static instance: DataBindingCore;
  private maxBindingDataCount: number = 8; // 預設值
  private supportedElementTypes: string[] = ['text', 'multiline-text']; // 目前僅支援文字元件
  private supportedFieldTypes: string[] = [DataFieldType.TEXT, DataFieldType.NUMBER]; // 僅支援文字和數字類型
  private listeners: ((count: number) => void)[] = []; // 訂閱者列表
  private constructor() {
    // 使用預設值進行初始化
    // 後續會透過 initializeConfig 來載入實際配置
  }

  /**
   * 獲取 DataBindingCore 的單例實例
   */
  public static getInstance(): DataBindingCore {
    if (!DataBindingCore.instance) {
      DataBindingCore.instance = new DataBindingCore();
      // 非同步初始化配置
      DataBindingCore.instance.initializeConfig();
    }
    return DataBindingCore.instance;
  }

  /**
   * 初始化配置
   * 這個方法會非同步載入系統配置，但不會阻塞程式執行
   */
  private initializeConfig(): void {
    // 使用非同步方式載入配置，但不等待結果
    this.loadMaxBindingDataCount().catch(error => {
      console.error("初始化綁定核心配置失敗:", error);
    });
  }

  /**
   * 從系統配置讀取最大資料綁定數量
   */
  private async loadMaxBindingDataCount(): Promise<void> {
    try {
      const config = await getSysConfig();
      if (config && config.maxBindingDataCount !== undefined) {
        this.maxBindingDataCount = config.maxBindingDataCount;
      }
    } catch (error) {
      console.error("獲取最大資料綁定數量失敗:", error);
      // 失敗時保留預設值
    }
  }

  /**
   * 獲取最大資料綁定數量
   */
  public getMaxBindingDataCount(): number {
    return this.maxBindingDataCount;
  }
  /**
   * 設置最大資料綁定數量
   * @param count 新的最大資料綁定數量
   */
  public setMaxBindingDataCount(count: number): void {
    if (this.maxBindingDataCount !== count) {
      this.maxBindingDataCount = count;
      // 通知所有監聽者
      this.notifyMaxBindingDataCountChange();
    }
  }
  
  /**
   * 添加最大資料綁定數量變更監聽器
   * @param listener 監聽函數
   */
  public addMaxBindingDataCountListener(listener: (count: number) => void): void {
    this.listeners.push(listener);
  }
  
  /**
   * 移除最大資料綁定數量變更監聽器
   * @param listener 要移除的監聽函數
   */
  public removeMaxBindingDataCountListener(listener: (count: number) => void): void {
    this.listeners = this.listeners.filter(l => l !== listener);
  }
  
  /**
   * 通知所有監聽者最大綁定數量已變更
   */
  private notifyMaxBindingDataCountChange(): void {
    for (const listener of this.listeners) {
      listener(this.maxBindingDataCount);
    }
  }

  /**
   * 檢查元件是否支援資料綁定
   * @param elementType 元件類型
   * @returns 是否支援綁定
   */
  public isElementTypeSupported(elementType: string): boolean {
    return this.supportedElementTypes.includes(elementType);
  }

  /**
   * 檢查資料欄位類型是否支援綁定
   * @param fieldType 資料欄位類型
   * @returns 是否支援綁定
   */
  public isFieldTypeSupported(fieldType: string): boolean {
    return this.supportedFieldTypes.includes(fieldType);
  }

  /**
   * 根據類型過濾資料欄位
   * @param fields 所有資料欄位
   * @returns 可綁定的資料欄位列表
   */
  public getBindableFields(fields: DataField[]): DataField[] {
    return fields.filter(field => this.isFieldTypeSupported(field.type));
  }

  /**
   * 檢查資料索引是否有效
   * @param dataIndex 資料索引
   * @returns 是否有效
   */
  public isDataIndexValid(dataIndex: number): boolean {
    return dataIndex >= 0 && dataIndex < this.maxBindingDataCount;
  }

  /**
   * 建立綁定資訊
   * @param dataIndex 資料索引
   * @param fieldId 欄位ID
   * @param options 顯示選項
   * @returns 綁定資訊
   */
  public createBindingInfo(dataIndex: number, fieldId: string | null, options?: { showPrefix?: boolean }): BindingInfo | null {
    if (fieldId && !this.isDataIndexValid(dataIndex)) {
      console.error(`資料索引 ${dataIndex} 超出範圍 0-${this.maxBindingDataCount - 1}`);
      return null;
    }

    return {
      dataIndex,
      fieldId,
      displayOptions: options
    };
  }

  /**
   * 將元件綁定到資料欄位
   * @param element 元件
   * @param dataIndex 資料索引
   * @param fieldId 欄位ID
   * @param options 顯示選項
   * @returns 更新後的元件
   */
  public bindElementToField(
    element: TemplateElement,
    dataIndex: number,
    fieldId: string | null,
    options?: { showPrefix?: boolean }
  ): TemplateElement {
    if (!this.isElementTypeSupported(element.type)) {
      console.error(`元件類型 ${element.type} 不支援資料綁定`);
      return element;
    }

    const bindingInfo = this.createBindingInfo(dataIndex, fieldId, options);
    if (!bindingInfo) return element;

    return {
      ...element,
      dataBinding: bindingInfo
    };
  }

  /**
   * 解除元件的資料綁定
   * @param element 綁定的元件
   * @returns 解除綁定後的元件
   */
  public unbindElement(element: TemplateElement): TemplateElement {
    if (!element.dataBinding) return element;

    const { dataBinding, ...rest } = element;
    return rest as TemplateElement;
  }

  /**
   * 檢查元件是否已綁定資料
   * @param element 元件
   * @returns 是否已綁定資料
   */
  public isElementBound(element: TemplateElement): boolean {
    return !!element.dataBinding && !!element.dataBinding.fieldId;
  }

  /**
   * 更新元件綁定選項
   * @param element 元件
   * @param options 新的顯示選項
   * @returns 更新後的元件
   */
  public updateBindingOptions(
    element: TemplateElement,
    options: { showPrefix?: boolean }
  ): TemplateElement {
    if (!element.dataBinding) return element;

    return {
      ...element,
      dataBinding: {
        ...element.dataBinding,
        displayOptions: {
          ...element.dataBinding.displayOptions,
          ...options
        }
      }
    };
  }
}

// 導出綁定核心的單例實例
export const bindingCore = DataBindingCore.getInstance();
