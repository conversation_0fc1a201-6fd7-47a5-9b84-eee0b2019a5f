import { Store, StoreListResponse } from '../../types/store';
import { buildEndpointUrl } from './apiConfig';
import { useAuthStore } from '../../store/authStore';

// 獲取所有門店
export async function getAllStores(): Promise<Store[]> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('stores'), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }

    const data: StoreListResponse = await response.json();
    return data.stores || [];
  } catch (error) {
    console.error('獲取門店失敗:', error);
    throw error;
  }
}

// 獲取單個門店
export async function getStore(id: string): Promise<Store> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('stores', id), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`獲取門店 ${id} 失敗:`, error);
    throw error;
  }
}

// 檢查門店 ID 是否已存在
export async function checkStoreIdExists(id: string): Promise<boolean> {
  try {
    const stores = await getAllStores();
    return stores.some(store => store.id === id);
  } catch (error) {
    console.error('檢查門店 ID 失敗:', error);
    throw error;
  }
}

// 自定義錯誤類型
export interface ApiError extends Error {
  code?: string;
  field?: string;
  message: string;
  status?: number;
}

// 創建門店
export async function createStore(storeData: Omit<Store, '_id'> & { importSystemData?: boolean }): Promise<Store> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 從 storeData 中提取 importSystemData 參數，並從發送到後端的數據中移除
    const { importSystemData = true, ...storeDataToSend } = storeData;

    const response = await fetch(buildEndpointUrl('stores') + `?importSystemData=${importSystemData}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(storeDataToSend),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      const errorData = await response.json();
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }

      // 創建自定義錯誤對象
      const error = new Error(errorData.message || errorData.error || `API error: ${response.status}`) as ApiError;
      error.code = errorData.code;
      error.field = errorData.field;
      error.status = response.status;

      // 特別處理門店 ID 重複的情況
      if (errorData.code === 'DUPLICATE_STORE_ID') {
        error.message = `門店 ID "${storeData.id}" 已存在，請使用其他 ID`;
      }

      throw error;
    }

    return await response.json();
  } catch (error) {
    console.error('創建門店失敗:', error);
    throw error;
  }
}

// 更新門店
export async function updateStore(id: string, storeData: Partial<Store>): Promise<Store> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('stores', id), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(storeData),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      const errorData = await response.json();
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }

      // 創建自定義錯誤對象
      const error = new Error(errorData.message || errorData.error || `API error: ${response.status}`) as ApiError;
      error.code = errorData.code;
      error.field = errorData.field;
      error.status = response.status;

      // 特別處理門店 ID 重複的情況
      if (errorData.code === 'DUPLICATE_STORE_ID' && storeData.id) {
        error.message = `門店 ID "${storeData.id}" 已存在，請使用其他 ID`;
      }

      throw error;
    }

    return await response.json();
  } catch (error) {
    console.error('更新門店失敗:', error);
    throw error;
  }
}

// 刪除門店
export async function deleteStore(id: string): Promise<void> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('stores', id), {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
  } catch (error) {
    console.error('刪除門店失敗:', error);
    throw error;
  }
}
