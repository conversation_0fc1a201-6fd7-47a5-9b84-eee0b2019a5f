const express = require('express');
const router = express.Router();
const { ObjectId } = require('mongodb');
const { authenticate, checkPermission } = require('../middleware/auth');
const jwt = require('jsonwebtoken');
const { buildWebSocketUrl } = require('../utils/networkUtils');

// MongoDB 連接信息
const collectionName = 'gateways';

// 使用共享的 MongoDB 連接
let getDbConnection = null;

// JWT 密鑰
let jwtSecret = 'your_jwt_secret'; // 這裡應該從配置中獲取，暫時使用硬編碼

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

// 獲取網關詳細信息，包括其發現的設備
router.get('/gateways/:id/devices', authenticate, checkPermission(['gateway:view', 'device:view']), async (req, res) => {
  try {
    const { id } = req.params;
    const { collection } = await getCollection();
    const { db } = await getDbConnection();

    // 獲取網關信息
    const gateway = await collection.findOne({ _id: new ObjectId(id) });
    if (!gateway) {
      return res.status(404).json({ error: '網關不存在' });
    }

    // 獲取該網關發現的設備
    let devices = [];
    if (gateway.devices && gateway.devices.length > 0) {
      devices = await db.collection('devices').find({
        _id: { $in: gateway.devices.map(id => new ObjectId(id)) }
      }).toArray();
    }

    res.json({
      gateway,
      devices
    });
  } catch (error) {
    console.error('獲取網關設備失敗:', error);
    res.status(500).json({ error: '獲取網關設備失敗' });
  }
});

// 驗證 MAC 地址格式
function isValidMacAddress(mac) {
  // 允許 XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX 格式
  const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
  return macRegex.test(mac);
}

// 設置 JWT 密鑰
const setJwtSecret = (secret) => {
  jwtSecret = secret;
};

module.exports = { router, initDB, setJwtSecret };

// 獲取數據集合
const getCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection(collectionName);
  return { collection, client };
};

// 獲取所有網關
router.get('/gateways', authenticate, checkPermission(['gateway:view', 'device:view']), async (req, res) => {
  try {
    const { storeId } = req.query;
    const { collection } = await getCollection();

    // 構建查詢條件
    const query = {};

    // 如果提供了門店ID，則只返回該門店的網關
    if (storeId) {
      query.storeId = storeId;
    }

    const gateways = await collection.find(query).toArray();
    res.json(gateways);
  } catch (error) {
    console.error('獲取網關列表失敗:', error);
    res.status(500).json({ error: '獲取網關列表失敗' });
  }
});

// 獲取單個網關
router.get('/gateways/:id', authenticate, checkPermission(['gateway:view', 'device:view']), async (req, res) => {
  try {
    const { id } = req.params;
    const { storeId } = req.query;
    const { collection } = await getCollection();

    // 構建查詢條件
    const query = { _id: new ObjectId(id) };

    // 如果提供了門店ID，則確保網關屬於該門店
    if (storeId) {
      query.storeId = storeId;
    }

    const gateway = await collection.findOne(query);

    if (!gateway) {
      return res.status(404).json({ error: '網關不存在或不屬於指定門店' });
    }

    // 如果網關存在但沒有 WebSocket 信息，則生成並更新
    //if (!gateway.websocket) {
      // 生成網關的 JWT token
      const gatewayId = gateway._id.toString();
      const token = jwt.sign(
        {
          gatewayId: gatewayId,
          storeId: gateway.storeId,
          macAddress: gateway.macAddress, // 添加MAC地址到token中
          type: 'gateway'
        },
        jwtSecret,
        { expiresIn: '30d' } // 30天有效期
      );

      // 構建 WebSocket 路徑
      const wsPath = `/ws/store/${gateway.storeId}/gateway/${gatewayId}`;

      // 構建 WebSocket 連接信息，使用真實IP地址
      const wsInfo = {
        url: buildWebSocketUrl(wsPath),
        path: wsPath,
        token: token,
        protocol: 'json'
      };

      // 更新網關信息
      await collection.updateOne(
        { _id: gateway._id },
        { $set: { websocket: wsInfo } }
      );

      // 重新獲取更新後的網關信息
      gateway.websocket = wsInfo;
    //}

    res.json(gateway);
  } catch (error) {
    console.error('獲取網關失敗:', error);
    res.status(500).json({ error: '獲取網關失敗' });
  }
});

// 創建新網關
router.post('/gateways', authenticate, checkPermission(['gateway:create', 'device:create']), async (req, res) => {
  try {
    const gatewayData = req.body;
    const { collection } = await getCollection();

    // 驗證必要欄位
    if (!gatewayData.storeId) {
      return res.status(400).json({ error: '門店ID不能為空' });
    }

    // 驗證門店ID是否存在
    const { db } = await getDbConnection();
    const storeCollection = db.collection('stores');
    let store = await storeCollection.findOne({ id: gatewayData.storeId });

    // 如果通過 id 字段找不到，嘗試用 _id 查詢
    if (!store && ObjectId.isValid(gatewayData.storeId)) {
      store = await storeCollection.findOne({ _id: new ObjectId(gatewayData.storeId) });
    }

    if (!store) {
      return res.status(400).json({ error: `找不到ID為 "${gatewayData.storeId}" 的門店，請確認門店ID是否正確` });
    }

    // 確保 storeId 存儲的是門店的 id 字段值，而不是 _id
    gatewayData.storeId = store.id;

    // 驗證 MAC 地址格式
    if (!isValidMacAddress(gatewayData.macAddress)) {
      return res.status(400).json({ error: 'MAC 地址格式不正確，應為 XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX 格式' });
    }

    // 檢查 MAC 地址是否已存在
    const existingGateway = await collection.findOne({ macAddress: gatewayData.macAddress });
    if (existingGateway) {
      return res.status(400).json({ error: 'MAC 地址已存在' });
    }

    // 添加自動生成的欄位
    const now = new Date();
    const newGateway = {
      ...gatewayData,
      status: 'offline', // 初始狀態為離線
      devices: [], // 初始化設備列表為空
      createdAt: now,
      updatedAt: now
    };

    // 插入新網關
    const result = await collection.insertOne(newGateway);

    if (result.acknowledged) {
      // 生成網關的 JWT token
      const gatewayId = result.insertedId.toString();
      const token = jwt.sign(
        {
          gatewayId: gatewayId,
          storeId: newGateway.storeId,
          macAddress: newGateway.macAddress, // 添加MAC地址到token中
          type: 'gateway'
        },
        jwtSecret,
        { expiresIn: '30d' } // 30天有效期
      );

      // 構建 WebSocket 路徑
      const wsPath = `/ws/store/${newGateway.storeId}/gateway/${gatewayId}`;

      // 構建 WebSocket 連接信息，使用真實IP地址
      const wsInfo = {
        url: buildWebSocketUrl(wsPath),
        path: wsPath,
        token: token,
        protocol: 'json'
      };

      // 更新網關信息，添加 WebSocket 連接信息
      await collection.updateOne(
        { _id: result.insertedId },
        { $set: { websocket: wsInfo } }
      );

      // 獲取更新後的網關信息
      const updatedGateway = await collection.findOne({ _id: result.insertedId });

      // 返回插入的網關數據，包含MongoDB自動生成的_id和WebSocket連接信息
      res.status(201).json(updatedGateway);
    } else {
      throw new Error('網關創建失敗');
    }
  } catch (error) {
    console.error('創建網關失敗:', error);
    res.status(500).json({ error: '創建網關失敗' });
  }
});

// 更新網關
router.put('/gateways/:id', authenticate, checkPermission(['gateway:update', 'device:update']), async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const { storeId } = req.query;
    const { collection } = await getCollection();

    // 構建查詢條件
    const query = { _id: new ObjectId(id) };

    // 如果提供了門店ID，則確保網關屬於該門店
    if (storeId) {
      query.storeId = storeId;
    }

    // 檢查網關是否存在
    const existingGateway = await collection.findOne(query);
    if (!existingGateway) {
      return res.status(404).json({ error: '網關不存在或不屬於指定門店' });
    }

    // 不允許修改 storeId
    if (updateData.storeId && updateData.storeId !== existingGateway.storeId) {
      return res.status(400).json({ error: '不允許修改網關所屬的門店' });
    }

    // 如果 MAC 地址有變更，檢查是否與其他網關衝突
    if (updateData.macAddress && updateData.macAddress !== existingGateway.macAddress) {
      // 驗證 MAC 地址格式
      if (!isValidMacAddress(updateData.macAddress)) {
        return res.status(400).json({ error: 'MAC 地址格式不正確，應為 XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX 格式' });
      }

      const duplicateMac = await collection.findOne({
        macAddress: updateData.macAddress,
        _id: { $ne: new ObjectId(id) }
      });

      if (duplicateMac) {
        return res.status(400).json({ error: 'MAC 地址已被其他網關使用' });
      }
    }

    // 更新網關資料
    const result = await collection.updateOne(
      query,
      {
        $set: {
          ...updateData,
          updatedAt: new Date()
        }
      }
    );

    if (result.matchedCount === 0) {
      return res.status(404).json({ error: '網關不存在或不屬於指定門店' });
    }

    // 獲取更新後的網關資料
    const updatedGateway = await collection.findOne({ _id: new ObjectId(id) });
    res.json(updatedGateway);
  } catch (error) {
    console.error('更新網關失敗:', error);
    res.status(500).json({ error: '更新網關失敗' });
  }
});

// 刪除網關
router.delete('/gateways/:id', authenticate, checkPermission(['gateway:delete', 'device:delete']), async (req, res) => {
  try {
    const { id } = req.params;
    const { storeId } = req.query;
    const { collection } = await getCollection();

    // 構建查詢條件
    const query = { _id: new ObjectId(id) };

    // 如果提供了門店ID，則確保網關屬於該門店
    if (storeId) {
      query.storeId = storeId;
    }

    // 檢查網關是否存在
    const gateway = await collection.findOne(query);
    if (!gateway) {
      return res.status(404).json({ error: '網關不存在或不屬於指定門店' });
    }

    // 刪除網關
    const result = await collection.deleteOne(query);

    if (result.deletedCount === 0) {
      return res.status(404).json({ error: '網關不存在或不屬於指定門店' });
    }

    res.status(204).send();
  } catch (error) {
    console.error('刪除網關失敗:', error);
    res.status(500).json({ error: '刪除網關失敗' });
  }
});

// 同步網關狀態 - 獲取最新網關數據
router.post('/gateways/sync', authenticate, checkPermission(['gateway:update', 'device:update']), async (req, res) => {
  try {
    const { storeId } = req.query;
    const { collection } = await getCollection();

    // 構建查詢條件
    const query = {};

    // 如果提供了門店ID，則只同步該門店的網關
    if (storeId) {
      query.storeId = storeId;
    }

    // 查詢網關數據（僅獲取數據，不修改狀態）
    const gateways = await collection.find(query).toArray();

    res.json({
      success: true,
      gateways: gateways,
      count: gateways.length,
      message: storeId ? `已獲取門店 ${storeId} 的網關數據` : '已獲取所有網關數據'
    });
  } catch (error) {
    console.error('同步網關狀態失敗:', error);
    res.status(500).json({ error: '同步網關狀態失敗' });
  }
});

// 重啟網關
router.post('/gateways/:id/restart', authenticate, checkPermission(['gateway:update', 'device:update']), async (req, res) => {
  try {
    const { id } = req.params;
    const { storeId } = req.query;
    const { collection } = await getCollection();

    // 構建查詢條件
    const query = { _id: new ObjectId(id) };

    // 如果提供了門店ID，則確保網關屬於該門店
    if (storeId) {
      query.storeId = storeId;
    }

    // 檢查網關是否存在
    const gateway = await collection.findOne(query);
    if (!gateway) {
      return res.status(404).json({ error: '網關不存在或不屬於指定門店' });
    }

    // 模擬重啟操作
    await collection.updateOne(
      query,
      {
        $set: {
          status: 'offline',
          updatedAt: new Date()
        }
      }
    );

    // 延遲 2 秒後將狀態設為 online，模擬重啟過程
    setTimeout(async () => {
      try {
        await collection.updateOne(
          { _id: new ObjectId(id) },
          {
            $set: {
              status: 'online',
              lastSeen: new Date(),
              updatedAt: new Date()
            }
          }
        );
        console.log(`網關 ${id} 重啟完成`);
      } catch (error) {
        console.error(`網關 ${id} 重啟後狀態更新失敗:`, error);
      }
    }, 2000);

    res.json({ success: true, message: '網關重啟指令已發送' });
  } catch (error) {
    console.error('重啟網關失敗:', error);
    res.status(500).json({ error: '重啟網關失敗' });
  }
});

// 升級 WiFi 固件
router.post('/gateways/upgrade-wifi', authenticate, checkPermission(['gateway:update', 'device:update']), async (req, res) => {
  try {
    const { ids, version } = req.body;
    const { storeId } = req.query;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ error: '請提供有效的網關 ID 列表' });
    }

    if (!version) {
      return res.status(400).json({ error: '請提供固件版本' });
    }

    const { collection } = await getCollection();

    // 構建查詢條件
    const query = { _id: { $in: ids.map(id => new ObjectId(id)) } };

    // 如果提供了門店ID，則確保網關屬於該門店
    if (storeId) {
      query.storeId = storeId;
    }

    // 更新指定網關的 WiFi 固件版本
    const result = await collection.updateMany(
      query,
      {
        $set: {
          wifiFirmwareVersion: version,
          updatedAt: new Date()
        }
      }
    );

    res.json({
      success: true,
      message: `已成功升級 ${result.modifiedCount} 個網關的 WiFi 固件`
    });
  } catch (error) {
    console.error('升級 WiFi 固件失敗:', error);
    res.status(500).json({ error: '升級 WiFi 固件失敗' });
  }
});

// 升級藍芽固件
router.post('/gateways/upgrade-bt', authenticate, checkPermission(['gateway:update', 'device:update']), async (req, res) => {
  try {
    const { ids, version } = req.body;
    const { storeId } = req.query;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ error: '請提供有效的網關 ID 列表' });
    }

    if (!version) {
      return res.status(400).json({ error: '請提供固件版本' });
    }

    const { collection } = await getCollection();

    // 構建查詢條件
    const query = { _id: { $in: ids.map(id => new ObjectId(id)) } };

    // 如果提供了門店ID，則確保網關屬於該門店
    if (storeId) {
      query.storeId = storeId;
    }

    // 更新指定網關的藍芽固件版本
    const result = await collection.updateMany(
      query,
      {
        $set: {
          btFirmwareVersion: version,
          updatedAt: new Date()
        }
      }
    );

    res.json({
      success: true,
      message: `已成功升級 ${result.modifiedCount} 個網關的藍芽固件`
    });
  } catch (error) {
    console.error('升級藍芽固件失敗:', error);
    res.status(500).json({ error: '升級藍芽固件失敗' });
  }
});

// 驗證 MAC 地址格式
function isValidMacAddress(mac) {
  // 允許 XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX 格式
  const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
  return macRegex.test(mac);
}

module.exports = { router, initDB, setJwtSecret };
