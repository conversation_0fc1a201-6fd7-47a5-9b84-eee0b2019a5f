import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Search, Import, Upload, Lightbulb, Plus, Trash2, Grid, Link, Trash, AlertCircle, RefreshCw, Edit } from 'lucide-react';
import { StoreData, DataField, DataFieldSectionType } from '../types';
import { getAllStoreData, deleteStoreData, syncDataFieldsToStoreData } from '../utils/api/storeDataApi';
import { getAllDataFields } from '../utils/api/dataFieldApi';
import { saveFieldsViewConfig, getFieldsViewConfig } from '../utils/api/sysConfigApi';
import { getAllStores } from '../utils/api/storeApi';
import { AddStoreDataModal } from './AddStoreDataModal';
import { EditStoreDataModal } from './EditStoreDataModal';
import { useTranslation } from 'react-i18next';
import { Store } from '../types/store';

interface DatabasePageProps {
  store: Store | null;
}

export function DatabasePage({ store }: DatabasePageProps) {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [storeData, setStoreData] = useState<StoreData[]>([]);
  const [dataFields, setDataFields] = useState<DataField[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [syncingFields, setSyncingFields] = useState(false);
  const [notification, setNotification] = useState<{ message: string, type: 'success' | 'error' } | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showFieldManager, setShowFieldManager] = useState(false);
  const [visibleFields, setVisibleFields] = useState<Record<string, boolean>>({});

  // 新增：用於追蹤選中的項目
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // 欄位管理視窗的 ref
  const fieldManagerRef = useRef<HTMLDivElement>(null);
  const fieldManagerButtonRef = useRef<HTMLButtonElement>(null);

  // 編輯門店資料的狀態
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedStoreData, setSelectedStoreData] = useState<StoreData | null>(null);

  // 新增：過濾後的數據（搜索結果）
  const filteredStoreData = useMemo(() => {
    if (!searchTerm) return storeData;

    return storeData.filter(item => {
      // 在所有欄位中搜索
      return Object.entries(item).some(([key, value]) => {
        // 忽略 sn 欄位，只在其他欄位中搜索
        if (key === 'sn') return false;

        // 將值轉換為字符串後進行不區分大小寫的搜索
        return String(value).toLowerCase().includes(searchTerm.toLowerCase());
      });
    });
  }, [storeData, searchTerm]);

  // 點擊外部關閉欄位管理視窗，並自動保存設定
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        showFieldManager &&
        fieldManagerRef.current &&
        !fieldManagerRef.current.contains(event.target as Node) &&
        fieldManagerButtonRef.current &&
        !fieldManagerButtonRef.current.contains(event.target as Node)
      ) {
        // 關閉設定視窗前自動保存設定
        saveFieldsViewSettings(visibleFields); // 傳入當前最新的 visibleFields
        setShowFieldManager(false);
      }
    }

    // 添加事件監聽器
    document.addEventListener('mousedown', handleClickOutside);

    // 清理函數
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showFieldManager, visibleFields]); // 同時監聽 visibleFields 變更
    // 新增：監控選中項目的變化，當有項目取消勾選時自動取消全選
  useEffect(() => {
    // 判斷是否當前過濾結果中的所有項目都被選中
    if (filteredStoreData.length > 0 && filteredStoreData.every(item => item.uid !== undefined && selectedItems.includes(item.uid))) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  }, [selectedItems, filteredStoreData]);

  // 新增：搜索條件變更時的處理
  useEffect(() => {
    // 當搜索條件變更時，調整選中狀態，只保留在過濾結果中的選中項
    const filteredUids = filteredStoreData.map(item => item.uid).filter(uid => uid !== undefined);
    const newSelectedItems = selectedItems.filter(uid => filteredUids.includes(uid));

    // 如果選中項發生了變化，更新選中狀態
    if (newSelectedItems.length !== selectedItems.length) {
      setSelectedItems(newSelectedItems);
    }
  }, [searchTerm, filteredStoreData]);
    // 當門店變化時重新獲取數據
  useEffect(() => {
    if (store?.id) {
      fetchData();
    }
  }, [store?.id]);  // 保存欄位顯示設定
  const saveFieldsViewSettings = async (currentVisibleFields: Record<string, boolean> = visibleFields) => {
    try {
      // 將 Record<string, boolean> 格式轉換為欄位 ID 數組格式
      const visibleFieldsArray = Object.entries(currentVisibleFields)
        .filter(([_, isVisible]) => isVisible)
        .map(([fieldId]) => fieldId);

      console.log('正在保存欄位設定:', visibleFieldsArray);

      await saveFieldsViewConfig({
        visibleFields: visibleFieldsArray,
        columnOrder: [], // 目前未實現排序功能
        columnWidths: {} // 目前未實現寬度調整功能
      });

      console.log('欄位設定保存成功');
      showNotification('欄位顯示設定已保存', 'success');
    } catch (error) {
      console.error('保存欄位設定失敗:', error);
      showNotification('保存欄位設定失敗', 'error');
    }
  };

  // 檢查是否所有欄位都被選中
  const areAllFieldsSelected = () => {
    const selectableFields = dataFields.filter(field => field.id !== 'sn' && field.id !== 'id');
    return selectableFields.length > 0 &&
      selectableFields.every(field => visibleFields[field.id]);
  };

  // 計算是否有可顯示的浮動欄位（非 sn 和 id）
  const hasVisibleFloatingFields = Object.entries(visibleFields)
    .some(([fieldId, isVisible]) => isVisible && fieldId !== 'sn' && fieldId !== 'id');
    // 獲取門店資料和資料欄位定義
  const fetchData = async () => {
    if (!store?.id) {
      setError('未選擇門店');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // 同時獲取門店資料、資料欄位定義和已保存的欄位顯示設定
      const [storeItems, fieldItems, savedConfig] = await Promise.all([
        getAllStoreData(store.id), // 使用從props傳入的門店ID
        getAllDataFields(),
        getFieldsViewConfig().catch(err => {
          console.error('載入欄位視圖設定失敗:', err);
          return null; // 如果載入失敗，返回 null
        })
      ]);

      setStoreData(storeItems);

      // 只保留一般資料欄位
      const ordinaryFields = fieldItems.filter(field => field.section === DataFieldSectionType.ORDINARY);
      setDataFields(ordinaryFields);
        // 檢查是否有已保存的欄位顯示設定
      if (savedConfig && savedConfig.visibleFields !== undefined) {
        console.log('已載入保存的欄位設定:', savedConfig.visibleFields);

        // 將數組格式的 visibleFields 轉換為 Record<string, boolean> 格式
        // 即使 visibleFields 是空陣列也視為有效設定（表示全部取消選取）
        const savedFieldsVisibility = ordinaryFields.reduce((acc, field) => {
          // 檢查欄位 ID 是否在已保存的可見欄位列表中，
          // 也確保不包含 sn 和 id
          if (field.id !== 'sn' && field.id !== 'id') {
            acc[field.id] = savedConfig.visibleFields.includes(field.id);
          }
          return acc;
        }, {} as Record<string, boolean>);

        setVisibleFields(savedFieldsVisibility);
      } else {
        // 沒有已保存的設定，使用預設值（所有欄位都顯示，但排除 sn 和 id）
        console.log('未找到已保存設定，使用預設值');
        const initialVisibility = ordinaryFields.reduce((acc, field) => {
          if (field.id !== 'sn' && field.id !== 'id') {
            acc[field.id] = true;
          }
          return acc;
        }, {} as Record<string, boolean>);

        setVisibleFields(initialVisibility);
      }
    } catch (err: any) {
      console.error('獲取資料失敗:', err);
      setError(err.message || '獲取資料失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 處理欄位顯示切換
  const toggleFieldVisibility = (fieldId: string) => {
    setVisibleFields(prev => ({
      ...prev,
      [fieldId]: !prev[fieldId]
    }));
  };
    // 全選或取消全選所有欄位
  const toggleAllFields = () => {
    const allSelected = areAllFieldsSelected();
    const newVisibility = dataFields.reduce((acc, field) => {
      if (field.id !== 'sn' && field.id !== 'id') {
        acc[field.id] = !allSelected;
      }
      return acc;
    }, {} as Record<string, boolean>);
    setVisibleFields(newVisibility);
  };

  // 同步資料欄位到門店資料
  const syncFields = async () => {
    try {
      setSyncingFields(true);
      const result = await syncDataFieldsToStoreData();
      if (result) {
        showNotification('資料欄位已成功同步到門店資料', 'success');
        // 重新獲取資料
        fetchData();
      }
    } catch (err: any) {
      console.error('同步資料欄位失敗:', err);
      showNotification(err.message || '同步資料欄位失敗，請重試', 'error');
    } finally {
      setSyncingFields(false);
    }
  };

  // 處理單筆資料刪除
  const handleDelete = async (uid: string) => {
    if (!store?.id) {
      showNotification('未選擇門店', 'error');
      return;
    }

    if (window.confirm(t('common.confirm') + '?')) {
      try {
        // 使用當前選擇的門店 ID
        await deleteStoreData(uid, store.id);
        showNotification('刪除門店資料成功', 'success');
        // 更新本地數據
        setStoreData(prevData => prevData.filter(item => item.uid !== uid));
      } catch (err: any) {
        showNotification(err.message || '刪除門店資料失敗', 'error');
      }
    }
  };

  // 新增：處理批量刪除
  const handleBatchDelete = async () => {
    const count = selectedItems.length;
    if (count === 0) {
      showNotification('請先選擇要刪除的門店資料', 'error');
      return;
    }

    if (!store?.id) {
      showNotification('未選擇門店', 'error');
      return;
    }

    if (window.confirm(`${t('common.confirm')}${count}?`)) {
      try {
        let deletedCount = 0;
        let errorCount = 0;

        // 依序刪除所選門店資料
        for (const uid of selectedItems) {
          try {
            if (!uid) {
              console.error(`無效的資料ID: ${uid}`);
              errorCount++;
              continue;
            }

            // 使用當前選擇的門店 ID
            await deleteStoreData(uid, store.id);
            deletedCount++;
          } catch (error) {
            console.error(`刪除門店資料失敗 (ID: ${uid}):`, error);
            errorCount++;
          }
        }

        // 更新本地數據
        setStoreData(prevData => prevData.filter(item => !selectedItems.includes(item.uid)));

        // 清空選擇
        setSelectedItems([]);
        setSelectAll(false);

        // 顯示結果
        if (errorCount === 0) {
          showNotification(`已成功刪除 ${deletedCount} 筆門店資料`, 'success');
        } else {
          showNotification(`成功刪除 ${deletedCount} 筆，失敗 ${errorCount} 筆門店資料`, 'error');
        }
      } catch (err: any) {
        showNotification(err.message || '批量刪除門店資料失敗', 'error');
      }
    }
  };

  // 處理資料編輯
  const handleEdit = (item: StoreData) => {
    setSelectedStoreData(item);
    setShowEditModal(true);
  };

  // 顯示通知訊息
  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({ message, type });
    setTimeout(() => {
      setNotification(null);
    }, 3000);
  };
  return (
    <div className="p-8 lg:px-4 py-2">
      <div className="max-w-7xl mx-auto">
        {/* 通知消息 */}
        {notification && (
          <div
            className={`mb-4 p-4 rounded-md ${
              notification.type === 'success' ? 'bg-green-100 border-l-4 border-green-500 text-green-700' :
              'bg-red-100 border-l-4 border-red-500 text-red-700'
            } flex items-center justify-between`}
          >
            <div className="flex items-center">
              {notification.type === 'success' ? null : <AlertCircle className="w-5 h-5 mr-2" />}
              <span>{notification.message}</span>
            </div>
            <button
              className="text-gray-500 hover:text-gray-700"
              onClick={() => setNotification(null)}
            >
              &times;
            </button>
          </div>
        )}

        {/* 錯誤消息 */}
        {error && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded flex items-center">
            <AlertCircle className="w-5 h-5 mr-2" />
            <span>{error}</span>
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-700 hover:text-red-900"
            >
              &times;
            </button>
          </div>
        )}

        {/* Search and Actions Bar */}
        <div className="mb-6 flex items-center space-x-4">
          <div className="relative flex-1">
            <input
              type="text"
              placeholder={t('database.searchPlaceholder')}
              className="w-full pl-4 pr-10 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Search className="absolute right-3 top-2.5 text-gray-400 w-5 h-5" />
          </div>

          {/* 顯示當前門店名稱 */}
          <div className="px-4 py-2 bg-blue-100 text-blue-800 rounded-md border border-blue-300">
            <span className="font-medium">{store?.name || '未選擇門店'}</span>
            <span className="ml-2 text-blue-600">({store?.id || ''})</span>
          </div>

          <button className="flex items-center gap-2 px-4 py-2 bg-emerald-500 text-white rounded-md hover:bg-emerald-600">
            <Import className="w-5 h-5" />
            {t('database.import')}
          </button>

          <button className="flex items-center gap-2 px-4 py-2 bg-cyan-500 text-white rounded-md hover:bg-cyan-600">
            <Upload className="w-5 h-5" />
            {t('database.export')}
          </button>

          <button
            className={`flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md ${
              syncingFields ? 'opacity-75 cursor-not-allowed' : 'hover:bg-blue-600'
            }`}
            onClick={syncFields}
            disabled={syncingFields}
          >
            <RefreshCw className={`w-5 h-5 ${syncingFields ? 'animate-spin' : ''}`} />
            {t('database.syncFields')}
          </button>
            <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-violet-500 text-white rounded-md hover:bg-violet-600"
          >
            <Plus className="w-5 h-5" />
            {t('common.add')}
          </button>

          <button
            className="flex items-center gap-2 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
            onClick={handleBatchDelete}
          >
            <Trash2 className="w-5 h-5" />
            {t('common.delete')}
          </button>
            <div className="relative" ref={fieldManagerRef}>
            <button
              className="flex items-center gap-2 px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600"
              onClick={() => setShowFieldManager(!showFieldManager)}
              ref={fieldManagerButtonRef}
            >
              <Grid className="w-5 h-5" />
              {t('database.fieldManagement')}
            </button>            {showFieldManager && (
              <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg z-50 py-2 px-3 border border-gray-200">
                <div className="pb-2 border-b border-gray-200">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-sm text-gray-700">{t('database.displayFieldSettings')}</h3>
                    <div>
                      <label className="text-xs flex items-center text-gray-600 cursor-pointer">
                        <input
                          type="checkbox"
                          className="mr-1 rounded"
                          checked={areAllFieldsSelected()}
                          onChange={toggleAllFields}
                        />
                        {t('common.selectAll')}
                      </label>
                    </div>
                  </div>
                </div>
                <div className="pt-2 max-h-60 overflow-y-auto">
                  {dataFields
                    .filter(field => field.id !== 'sn' && field.id !== 'id')
                    .map(field => (
                      <label key={field.id} className="block py-1 px-2 text-sm text-gray-700 hover:bg-gray-100 rounded cursor-pointer">
                        <input
                          type="checkbox"
                          className="mr-2 rounded"
                          checked={visibleFields[field.id] || false}
                          onChange={() => toggleFieldVisibility(field.id)}
                        />
                        {field.name}
                      </label>
                    ))
                  }
                </div>                {/* 移除保存按鈕，改為設定視窗關閉時自動保存 */}
              </div>
            )}
          </div>
        </div>        {/* Table */}
        <div className="bg-white rounded-lg shadow relative">
          {/* 使用三個固定位置的表格實現左右固定，中間滾動的效果 */}
          <div className="relative overflow-x-auto" style={{ maxWidth: "100%" }}>
            <div className="flex relative">              {/* 左側固定欄位（勾選框和 SN） */}              <div className="sticky left-0 z-20 bg-white shadow-sm" style={hasVisibleFloatingFields ? { width: "230px" } : { width: "130px" }}>
                <table className="w-full border-separate border-spacing-0">                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 w-12 border-b border-gray-200 text-white">
                        <input
                          type="checkbox"
                          className="rounded"
                          checked={selectAll}
                          onChange={(e) => {
                            setSelectAll(e.target.checked);
                            setSelectedItems(e.target.checked ?
                              filteredStoreData
                                .filter(item => item.uid !== undefined)
                                .map(item => item.uid)
                              : []);
                          }}
                        />
                      </th>
                      <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 text-white">
                        {t('database.serialNumber')}
                      </th>
                      {hasVisibleFloatingFields && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 text-white">
                          {t('database.id')}
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={hasVisibleFloatingFields ? 3 : 2} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8">
                            <RefreshCw className="w-5 h-5 animate-spin mx-auto mb-2" />
                          </div>
                        </td>
                      </tr>
                    ) : filteredStoreData.length === 0 ? (
                      <tr>
                        <td colSpan={hasVisibleFloatingFields ? 3 : 2} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8">{t('database.noData')}</div>
                        </td>
                      </tr>                    ) : (                      filteredStoreData.map((item, index) => (
                        <tr key={`left-${item.sn}`} className={`h-[53px] ${index % 2 === 0 ? 'bg-amber-50' : 'bg-blue-50'}`}>
                          <td className={`px-4 py-3 border-b border-gray-200 align-middle`}>
                            <input
                              type="checkbox"
                              className="rounded"
                              checked={item.uid !== undefined && selectedItems.includes(item.uid)}
                              onChange={(e) => {
                                if (!item.uid) return;

                                if (e.target.checked) {
                                  setSelectedItems(prev => [...prev, item.uid]);
                                } else {
                                  setSelectedItems(prev => prev.filter(uid => uid !== item.uid));
                                }
                              }}
                            />
                          </td>                          <td className={`px-4 py-3 border-b border-gray-200 align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                            {/* 顯示基於索引的流水號，從1開始 */}
                            {filteredStoreData.indexOf(item) + 1}
                          </td>
                          {hasVisibleFloatingFields && (
                            <td className={`px-4 py-3 border-b border-gray-200 align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {item.id}
                            </td>
                          )}
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* 中間可滾動區域（動態欄位） */}
              <div className="flex-1 overflow-x-auto">
                <table className="w-full border-separate border-spacing-0">                  <thead>                    <tr>
                      {!hasVisibleFloatingFields ? (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('database.id')}
                        </th>
                      ) : (
                        dataFields
                          .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
                          .filter(field => field.id !== 'sn' && field.id !== 'id' && visibleFields[field.id])
                          .map(field => (
                            <th key={field.id} className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                              {field.name}
                            </th>
                          ))
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={!hasVisibleFloatingFields ? 1 : Object.values(visibleFields).filter(Boolean).length} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : filteredStoreData.length === 0 ? (
                      <tr>
                        <td colSpan={!hasVisibleFloatingFields ? 1 : Object.values(visibleFields).filter(Boolean).length} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8">
                            {t('database.noStoreData')}
                          </div>
                        </td>
                      </tr>                    ) : (                      filteredStoreData.map((item, index) => (
                        <tr key={`mid-${item.sn}`} className={`h-[53px] ${index % 2 === 0 ? 'bg-amber-50' : 'bg-blue-50'}`}>
                          {!hasVisibleFloatingFields ? (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {item.id}
                            </td>
                          ) : (
                            dataFields
                              .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
                              .filter(field => field.id !== 'sn' && field.id !== 'id' && visibleFields[field.id])
                              .map(field => (
                                <td key={field.id} className={`px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                                  {item[field.id] !== undefined ? String(item[field.id]) : ''}
                                </td>
                              ))
                          )}
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
                {/* 右側固定區域（操作欄） */}
              <div className="sticky right-0 z-20 bg-white shadow-sm" style={{ width: "100px" }}>                <table className="w-full border-separate border-spacing-0">                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap text-white">
                        {t('database.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : filteredStoreData.length === 0 ? (
                      <tr>
                        <td className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>                    ) : (                      filteredStoreData.map((item, index) => (
                        <tr key={`right-${item.sn}`} className={`h-[53px] align-middle ${index % 2 === 0 ? 'bg-amber-50' : 'bg-blue-50'}`}>
                          <td className="px-4 py-3 border-b border-gray-200 whitespace-nowrap">
                            <div className="flex items-center gap-2">
                              <button
                                className="text-gray-500 hover:text-blue-600"
                                onClick={() => handleEdit(item)}
                              >
                                <Edit className="w-5 h-5" />
                              </button>
                              <button
                                className="text-gray-500 hover:text-red-600"
                                onClick={() => handleDelete(item.uid)}
                              >
                                <Trash className="w-5 h-5" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        {/* Pagination */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              className="px-4 py-2 bg-violet-500 text-white rounded-md"
              onClick={() => setCurrentPage(1)}
            >
              1
            </button>
            <select
              className="border rounded-md px-2 py-1"
              value={itemsPerPage}
            >
              <option value="10">10 / {t('common.page')}</option>
            </select>
            <span>1 {t('common.pagesTotal')}</span>
          </div>
          <div className="flex items-center gap-2">
            <span>{t('common.goTo')}</span>
            <input
              type="number"
              className="w-20 border rounded-md px-2 py-1"
              min={1}
              max={1}
              value={currentPage}
              onChange={(e) => setCurrentPage(Number(e.target.value))}
            />
            <span>{t('common.page')}</span>
            <button className="px-4 py-1 border rounded-md hover:bg-gray-50">
              {t('common.confirm')}
            </button>
          </div>
        </div>
      </div>

      {/* 新增門店資料模態窗口 */}
      <AddStoreDataModal
        isOpen={showAddModal}
        dataFields={dataFields}
        storeId={store?.id || ''}
        onClose={() => setShowAddModal(false)}
        onSuccess={() => {
          // 重新加載門店資料
          fetchData();
          // 顯示成功訊息
          showNotification('門店資料新增成功', 'success');
        }}
      />      {/* 編輯門店資料模態窗口 */}
      <EditStoreDataModal
        isOpen={showEditModal}
        dataFields={dataFields}
        storeData={selectedStoreData}
        storeId={store?.id || ''}
        onClose={() => {
          setShowEditModal(false);
          setSelectedStoreData(null);
        }}
        onSuccess={() => {
          // 重新加載門店資料
          fetchData();
          // 顯示成功訊息
          showNotification('門店資料修改成功', 'success');
        }}
      />
    </div>
  );
}
