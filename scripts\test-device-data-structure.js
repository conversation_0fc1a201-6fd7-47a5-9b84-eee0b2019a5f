// 測試新的設備數據結構
const device = {
  _id: '123456',
  macAddress: '11:22:33:44:55:66',
  status: 'online',
  dataId: 'data123',
  storeId: 'store1',
  initialized: true,
  primaryGatewayId: '60d2f4a45d432a1f345678ab',
  otherGateways: ['60d2f4a45d432a1f345678cd'],
  userId: '609f1c9b5f8c443e9f7654ab',
  lastSeen: new Date(),
  note: '重要設備',
  data: {
    size: '10.3"',
    rssi: -75,
    battery: 85,
    imgcode: 'IMG12345'
  },
  createdAt: new Date(),
  updatedAt: new Date()
};

console.log('設備資料結構測試:');
console.log('-------------------');
console.log('設備 ID:', device._id);
console.log('MAC 地址:', device.macAddress);
console.log('狀態:', device.status);
console.log('數據 ID:', device.dataId);
console.log('門店 ID:', device.storeId);
console.log('初始化狀態:', device.initialized ? '已初始化' : '未初始化');
console.log('主要網關 ID:', device.primaryGatewayId);
console.log('其他網關數量:', device.otherGateways.length);
console.log('用戶 ID:', device.userId);
console.log('最後活動時間:', device.lastSeen);
console.log('備註:', device.note);
console.log('設備尺寸:', device.data.size);
console.log('信號強度:', device.data.rssi, 'dBm');
console.log('電池電量:', device.data.battery, '%');
console.log('圖片編碼:', device.data.imgcode);
console.log('-------------------');

// 測試渲染邏輯
function renderBatteryIndicator(battery) {
  if (battery === undefined) return '未知';
  return `電池: ${battery}%`;
}

function renderSignalStrength(rssi) {
  if (rssi === undefined) return '未知';
  
  let status = '弱';
  if (rssi > -70) status = '強';
  else if (rssi > -85) status = '中';
  
  return `信號: ${status} (${rssi} dBm)`;
}

console.log('渲染測試:');
console.log(renderBatteryIndicator(device.data.battery));
console.log(renderSignalStrength(device.data.rssi));

// 測試缺少數據的情況
const deviceWithoutData = {
  _id: '654321',
  macAddress: '66:55:44:33:22:11',
  status: 'offline',
  dataId: 'data456',
  storeId: 'store2',
  initialized: false,
  primaryGatewayId: null,
  otherGateways: [],
  userId: null,
  lastSeen: new Date(),
  note: '',
  data: {}  // 空的數據對象
};

console.log('\n缺少數據測試:');
console.log('MAC 地址:', deviceWithoutData.macAddress);
console.log('設備尺寸:', deviceWithoutData.data?.size || '未知');
console.log('信號強度:', renderSignalStrength(deviceWithoutData.data?.rssi));
console.log('電池電量:', renderBatteryIndicator(deviceWithoutData.data?.battery));
