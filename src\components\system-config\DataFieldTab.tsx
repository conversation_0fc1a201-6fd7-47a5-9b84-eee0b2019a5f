import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Plus, ChevronDown, ChevronRight, Trash2, MoveUp, MoveDown, Save, RefreshCw, AlertCircle } from 'lucide-react';
import {
  DataField,
  DataFieldSectionType,
  DataFieldType,
  DataFieldSyncStatus
} from '../../types';
import {
  getAllDataFields,
  createDataField,
  updateDataField,
  deleteDataField,
  updateDataFieldsOrder,
  syncDataFields,
  checkDuplicateField,
  ensureIdField
} from '../../utils/api/dataFieldApi';

export function DataFieldTab() {
  const { t } = useTranslation();
  const [expandedSection, setExpandedSection] = useState<DataFieldSectionType | ''>
    (DataFieldSectionType.ORDINARY);
  const [dataFields, setDataFields] = useState<DataField[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [syncStatus, setSyncStatus] = useState<DataFieldSyncStatus>({
    lastSynced: null,
    isModified: false
  });
  const [showNotification, setShowNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState('');
  const [fieldErrors, setFieldErrors] = useState<Record<string, { id?: string, name?: string }>>({});
  const [newFieldDraft, setNewFieldDraft] = useState<{[key: string]: Partial<DataField>}>({});

  // 區塊定義
  const sections = [
    { id: DataFieldSectionType.ORDINARY, title: t('systemConfig.ordinaryDataFields') },
    { id: DataFieldSectionType.ICON, title: t('systemConfig.iconFields') },
    { id: DataFieldSectionType.IMAGE, title: t('systemConfig.imageFields') },
    { id: DataFieldSectionType.VIDEO, title: t('systemConfig.videoFields') },
  ];

  // 資料類型選項
  const dataFieldTypes = Object.values(DataFieldType);

  // 載入資料欄位
  useEffect(() => {
    fetchDataFields();
  }, []);
  // 獲取所有資料欄位
  const fetchDataFields = async () => {
    try {
      setLoading(true);
      setError(null);

      // 獲取所有資料欄位
      let fields = await getAllDataFields();
      console.log('獲取到資料欄位:', fields.length);
      console.log('資料欄位詳情:', fields);

      // 檢查是否有預設的 id 欄位
      const hasDefaultIdField = fields.some(field => field.id === 'id');
      console.log('是否有預設 id 欄位:', hasDefaultIdField);

      // 每次載入時都檢查是否需要創建預設的 id 欄位
      if (fields.length === 0 || !hasDefaultIdField) {
        console.log('需要創建預設 id 欄位');
        // 使用原子操作確保 id 欄位存在
        const idField = await ensureIdField();
        console.log('確保 id 欄位存在:', idField);

        // 重新獲取所有欄位，確保包含新創建的 id 欄位
        fields = await getAllDataFields();
        console.log('重新獲取資料欄位後:', fields.length);
      }

      // 設置資料欄位到狀態
      setDataFields(fields);

      setSyncStatus({
        lastSynced: new Date(),
        isModified: false
      });
    } catch (err) {
      console.error('獲取資料欄位失敗:', err);
      setError('獲取資料欄位失敗，請重試或檢查服務器連接');
    } finally {
      setLoading(false);
    }
  };

  // 切換區塊展開狀態
  const toggleSection = (section: DataFieldSectionType) => {
    setExpandedSection(expandedSection === section ? '' : section);
  };

  // 添加新欄位草稿
  const addFieldDraft = (section: DataFieldSectionType) => {
    const sectionKey = section;
    setNewFieldDraft(prev => ({
      ...prev,
      [sectionKey]: {
        id: '',
        type: section === DataFieldSectionType.ORDINARY ? DataFieldType.TEXT : DataFieldType.NUMBER,
        name: '',
        prefix: '',
        section: section
      }
    }));
  };

  // 更新新欄位草稿
  const updateFieldDraft = (section: DataFieldSectionType, field: Partial<DataField>) => {
    const sectionKey = section;
    setNewFieldDraft(prev => ({
      ...prev,
      [sectionKey]: {
        ...prev[sectionKey],
        ...field
      }
    }));
  };

  // 檢查欄位重複 - 在輸入框失去焦點時呼叫
  const checkFieldDuplicate = async (sectionKey: string, field: Partial<DataField>) => {
    try {
      if (!field.id && !field.name) return;

      const result = await checkDuplicateField({
        id: field.id,
        name: field.name
      });

      const newErrors: { id?: string, name?: string } = {};
      if (field.id && result.isDuplicateId) {
        newErrors.id = 'ID 已存在，請使用其他 ID';
      }
      if (field.name && result.isDuplicateName) {      newErrors.name = '名稱已存在，請使用其他名稱';
      }

      setFieldErrors(prev => {
        const newFieldErrors = {...prev};
        if (Object.keys(newErrors).length > 0) {
          newFieldErrors[sectionKey] = newErrors;
        } else {
          delete newFieldErrors[sectionKey];
        }
        return newFieldErrors;
      });
    } catch (err) {
      console.error('檢查重複失敗:', err);
    }
  };

  // 在輸入完成後（失去焦點時）檢查重複性
  const handleFieldBlur = (sectionKey: string, field: Partial<DataField>) => {
    if ((field.id && field.id.trim() !== '') || (field.name && field.name.trim() !== '')) {
      checkFieldDuplicate(sectionKey, field);
    }
  };

  // 在已存在欄位失去焦點時檢查名稱
  const handleExistingFieldBlur = async (id: string, name: string) => {
    if (!name || name.trim() === '') return;

    try {
      const result = await checkDuplicateField({ name }, id);
      if (result.isDuplicateName) {
        setFieldErrors(prev => ({
          ...prev,
          [id]: { ...prev[id], name: '名稱已存在，請使用其他名稱' }
        }));
      } else {
        // 清除該字段的名稱錯誤
        setFieldErrors(prev => {
          const newErrors = { ...prev };
          if (newErrors[id]?.name) {
            delete newErrors[id].name;
            if (Object.keys(newErrors[id] || {}).length === 0) {
              delete newErrors[id];
            }
          }
          return newErrors;
        });
      }
    } catch (err) {
      console.error('檢查重複失敗:', err);
    }
  };

  // 取消新增欄位
  const cancelAddField = (section: DataFieldSectionType) => {
    const sectionKey = section;
    setNewFieldDraft(prev => {
      const newDraft = {...prev};
      delete newDraft[sectionKey];
      return newDraft;
    });

    setFieldErrors(prev => {
      const newErrors = {...prev};
      delete newErrors[sectionKey];
      return newErrors;
    });
  };
  // 確認新增欄位
  const confirmAddField = async (section: DataFieldSectionType) => {
    const sectionKey = section;
    const draft = newFieldDraft[sectionKey];

    if (!draft) return;

    // 檢查必填字段
    if (!draft.id || !draft.name) {
      setError('ID 和名稱為必填項');
      return;
    }

    // 檢查是否有重複錯誤
    if (fieldErrors[sectionKey]?.id || fieldErrors[sectionKey]?.name) {
      setError('請解決所有錯誤後再提交');
      return;
    }

    try {
      setLoading(true);

      // 獲取當前區塊的欄位
      const sectionFields = dataFields.filter(field => field.section === section)
        .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

      // 計算新欄位的 sortOrder，確保它排在最後面
      // 如果已有欄位，則取最後一個欄位的 sortOrder 加上 10；否則設為 0
      const lastSortOrder = sectionFields.length > 0 ? (sectionFields[sectionFields.length - 1].sortOrder || 0) : 0;
      const newSortOrder = lastSortOrder + 10;

      const createdField = await createDataField(draft as Omit<DataField, 'sortOrder'>);

      // 更新欄位排序
      await updateDataFieldsOrder([{
        id: createdField.id,
        sortOrder: newSortOrder
      }]);

      // 將新欄位添加到本地狀態，並包含 sortOrder
      setDataFields([...dataFields, {...createdField, sortOrder: newSortOrder}]);
      setSyncStatus(prev => ({...prev, isModified: true}));
      showNotificationMessage('新增欄位成功');

      // 清除草稿
      cancelAddField(section);
    } catch (err: any) {
      console.error('添加欄位失敗:', err);
      setError(err.message || '添加欄位失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 更新資料欄位
  const handleFieldChange = async (id: string, changes: Partial<DataField>) => {
    try {
      // 更新資料欄位
      await updateDataField(id, changes);

      // 更新本地狀態
      setDataFields(dataFields.map(field =>
        field.id === id ? { ...field, ...changes } : field
      ));

      setSyncStatus(prev => ({...prev, isModified: true}));
    } catch (err: any) {
      // 捕獲並顯示後端返回的錯誤
      console.error('更新欄位失敗:', err);
      setError(err.message || '更新欄位失敗，請重試');
    }
  };

  // 刪除資料欄位
  const handleDeleteField = async (id: string) => {
    try {
      // 發送刪除請求到後端
      await deleteDataField(id);

      // 更新本地狀態
      setDataFields(dataFields.filter(field => field.id !== id));
      setSyncStatus(prev => ({...prev, isModified: true}));
      showNotificationMessage('刪除欄位成功');
    } catch (err) {
      console.error('刪除欄位失敗:', err);
      setError('刪除欄位失敗，請重試');
    }
  };  // 移動欄位順序（上移）
  const handleMoveUp = async (index: number, section: DataFieldSectionType) => {
    const sectionFields = dataFields.filter(field => field.section === section)
      .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
    if (index <= 0) return;

    // 檢查是否為第二個項目且前一個是預設 id 欄位
    if (index === 1 && sectionFields[0]?.id === 'id' &&
        sectionFields[0]?.type === DataFieldType.UNIQUE_IDENTIFIER) return;

    // 先重設所有欄位的 sortOrder 確保它們是連續的
    const updatedFields = [...sectionFields].map((field, idx) => ({
      ...field,
      sortOrder: idx * 10 // 使用間隔為10的排序值，方便將來插入新欄位
    }));

    // 交換要移動的兩個欄位的順序
    const temp = updatedFields[index].sortOrder;
    updatedFields[index].sortOrder = updatedFields[index - 1].sortOrder;
    updatedFields[index - 1].sortOrder = temp;

    // 按照新的排序重新排列
    updatedFields.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

    try {
      // 準備所有需要更新的欄位順序
      const orderUpdates = updatedFields.map(field => ({
        id: field.id,
        sortOrder: field.sortOrder
      }));

      // 發送更新請求
      await updateDataFieldsOrder(orderUpdates);

      // 更新本地數據
      const newDataFields = dataFields.map(field => {
        const updatedField = updatedFields.find(uf => uf.id === field.id);
        return updatedField ? { ...field, sortOrder: updatedField.sortOrder } : field;
      });

      setDataFields(newDataFields);
      setSyncStatus(prev => ({...prev, isModified: true}));
    } catch (err) {
      console.error('更新欄位順序失敗:', err);
      setError('更新欄位順序失敗，請重試');
    }
  };
  // 移動欄位順序（下移）
  const handleMoveDown = async (index: number, section: DataFieldSectionType) => {
    const sectionFields = dataFields.filter(field => field.section === section)
      .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
    if (index >= sectionFields.length - 1) return;

    // 先重設所有欄位的 sortOrder 確保它們是連續的
    const updatedFields = [...sectionFields].map((field, idx) => ({
      ...field,
      sortOrder: idx * 10 // 使用間隔為10的排序值，方便將來插入新欄位
    }));

    // 交換要移動的兩個欄位的順序
    const temp = updatedFields[index].sortOrder;
    updatedFields[index].sortOrder = updatedFields[index + 1].sortOrder;
    updatedFields[index + 1].sortOrder = temp;

    // 按照新的排序重新排列
    updatedFields.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

    try {
      // 準備所有需要更新的欄位順序
      const orderUpdates = updatedFields.map(field => ({
        id: field.id,
        sortOrder: field.sortOrder
      }));

      // 發送更新請求
      await updateDataFieldsOrder(orderUpdates);

      // 更新本地數據
      const newDataFields = dataFields.map(field => {
        const updatedField = updatedFields.find(uf => uf.id === field.id);
        return updatedField ? { ...field, sortOrder: updatedField.sortOrder } : field;
      });

      setDataFields(newDataFields);
      setSyncStatus(prev => ({...prev, isModified: true}));
    } catch (err) {
      console.error('更新欄位順序失敗:', err);
      setError('更新欄位順序失敗，請重試');
    }
  };

  // 同步資料欄位到資料庫
  const handleSyncFields = async () => {
    try {
      setLoading(true);
      const result = await syncDataFields();
      if (result) {
        setSyncStatus({
          lastSynced: new Date(),
          isModified: false
        });
        showNotificationMessage('資料欄位已成功同步到資料庫');
      }
    } catch (err) {
      console.error('同步資料欄位失敗:', err);
      setError('同步資料欄位失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 顯示通知訊息
  const showNotificationMessage = (message: string) => {
    setNotificationMessage(message);
    setShowNotification(true);
    setTimeout(() => {
      setShowNotification(false);
    }, 3000);
  };

  // 當前區塊的資料欄位（按照 sortOrder 排序）
  const getSectionFields = (section: DataFieldSectionType) => {
    // 過濾出指定區塊的欄位（大小寫不敏感）
    const fields = dataFields.filter(field => {
      // 大小寫不敏感的比較
      if (typeof field.section === 'string' && typeof section === 'string') {
        return field.section.toLowerCase() === section.toLowerCase();
      }
      return field.section === section;
    });

    // 輸出調試信息
    console.log(`區塊 ${section} 的欄位:`, fields);

    // 按照 sortOrder 排序
    return fields.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
  };

  return (
    <div className="space-y-4 relative">
      {/* 通知消息 */}
      {showNotification && (
        <div className="fixed top-6 right-6 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md z-50 animate-fade-in-out">
          {notificationMessage}
        </div>
      )}

      {/* 錯誤消息 */}
      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded flex items-center">
          <AlertCircle className="w-5 h-5 mr-2" />
          <span>{error}</span>
          <button
            onClick={() => setError(null)}
            className="ml-auto text-red-700 hover:text-red-900"
          >
            ×
          </button>
        </div>
      )}

      {/* 同步狀態和操作按鈕 */}
      <div className="flex justify-between items-center mb-4">
        <div className="text-sm text-gray-600">
          {syncStatus.lastSynced ? (
            <>
              上次同步時間: {syncStatus.lastSynced.toLocaleString()}
              {syncStatus.isModified && (
                <span className="ml-2 text-yellow-600">(有未同步的更改)</span>
              )}
            </>
          ) : (
            <span>尚未同步</span>
          )}
        </div>
        <div className="flex gap-2">
          <button
            className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-1"
            onClick={() => fetchDataFields()}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            {t('common.reload')}
          </button>
          <button
            className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center gap-1"
            onClick={handleSyncFields}
            disabled={loading || !syncStatus.isModified}
          >
            <Save className="w-4 h-4" />
            {t('systemConfig.syncToDatabase')}
          </button>
        </div>
      </div>

      {/* 區塊列表 */}
      {sections.map((section) => {
        const sectionFields = getSectionFields(section.id as DataFieldSectionType);
        const sectionKey = section.id;
        const newFieldDraftData = newFieldDraft[sectionKey];
        const fieldError = fieldErrors[sectionKey];

        return (
          <div key={section.id} className="bg-gray-50 rounded-lg overflow-hidden">
            <button
              className="w-full flex items-center gap-2 px-4 py-3 text-blue-600 hover:bg-gray-100"
              onClick={() => toggleSection(section.id as DataFieldSectionType)}
            >
              {expandedSection === section.id ? (
                <ChevronDown className="w-5 h-5" />
              ) : (
                <ChevronRight className="w-5 h-5" />
              )}
              {section.title} ({sectionFields.length})
              <Plus
                className="w-5 h-5 ml-auto hover:text-blue-700"
                onClick={(e) => {
                  e.stopPropagation();
                  addFieldDraft(section.id as DataFieldSectionType);
                }}
              />
            </button>

            {expandedSection === section.id && (
              <div className="p-4">
                <div className="bg-white rounded-lg overflow-hidden">
                  {sectionFields.length === 0 && !newFieldDraftData ? (
                    <div className="p-4 text-center text-gray-500">
                      此區塊尚無資料欄位，請點擊 + 按鈕新增
                    </div>
                  ) : (
                    <table className="w-full">                      <thead>
                        <tr className="border-b">
                          <th className="bg-amber-600 px-4 py-2 text-left text-white">ID</th>
                          <th className="bg-amber-600 px-4 py-2 text-left text-white">{t('common.type')}</th>
                          <th className="bg-amber-600 px-4 py-2 text-left text-white">{t('common.name')}</th>
                          <th className="bg-amber-600 px-4 py-2 text-left text-white">{t('common.prefix')}</th>
                          <th className="bg-amber-600 px-4 py-2 text-left text-white">{t('common.actions')}</th>
                        </tr>
                      </thead>
                      <tbody>
                        {/* 新增欄位表單 */}
                        {newFieldDraftData && (
                          <tr key="new-field" className="border-b bg-blue-50">
                            <td className="px-4 py-2">
                              <input
                                type="text"
                                className={`w-full border ${fieldError?.id ? 'border-red-500' : 'border-gray-300'} rounded px-2 py-1`}
                                value={newFieldDraftData.id || ''}
                                onChange={(e) => updateFieldDraft(section.id as DataFieldSectionType, { id: e.target.value })}
                                onBlur={() => handleFieldBlur(sectionKey, { id: newFieldDraftData.id })}
                                placeholder="請輸入唯一 ID"
                              />
                              {fieldError?.id && (
                                <div className="text-red-500 text-xs mt-1">{fieldError.id}</div>
                              )}
                            </td>
                            <td className="px-4 py-2">                              <select
                                className="w-full border border-gray-300 rounded px-2 py-1"
                                value={newFieldDraftData.type || ''}
                                onChange={(e) => updateFieldDraft(section.id as DataFieldSectionType, { type: e.target.value })}
                              >
                                {dataFieldTypes
                                  .filter(type => type !== DataFieldType.UNIQUE_IDENTIFIER)
                                  .map(type => (
                                    <option key={type} value={type}>{type}</option>
                                ))}
                              </select>
                            </td>
                            <td className="px-4 py-2">
                              <input
                                type="text"
                                className={`w-full border ${fieldError?.name ? 'border-red-500' : 'border-gray-300'} rounded px-2 py-1`}
                                value={newFieldDraftData.name || ''}
                                onChange={(e) => updateFieldDraft(section.id as DataFieldSectionType, { name: e.target.value })}
                                onBlur={() => handleFieldBlur(sectionKey, { name: newFieldDraftData.name })}
                                placeholder="請輸入名稱"
                              />
                              {fieldError?.name && (
                                <div className="text-red-500 text-xs mt-1">{fieldError.name}</div>
                              )}
                            </td>
                            <td className="px-4 py-2">
                              <input
                                type="text"
                                className="w-full border border-gray-300 rounded px-2 py-1"
                                value={newFieldDraftData.prefix || ''}
                                onChange={(e) => updateFieldDraft(section.id as DataFieldSectionType, { prefix: e.target.value })}
                                placeholder="請輸入前綴"
                              />
                            </td>
                            <td className="px-4 py-2">
                              <div className="flex items-center gap-2">
                                <button
                                  className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 text-sm"
                                  onClick={() => confirmAddField(section.id as DataFieldSectionType)}
                                  disabled={loading || !newFieldDraftData.id || !newFieldDraftData.name}
                                >
                                  {t('common.confirm')}
                                </button>
                                <button
                                  className="px-3 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 text-sm"
                                  onClick={() => cancelAddField(section.id as DataFieldSectionType)}
                                >
                                  {t('common.cancel')}
                                </button>
                              </div>
                            </td>
                          </tr>
                        )}
                          {/* 既有資料欄位 */}                        {sectionFields.map((field, index) => {
                          const fieldError = fieldErrors[field.id];
                          const isDefaultIdField = field.id === 'id' && field.type === DataFieldType.UNIQUE_IDENTIFIER;

                          return (
                            <tr key={field.id} className={`border-b ${isDefaultIdField ? 'bg-gray-50' : index % 2 === 0 ? 'bg-amber-40' : 'bg-green-50'}`}>
                              <td className="px-4 py-2">
                                <input
                                  type="text"
                                  className="w-full border rounded px-2 py-1 bg-gray-100"
                                  value={field.id}
                                  disabled
                                  title={isDefaultIdField ? "系統預設欄位，ID 不可更改" : "ID 創建後不可更改"}
                                />
                              </td>
                              <td className="px-4 py-2">
                                <select
                                  className="w-full border rounded px-2 py-1 bg-gray-100"
                                  value={field.type}
                                  disabled
                                  title={isDefaultIdField ? "系統預設欄位，類型不可更改" : "類型創建後不可更改"}
                                >
                                  {dataFieldTypes.map(type => (
                                    <option key={type} value={type}>{type}</option>
                                  ))}
                                </select>
                              </td>
                              <td className="px-4 py-2">
                                <div className="w-full">
                                  <input
                                    type="text"
                                    className={`w-full border ${fieldError?.name ? 'border-red-500' : 'border-gray-300'} rounded px-2 py-1 ${isDefaultIdField ? 'bg-gray-100' : ''}`}
                                    value={field.name}
                                    onChange={(e) => !isDefaultIdField && handleFieldChange(field.id, { name: e.target.value })}
                                    onBlur={(e) => !isDefaultIdField && handleExistingFieldBlur(field.id, e.target.value)}
                                    placeholder="請輸入名稱"
                                    disabled={isDefaultIdField}
                                    title={isDefaultIdField ? "系統預設欄位，名稱不可更改" : ""}
                                  />
                                  {fieldError?.name && !isDefaultIdField && (
                                    <div className="text-red-500 text-xs mt-1">{fieldError.name}</div>
                                  )}
                                </div>
                              </td>
                              <td className="px-4 py-2">
                                <input
                                  type="text"
                                  className={`w-full border rounded px-2 py-1 ${isDefaultIdField ? 'border-blue-300' : ''}`}
                                  value={field.prefix}
                                  onChange={(e) => handleFieldChange(field.id, { prefix: e.target.value })}
                                  placeholder="請輸入前綴"
                                />
                              </td>
                              <td className="px-4 py-2">
                                <div className="flex items-center gap-2">                                  <button
                                    className={`p-1 ${isDefaultIdField ? 'text-gray-400 cursor-not-allowed' : index === 1 && sectionFields[0]?.id === 'id' ? 'text-gray-400 cursor-not-allowed' : 'hover:text-blue-600'}`}
                                    onClick={() => !isDefaultIdField && !(index === 1 && sectionFields[0]?.id === 'id') && handleMoveUp(index, section.id as DataFieldSectionType)}
                                    disabled={isDefaultIdField || index === 0 || (index === 1 && sectionFields[0]?.id === 'id')}
                                    title={isDefaultIdField ? "系統預設欄位，不可移動位置" : (index === 1 && sectionFields[0]?.id === 'id') ? "不能與系統預設 ID 欄位互換位置" : ""}
                                  >
                                    <MoveUp className="w-4 h-4" />
                                  </button>
                                  <button
                                    className={`p-1 ${isDefaultIdField ? 'text-gray-400 cursor-not-allowed' : 'hover:text-blue-600'}`}
                                    onClick={() => !isDefaultIdField && handleMoveDown(index, section.id as DataFieldSectionType)}
                                    disabled={isDefaultIdField || index === sectionFields.length - 1}
                                    title={isDefaultIdField ? "系統預設欄位，不可移動位置" : ""}
                                  >
                                    <MoveDown className="w-4 h-4" />
                                  </button>
                                  <button
                                    className={`p-1 ${isDefaultIdField ? 'text-gray-400 cursor-not-allowed' : 'hover:text-red-600'}`}
                                    onClick={() => !isDefaultIdField && handleDeleteField(field.id)}
                                    disabled={isDefaultIdField}
                                    title={isDefaultIdField ? "系統預設欄位，不可刪除" : ""}
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </button>
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  )}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}