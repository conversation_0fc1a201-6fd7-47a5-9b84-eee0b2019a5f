// EPD Manager App - 藍芽配對功能測試

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { AutoPairingButton } from '../components/AutoPairingButton';
import { DeviceManagementScreen } from '../screens/DeviceManagementScreen';
import { ConnectionMonitorScreen } from '../screens/ConnectionMonitorScreen';
import { COLORS, SIZES } from '../utils/constants';

export const BluetoothPairingTest: React.FC = () => {
  const [currentTest, setCurrentTest] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});

  const runTest = (testName: string, testFunction: () => Promise<boolean> | boolean) => {
    setCurrentTest(testName);
    
    try {
      const result = testFunction();
      
      if (result instanceof Promise) {
        result.then((success) => {
          setTestResults(prev => ({ ...prev, [testName]: success }));
          setCurrentTest(null);
          Alert.alert(
            '測試結果',
            `${testName}: ${success ? '通過' : '失敗'}`
          );
        }).catch((error) => {
          setTestResults(prev => ({ ...prev, [testName]: false }));
          setCurrentTest(null);
          Alert.alert('測試錯誤', `${testName}: ${error.message}`);
        });
      } else {
        setTestResults(prev => ({ ...prev, [testName]: result }));
        setCurrentTest(null);
        Alert.alert(
          '測試結果',
          `${testName}: ${result ? '通過' : '失敗'}`
        );
      }
    } catch (error: any) {
      setTestResults(prev => ({ ...prev, [testName]: false }));
      setCurrentTest(null);
      Alert.alert('測試錯誤', `${testName}: ${error.message}`);
    }
  };

  // 測試藍芽設備生成
  const testBluetoothDeviceGeneration = () => {
    try {
      // 模擬 AutoPairingButton 中的設備生成邏輯
      const devices = [];
      for (let i = 1; i <= 5; i++) {
        const randomMac = Array.from({ length: 6 }, () => 
          Math.floor(Math.random() * 256).toString(16).padStart(2, '0').toUpperCase()
        ).join(':');
        
        devices.push({
          id: `device_${i}`,
          name: `EPD-GATEWAY-${String(i).padStart(3, '0')}`,
          macAddress: randomMac,
          selected: false,
        });
      }
      
      // 驗證生成的設備
      const isValid = devices.length === 5 &&
        devices.every(device => 
          device.name.startsWith('EPD-GATEWAY-') &&
          device.macAddress.match(/^([0-9A-F]{2}:){5}[0-9A-F]{2}$/) &&
          device.selected === false
        );
      
      console.log('生成的藍芽設備:', devices);
      return isValid;
    } catch (error) {
      console.error('藍芽設備生成測試失敗:', error);
      return false;
    }
  };

  // 測試 MAC 地址格式
  const testMacAddressFormat = () => {
    try {
      const macRegex = /^([0-9A-F]{2}:){5}[0-9A-F]{2}$/;
      const testMacs = [
        'AA:BB:CC:DD:EE:FF',
        '12:34:56:78:9A:BC',
        'FF:FF:FF:FF:FF:FF',
        '00:00:00:00:00:00'
      ];
      
      const invalidMacs = [
        'invalid-mac',
        'AA:BB:CC:DD:EE',
        'AA:BB:CC:DD:EE:FF:GG',
        'aa:bb:cc:dd:ee:ff' // 小寫
      ];
      
      const validResults = testMacs.every(mac => macRegex.test(mac));
      const invalidResults = invalidMacs.every(mac => !macRegex.test(mac));
      
      return validResults && invalidResults;
    } catch (error) {
      console.error('MAC 地址格式測試失敗:', error);
      return false;
    }
  };

  // 測試設備選擇邏輯
  const testDeviceSelection = () => {
    try {
      let devices = [
        { id: 'device_1', name: 'EPD-GATEWAY-001', macAddress: 'AA:BB:CC:DD:EE:01', selected: false },
        { id: 'device_2', name: 'EPD-GATEWAY-002', macAddress: 'AA:BB:CC:DD:EE:02', selected: false },
        { id: 'device_3', name: 'EPD-GATEWAY-003', macAddress: 'AA:BB:CC:DD:EE:03', selected: false },
      ];
      
      // 模擬選擇設備
      devices = devices.map(device => ({
        ...device,
        selected: device.id === 'device_1' ? !device.selected : device.selected
      }));
      
      // 驗證選擇結果
      const selectedDevices = devices.filter(device => device.selected);
      const isValid = selectedDevices.length === 1 && selectedDevices[0].id === 'device_1';
      
      console.log('設備選擇測試結果:', { devices, selectedDevices, isValid });
      return isValid;
    } catch (error) {
      console.error('設備選擇測試失敗:', error);
      return false;
    }
  };

  // 測試批量選擇
  const testBatchSelection = () => {
    try {
      let devices = [
        { id: 'device_1', name: 'EPD-GATEWAY-001', macAddress: 'AA:BB:CC:DD:EE:01', selected: false },
        { id: 'device_2', name: 'EPD-GATEWAY-002', macAddress: 'AA:BB:CC:DD:EE:02', selected: false },
        { id: 'device_3', name: 'EPD-GATEWAY-003', macAddress: 'AA:BB:CC:DD:EE:03', selected: false },
      ];
      
      // 模擬批量選擇
      ['device_1', 'device_2'].forEach(deviceId => {
        devices = devices.map(device => ({
          ...device,
          selected: device.id === deviceId ? !device.selected : device.selected
        }));
      });
      
      // 驗證批量選擇結果
      const selectedDevices = devices.filter(device => device.selected);
      const isValid = selectedDevices.length === 2 && 
        selectedDevices.some(d => d.id === 'device_1') &&
        selectedDevices.some(d => d.id === 'device_2');
      
      console.log('批量選擇測試結果:', { devices, selectedDevices, isValid });
      return isValid;
    } catch (error) {
      console.error('批量選擇測試失敗:', error);
      return false;
    }
  };

  const getTestResultIcon = (testName: string) => {
    if (currentTest === testName) return '⏳';
    if (testResults[testName] === true) return '✅';
    if (testResults[testName] === false) return '❌';
    return '⚪';
  };

  const getTestResultColor = (testName: string) => {
    if (currentTest === testName) return COLORS.WARNING;
    if (testResults[testName] === true) return COLORS.SUCCESS;
    if (testResults[testName] === false) return COLORS.ERROR;
    return COLORS.TEXT_DISABLED;
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Text style={styles.title}>藍芽配對功能測試</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>單元測試</Text>
          
          <TouchableOpacity
            style={styles.testButton}
            onPress={() => runTest('藍芽設備生成', testBluetoothDeviceGeneration)}
            disabled={currentTest !== null}
          >
            <Text style={[styles.testIcon, { color: getTestResultColor('藍芽設備生成') }]}>
              {getTestResultIcon('藍芽設備生成')}
            </Text>
            <Text style={styles.testText}>藍芽設備生成測試</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.testButton}
            onPress={() => runTest('MAC地址格式', testMacAddressFormat)}
            disabled={currentTest !== null}
          >
            <Text style={[styles.testIcon, { color: getTestResultColor('MAC地址格式') }]}>
              {getTestResultIcon('MAC地址格式')}
            </Text>
            <Text style={styles.testText}>MAC 地址格式驗證</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.testButton}
            onPress={() => runTest('設備選擇', testDeviceSelection)}
            disabled={currentTest !== null}
          >
            <Text style={[styles.testIcon, { color: getTestResultColor('設備選擇') }]}>
              {getTestResultIcon('設備選擇')}
            </Text>
            <Text style={styles.testText}>設備選擇邏輯測試</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.testButton}
            onPress={() => runTest('批量選擇', testBatchSelection)}
            disabled={currentTest !== null}
          >
            <Text style={[styles.testIcon, { color: getTestResultColor('批量選擇') }]}>
              {getTestResultIcon('批量選擇')}
            </Text>
            <Text style={styles.testText}>批量選擇測試</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>組件測試</Text>
          
          <View style={styles.componentTest}>
            <Text style={styles.componentTitle}>一鍵配對按鈕</Text>
            <AutoPairingButton
              onSuccess={(devices) => {
                Alert.alert('配對成功', `成功配對 ${Array.isArray(devices) ? devices.length : 1} 個設備`);
              }}
              onError={(error) => {
                Alert.alert('配對失敗', error);
              }}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>測試說明</Text>
          <Text style={styles.description}>
            本測試頁面用於驗證藍芽配對功能的各個組件：
          </Text>
          <Text style={styles.listItem}>• 藍芽設備生成：驗證假設備數據的生成</Text>
          <Text style={styles.listItem}>• MAC 地址格式：驗證 MAC 地址格式的正確性</Text>
          <Text style={styles.listItem}>• 設備選擇：驗證單個設備選擇邏輯</Text>
          <Text style={styles.listItem}>• 批量選擇：驗證多個設備選擇邏輯</Text>
          <Text style={styles.listItem}>• 組件測試：實際測試配對按鈕功能</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollView: {
    flex: 1,
    padding: SIZES.SPACING_MD,
  },
  title: {
    fontSize: SIZES.FONT_SIZE_XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: SIZES.SPACING_LG,
  },
  section: {
    marginBottom: SIZES.SPACING_LG,
  },
  sectionTitle: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_MD,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.SURFACE,
    padding: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    marginBottom: SIZES.SPACING_SM,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
  },
  testIcon: {
    fontSize: SIZES.FONT_SIZE_LG,
    marginRight: SIZES.SPACING_MD,
  },
  testText: {
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_PRIMARY,
    flex: 1,
  },
  componentTest: {
    backgroundColor: COLORS.SURFACE,
    padding: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
  },
  componentTitle: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_MD,
  },
  description: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SIZES.SPACING_SM,
    lineHeight: 20,
  },
  listItem: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SIZES.SPACING_XS,
    lineHeight: 18,
  },
});

export default BluetoothPairingTest;
