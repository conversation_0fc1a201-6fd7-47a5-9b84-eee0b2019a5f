import React from 'react';
import { CheckCircle, XCircle, CircleDashed } from 'lucide-react';

// 重新定義接口
export interface ImageUpdateStatusBadgeProps {
  deviceImageCode?: string;
  updateStatus?: string;
  size?: 'sm' | 'md' | 'lg';
}

// 確保明確導出
export const ImageUpdateStatusBadge: React.FC<ImageUpdateStatusBadgeProps> = ({
  deviceImageCode,
  updateStatus,
  size = 'md'
}) => {
  // 根據 updateStatus 顯示更新狀態
  const getStatus = (): { text: string; className: string; icon: JSX.Element } => {
    // 如果有明確的更新狀態，直接使用
    if (updateStatus === '已更新') {
      return {
        text: '已更新',
        className: 'bg-green-100 text-green-800',
        icon: <CheckCircle className={`${getIconSize()} mr-1 text-green-500`} />
      };
    } else if (updateStatus === '未更新') {
      return {
        text: '未更新',
        className: 'bg-red-100 text-red-800',
        icon: <XCircle className={`${getIconSize()} mr-1 text-red-500`} />
      };
    }

    // 如果沒有 deviceImageCode，表示尚未推送圖像
    if (!deviceImageCode) {
      return {
        text: '未推送',
        className: 'bg-gray-100 text-gray-800',
        icon: <CircleDashed className={`${getIconSize()} mr-1 text-gray-500`} />
      };
    }

    // 如果沒有明確的更新狀態，但有 deviceImageCode，顯示為未回報
    return {
      text: '未回報',
      className: 'bg-yellow-100 text-yellow-800',
      icon: <CircleDashed className={`${getIconSize()} mr-1 text-yellow-500`} />
    };
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'w-3 h-3';
      case 'lg':
        return 'w-5 h-5';
      default:
        return 'w-4 h-4';
    }
  };

  const getBadgeSize = () => {
    switch (size) {
      case 'sm':
        return 'px-1.5 py-0.5 text-xs';
      case 'lg':
        return 'px-3 py-1 text-sm';
      default:
        return 'px-2 py-0.5 text-xs';
    }
  };

  const status = getStatus();

  return (
    <span className={`inline-flex items-center ${getBadgeSize()} font-semibold rounded-full ${status.className}`}>
      {status.icon}
      {status.text}
    </span>
  );
};
