import React, { useState, useEffect, useRef } from 'react';
import { Search, RefreshCw, Plus, Trash2, Edit, AlertCircle, Battery, Signal, Wifi, Link2, Link, Grid, Trash, Send } from 'lucide-react';
import { Device, DeviceStatus } from '../types/device';
import { getAllDevices, deleteDevice, syncDevices, sendDevicePreviewToGateway, updateDevice } from '../utils/api/deviceApi';
import { DeviceStatusBadge } from './ui/DeviceStatusBadge';
// 使用絕對路徑導入
import { ImageUpdateStatusBadge } from '../components/ui/ImageUpdateStatusBadge';
import { AddDeviceModal } from './AddDeviceModal';
import { EditDeviceModal } from './EditDeviceModal';
import BindDeviceDataModal from './BindDeviceDataModal';
import { saveDeviceFieldsViewConfig, getDeviceFieldsViewConfig, DEFAULT_DEVICE_FIELDS_VIEW_CONFIG } from '../utils/api/deviceConfigApi';
import { buildEndpointUrl } from '../utils/api/apiConfig';
import { Template, DisplayColorType } from '../types';
import { getScreenConfigs } from '../screens/screenSizeMap';
import { useTranslation } from 'react-i18next';
import { Store, StoreSpecificData } from '../types/store';
import { getAllStoreData } from '../utils/api/storeDataApi';
import { useAuthStore } from '../store/authStore';
import { regeneratePreviewBeforeSend } from '../utils/previewImageManager';

import ColorTypeGradient from './ui/ColorTypeGradient';

interface DevicesPageProps {
  store: Store | null;
  onViewDeviceDetail?: (device: Device) => void;
}

export function DevicesPage({ store, onViewDeviceDetail }: DevicesPageProps) {
  const { t } = useTranslation();
  const [devices, setDevices] = useState<Device[]>([]);
  const [filteredDevices, setFilteredDevices] = useState<Device[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<DeviceStatus | ''>('');
  const [sizeFilter, setSizeFilter] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [notification, setNotification] = useState<{ message: string, type: 'success' | 'error' } | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showBindModal, setShowBindModal] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [syncingDevices, setSyncingDevices] = useState(false);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [templateMap, setTemplateMap] = useState<Record<string, string>>({});
  const [storeData, setStoreData] = useState<StoreSpecificData[]>([]);  const [visibleFields, setVisibleFields] = useState<Record<string, boolean>>({
    macAddress: true,
    size: true,
    rssi: true,
    battery: true,
    status: true,
    lastSeen: true,
    code: true,
    note: true,
    dataId: true,
    templateId: true,
    colorType: true, // 添加 colorType 欄位
    imageUpdateStatus: true, // 添加圖片更新狀態欄位
  });const [showFieldManager, setShowFieldManager] = useState(false);
  const fieldManagerRef = useRef<HTMLDivElement>(null);
  const fieldManagerButtonRef = useRef<HTMLButtonElement>(null);

  // 處理欄位管理器外部點擊關閉
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        showFieldManager &&
        fieldManagerRef.current &&
        !fieldManagerRef.current.contains(event.target as Node) &&
        fieldManagerButtonRef.current &&
        !fieldManagerButtonRef.current.contains(event.target as Node)
      ) {
        // 關閉設定視窗前自動保存設定
        saveFieldsViewSettings();
        setShowFieldManager(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showFieldManager, visibleFields]);
  // 處理欄位顯示切換
  const toggleFieldVisibility = (fieldId: string) => {
    // 如果是 MAC 地址欄位，則不允許取消
    if (fieldId === 'macAddress') {
      return;
    }

    setVisibleFields(prev => ({
      ...prev,
      [fieldId]: !prev[fieldId]
    }));
  };

  // 檢查是否所有欄位都被選中
  const areAllFieldsSelected = () => {
    // 排除 macAddress 欄位後，檢查其他欄位是否全都選中
    const otherFields = Object.entries(visibleFields).filter(([key]) => key !== 'macAddress');
    return otherFields.length > 0 && otherFields.every(([_, isVisible]) => isVisible);
  };

  // 全選或取消全選所有欄位
  const toggleAllFields = () => {
    const allSelected = areAllFieldsSelected();
    const newVisibility = Object.keys(visibleFields).reduce((acc, field) => {
      // MAC 地址欄位始終保持選中狀態
      if (field === 'macAddress') {
        acc[field] = true;
      } else {
        acc[field] = !allSelected;
      }
      return acc;
    }, {} as Record<string, boolean>);
    setVisibleFields(newVisibility);
  };

  // 保存欄位顯示設定
  const saveFieldsViewSettings = async () => {
    try {
      // 將 Record<string, boolean> 格式轉換為欄位 ID 數組格式
      const visibleFieldsArray = Object.entries(visibleFields)
        .filter(([_, isVisible]) => isVisible)
        .map(([fieldId]) => fieldId);

      console.log('保存設備欄位設定:', visibleFieldsArray);

      await saveDeviceFieldsViewConfig({
        visibleFields: visibleFieldsArray,
        columnOrder: [], // 目前未實現排序功能
        columnWidths: {} // 目前未實現寬度調整功能
      });

      console.log('設備欄位設定保存成功');
      showNotification('欄位顯示設定已保存', 'success');
    } catch (error) {
      console.error('保存設備欄位設定失敗:', error);
      showNotification('保存欄位設定失敗', 'error');
    }
  };

  // 獲取設備尺寸對應的 DisplayColorType
  const getDeviceColorType = (deviceSize: string | undefined): DisplayColorType | undefined => {
    if (!deviceSize) return undefined;

    // 獲取所有屏幕配置
    const screenConfigs = getScreenConfigs();

    // 標準化尺寸字符串，移除非數字和點的字符
    let normalizedSize = deviceSize.replace(/[^0-9.]/g, '');

    // 尋找匹配的屏幕配置
    for (const config of screenConfigs) {
      // 標準化配置名稱
      const configName = config.name.replace(/[^0-9.]/g, '');

      if (configName === normalizedSize) {
        // 如果找到匹配的配置，返回其支持的第一個顏色類型
        return config.supportedColors[0];
      }
    }

    // 如果找不到匹配的配置，返回默認值
    return DisplayColorType.BW;
  };

  // 獲取設備列表
  const fetchDevices = async () => {
    try {
      setLoading(true);
      setError(null);

      // 如果有門店ID，則按門店ID過濾設備
      const deviceList = await getAllDevices(store?.id);

      // 將日期字符串轉換為 Date 對象
      const formattedDevices = deviceList.map(device => ({
        ...device,
        lastSeen: device.lastSeen ? new Date(device.lastSeen) : null,
        createdAt: device.createdAt ? new Date(device.createdAt) : null,
        updatedAt: device.updatedAt ? new Date(device.updatedAt) : null,
      }));

      // 檢查並設置每個設備的 colorType
      for (const device of formattedDevices) {
        if (device._id && device.data && !device.data.colorType && device.data.size) {
          const colorType = getDeviceColorType(device.data.size);
          if (colorType) {
            try {
              await updateDevice(device._id, {
                data: {
                  ...device.data,
                  colorType: colorType
                }
              });
              console.log(`已根據設備尺寸設置 colorType: ${colorType}`);
              // 更新本地設備數據
              device.data.colorType = colorType;
            } catch (error) {
              console.error('設置 colorType 失敗:', error);
            }
          }
        }
      }

      setDevices(formattedDevices);
      applyFilters(formattedDevices, searchTerm, statusFilter, sizeFilter);
    } catch (err: any) {
      console.error('獲取設備列表失敗:', err);
      setError(err.message || '獲取設備列表失敗');
    } finally {
      setLoading(false);
    }
  };  // 獲取模板列表
  const fetchTemplates = async () => {
    try {
      // 從 authStore 獲取 token
      const { token } = useAuthStore.getState();

      // 構建API URL，如果有門店ID則添加到查詢參數
      let apiUrl = buildEndpointUrl('templates');
      if (store?.id) {
        apiUrl += `?storeId=${encodeURIComponent(store.id)}`;
        console.log(`按門店ID過濾模板: ${store.id}`);
      }

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        credentials: 'include', // 包含 cookie
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('未登入或登入已過期');
        }
        throw new Error(`獲取模板列表失敗: ${response.status}`);
      }
      const templateList = await response.json();
      setTemplates(templateList);

      // 創建模板ID到模板名稱的映射
      const map: Record<string, string> = {};
      templateList.forEach((template: Template) => {
        map[template.id] = template.name;
      });
      setTemplateMap(map);
    } catch (err) {
      console.error('獲取模板列表失敗:', err);
    }
  };

  // 獲取門店數據
  const fetchStoreData = async () => {
    if (!store?.id) return;

    try {
      console.log(`正在獲取門店 ${store.id} 的數據...`);
      const data = await getAllStoreData(store.id);
      console.log(`獲取到門店數據，數量: ${data.length}`);
      setStoreData(data);
    } catch (error) {
      console.error('獲取門店數據失敗:', error);
    }
  };

  // 初始加載
  useEffect(() => {
    loadFieldsViewSettings();
  }, []);

  // 監聽設備更新事件
  useEffect(() => {
    const handleDeviceUpdated = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail) {
        console.log('接收到設備更新事件，重新獲取設備列表');
        fetchDevices();
      }
    };

    window.addEventListener('deviceUpdated', handleDeviceUpdated);

    return () => {
      window.removeEventListener('deviceUpdated', handleDeviceUpdated);
    };
  }, []);

  // 當門店變化時重新獲取所有數據
  useEffect(() => {
    if (store?.id) {
      console.log(`門店變更為 ${store.id}，重新獲取設備列表、模板和門店數據`);
      fetchDevices();
      fetchTemplates();
      fetchStoreData();
    }
  }, [store?.id]);
  // 載入欄位顯示設定
  const loadFieldsViewSettings = async () => {
    try {
      try {
        const savedConfig = await getDeviceFieldsViewConfig();

        if (savedConfig && savedConfig.visibleFields !== undefined) {
          console.log('已載入保存的設備欄位設定:', savedConfig.visibleFields);

          // 將數組格式的 visibleFields 轉換為 Record<string, boolean> 格式
          const initialFieldKeys = Object.keys(visibleFields);
          const savedFieldsVisibility = initialFieldKeys.reduce((acc, field) => {
            acc[field] = savedConfig.visibleFields.includes(field);
            return acc;
          }, {} as Record<string, boolean>);

          setVisibleFields(savedFieldsVisibility);
          return;
        }
      } catch (configErr: any) {
        // 如果是 404 錯誤，表示後端尚未實現此端點，使用默認配置
        if (configErr.message && (configErr.message.includes('Not Found') || configErr.message.includes('404'))) {
          console.log('設備欄位設定API尚未實現，使用默認配置');
        } else {
          console.error('載入設備欄位設定失敗:', configErr);
        }
      }

      // 如果沒有設定或發生錯誤，使用默認設定
      console.log('使用默認設備欄位設定');
      const defaultVisibility = DEFAULT_DEVICE_FIELDS_VIEW_CONFIG.visibleFields.reduce((acc, field) => {
        acc[field] = true;
        return acc;
      }, {} as Record<string, boolean>);

      setVisibleFields(defaultVisibility);
    } catch (err) {
      console.error('設定欄位視圖時發生未預期的錯誤:', err);
      // 發生錯誤時使用當前的設定
    }
  };

  // 應用篩選器
  const applyFilters = (
    deviceList: Device[],
    search: string,
    status: DeviceStatus | '',
    size: string
  ) => {
    let result = [...deviceList];

    // 搜尋條件篩選
    if (search) {
      const lowerSearch = search.toLowerCase();
      result = result.filter(
        device =>
          device.macAddress.toLowerCase().includes(lowerSearch) ||
          device.dataId?.toLowerCase().includes(lowerSearch)
      );
    }

    // 狀態篩選
    if (status) {
      result = result.filter(device => device.status === status);
    }    // 尺寸篩選
    if (size) {
      result = result.filter(device => device.data?.size === size);
    }

    setFilteredDevices(result);

    // 重置分頁
    setCurrentPage(1);

    // 重置選擇
    setSelectedItems([]);
    setSelectAll(false);
  };

  // 當篩選條件變更時重新應用篩選
  useEffect(() => {
    applyFilters(devices, searchTerm, statusFilter, sizeFilter);
  }, [searchTerm, statusFilter, sizeFilter, devices]);

  // 處理搜尋
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // 處理狀態篩選
  const handleStatusFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value as DeviceStatus | '' );
  };

  // 處理尺寸篩選
  const handleSizeFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSizeFilter(e.target.value);
  };

  // 處理刪除設備
  const handleDelete = async (deviceId: string) => {
    if (window.confirm(t('devices.confirmDelete'))) {
      try {
        // 如果有門店ID，則傳遞給刪除函數
        await deleteDevice(deviceId, store?.id);
        showNotification('刪除設備成功', 'success');
        // 更新本地數據
        setDevices(prevDevices => prevDevices.filter(device => device._id !== deviceId));
      } catch (err: any) {
        showNotification(err.message || '刪除設備失敗', 'error');
      }
    }
  };

  // 處理批量刪除
  const handleBatchDelete = async () => {
    const count = selectedItems.length;
    if (count === 0) {
      showNotification('請先選擇要刪除的設備', 'error');
      return;
    }

    if (window.confirm(t('devices.confirmDeleteMultiple', { count }))) {
      try {
        // 逐個刪除選中的設備
        for (const deviceId of selectedItems) {
          // 如果有門店ID，則傳遞給刪除函數
          await deleteDevice(deviceId, store?.id);
        }

        showNotification(`成功刪除 ${count} 個設備`, 'success');

        // 更新本地數據
        setDevices(prevDevices =>
          prevDevices.filter(device => !selectedItems.includes(device._id || ''))
        );

        // 清空選擇
        setSelectedItems([]);
        setSelectAll(false);
      } catch (err: any) {
        showNotification(err.message || '批量刪除設備失敗', 'error');
      }
    }
  };

  // 處理編輯設備
  const handleEdit = (device: Device) => {
    setSelectedDevice(device);
    setShowEditModal(true);
  };

  // 處理同步設備狀態
  const handleSync = async () => {
    try {
      setSyncingDevices(true);
      // 如果有門店ID，則傳遞給同步函數
      const success = await syncDevices(store?.id);
      if (success) {
        showNotification('設備狀態同步成功', 'success');
        // 重新獲取設備列表
        await fetchDevices();
      } else {
        showNotification('設備狀態同步失敗', 'error');
      }
    } catch (err: any) {
      showNotification(err.message || '設備狀態同步失敗', 'error');
    } finally {
      setSyncingDevices(false);
    }
  };
  // 處理發送預覽圖到網關
  const handleSendPreviewToGateway = async (device: Device) => {
    if (!device._id) {
      showNotification(t('devices.deviceIdMissing'), 'error');
      return;
    }

    try {
      setLoading(true);

      // 確認設備有主要網關
      if (!device.primaryGatewayId) {
        showNotification(t('devices.noPrimaryGateway'), 'error');
        setLoading(false);
        return;
      }

      // 先獲取最新的設備數據，確保我們使用最新的狀態
      let latestDevice: Device;
      try {
        const deviceResponse = await fetch(buildEndpointUrl(`devices/${device._id}`), {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
        });

        if (!deviceResponse.ok) {
          throw new Error('無法獲取最新設備數據');
        }

        latestDevice = await deviceResponse.json();
        console.log('已獲取最新設備數據:', latestDevice._id);
      } catch (error) {
        console.error('獲取最新設備數據失敗:', error);
        // 如果無法獲取最新數據，使用當前設備數據
        latestDevice = device;
      }

      // 嘗試獲取設備的模板和門店數據來重新生成預覽圖

      if (latestDevice.templateId && latestDevice.dataBindings) {
        showNotification('正在重新生成預覽圖...', 'success');

        try {
          // 1. 獲取模板數據
          const templateResponse = await fetch(buildEndpointUrl(`templates/${latestDevice.templateId}`), {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
          });

          if (!templateResponse.ok) {
            throw new Error('無法獲取模板數據');
          }

          const template = await templateResponse.json();

          // 2. 獲取門店數據
          const storeDataList = await getAllStoreData(latestDevice.storeId || store?.id || '');

          // 3. 使用 regeneratePreviewBeforeSend 函數重新生成預覽圖
          const newPreviewImage = await regeneratePreviewBeforeSend(latestDevice, storeDataList, template);

          if (newPreviewImage) {
            showNotification('已重新生成預覽圖，正在發送...', 'success');
          } else {
            // 如果重新生成失敗但設備有保存的預覽圖，則使用已有的預覽圖
            if (!latestDevice.previewImage) {
              showNotification('無法生成預覽圖，並且設備沒有保存的預覽圖', 'error');
              setLoading(false);
              return;
            }
            showNotification('重新生成預覽圖失敗，將使用已保存的預覽圖', 'error');
          }
        } catch (e) {
          console.error('嘗試重新生成預覽圖失敗:', e);
          // 如果嘗試重新生成失敗但設備有保存的預覽圖，則使用已有的預覽圖
          if (!latestDevice.previewImage) {
            showNotification('無法生成預覽圖，並且設備沒有保存的預覽圖', 'error');
            setLoading(false);
            return;
          }
          showNotification('重新生成預覽圖失敗，將使用已保存的預覽圖', 'error');
        }
      } else if (!latestDevice.previewImage) {
        // 如果無法重新生成且沒有預覽圖，則提示錯誤
        showNotification(t('devices.noPreviewImage'), 'error');
        setLoading(false);
        return;
      }

      // 發送預覽圖到網關
      if (!latestDevice._id) {
        showNotification('設備ID不存在，無法發送', 'error');
        setLoading(false);
        return;
      }

      console.log(`發送預覽圖到網關，設備ID: ${latestDevice._id}`);
      const result = await sendDevicePreviewToGateway(latestDevice._id, {
        sendToAllGateways: false,
        storeId: latestDevice.storeId || store?.id
      });
      console.log(`發送預覽圖到網關結果:`, result);

      if (result.success) {
        showNotification(t('devices.previewSendSuccess'), 'success');
        // 發送成功後立即重新獲取設備列表，以更新圖片更新狀態
        await fetchDevices();
      } else {
        showNotification(t('devices.previewSendFail') + ': ' + (result.error || t('common.unknownError')), 'error');
      }
    } catch (error) {
      console.error('發送預覽圖到網關出錯:', error);
      showNotification(t('devices.previewSendError') + ': ' + (error instanceof Error ? error.message : t('common.unknownError')), 'error');
    } finally {
      setLoading(false);
    }
  };

  // 顯示通知
  const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 3000);
  };

  // 處理全選/取消全選
  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // 選中當前頁的所有設備
      const currentPageItems = getCurrentPageItems().map(device => device._id || '').filter(id => id !== '');
      setSelectedItems(currentPageItems);
    } else {
      // 取消選中
      setSelectedItems([]);
    }
  };

  // 處理單個選擇
  const handleSelectItem = (e: React.ChangeEvent<HTMLInputElement>, deviceId: string) => {
    const checked = e.target.checked;

    if (checked) {
      setSelectedItems(prev => [...prev, deviceId]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== deviceId));
    }
  };

  // 獲取當前頁的設備
  const getCurrentPageItems = () => {
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return filteredDevices.slice(indexOfFirstItem, indexOfLastItem);
  };

  // 計算總頁數
  const totalPages = Math.ceil(filteredDevices.length / itemsPerPage);

  // 處理頁面變更
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };
  // 獲取唯一的尺寸列表
  const uniqueSizes = [...new Set(devices.map(device => device.data?.size).filter(Boolean))];
  // 渲染電量指示器
  const renderBatteryIndicator = (battery?: number) => {
    if (battery === undefined) {
      return <div className="flex items-center">
        <Battery className="w-4 h-4 mr-1 text-gray-400" />
        <span>未知</span>
      </div>;
    }

    let color = 'text-green-500';
    if (battery < 20) {
      color = 'text-red-500';
    } else if (battery < 50) {
      color = 'text-yellow-500';
    }

    return (
      <div className="flex items-center">
        <Battery className={`w-4 h-4 mr-1 ${color}`} />
        <span className={battery < 20 ? 'text-red-500 font-bold' : ''}>{battery}%</span>
      </div>
    );
  };

  // 渲染信號強度指示器
  const renderSignalStrength = (rssi?: number) => {
    if (rssi === undefined) {
      return <div className="flex items-center">
        <Wifi className="w-4 h-4 mr-1 text-gray-400" />
        <span>未知</span>
      </div>;
    }

    // RSSI 通常在 -100 (很弱) 到 0 (很強) 之間
    let icon = <Wifi className="w-4 h-4 mr-1 text-gray-400" />;
    let color = 'text-gray-500';

    if (rssi > -70) {
      icon = <Wifi className="w-4 h-4 mr-1 text-green-500" />;
      color = 'text-green-500';
    } else if (rssi > -85) {
      icon = <Wifi className="w-4 h-4 mr-1 text-yellow-500" />;
      color = 'text-yellow-500';
    } else {
      icon = <Wifi className="w-4 h-4 mr-1 text-red-500" />;
      color = 'text-red-500';
    }

    return (
      <div className="flex items-center">
        {icon}
        <span className={color}>{rssi} dBm</span>
      </div>
    );
  };
  return (
    <div className="p-8 lg:px-4 py-2">
      <div className="max-w-7xl mx-auto">
        {/* 通知消息 */}
        {notification && (
          <div
            className={`mb-4 p-4 rounded-md ${
              notification.type === 'success' ? 'bg-green-100 border-l-4 border-green-500 text-green-700' :
              'bg-red-100 border-l-4 border-red-500 text-red-700'
            } flex items-center justify-between`}
          >
            <div className="flex items-center">
              {notification.type === 'success' ? null : <AlertCircle className="w-5 h-5 mr-2" />}
              <span>{notification.message}</span>
            </div>
            <button
              className="text-gray-500 hover:text-gray-700"
              onClick={() => setNotification(null)}
            >
              &times;
            </button>
          </div>
        )}

        {/* 錯誤消息 */}
        {error && (
          <div className="mb-4 p-4 bg-red-100 border-l-4 border-red-500 text-red-700 flex items-center justify-between">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 mr-2" />
              <span>{error}</span>
            </div>
            <button
              className="text-gray-500 hover:text-gray-700"
              onClick={() => setError(null)}
            >
              &times;
            </button>
          </div>
        )}

        {/* 工具列 */}
        <div className="mb-6 flex flex-wrap items-center gap-4">
          {/* 顯示當前門店名稱 */}
          <div className="px-4 py-2 bg-blue-100 text-blue-800 rounded-md border border-blue-300">
            <span className="font-medium">{store?.name || '未選擇門店'}</span>
            <span className="ml-2 text-blue-600">({store?.id || ''})</span>
          </div>

          <div className="relative flex-1">
            <input
              type="text"
              placeholder={t('devices.searchPlaceholder')}
              value={searchTerm}
              onChange={handleSearch}
              className="w-full pl-4 pr-10 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Search className="absolute right-3 top-2.5 text-gray-400 w-5 h-5" />
          </div>

          {/* 狀態篩選 */}
          <select
            value={statusFilter}
            onChange={handleStatusFilter}
            className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">{t('devices.allStatuses')}</option>
            <option value="online">{t('devices.online')}</option>
            <option value="offline">{t('devices.offline')}</option>
          </select>

          {/* 尺寸篩選 */}
          <select
            value={sizeFilter}
            onChange={handleSizeFilter}
            className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">{t('devices.allSizes')}</option>
            {uniqueSizes.map(size => (
              <option key={size} value={size}>{size}</option>
            ))}
          </select>

          {/* 同步按鈕 */}
          <button
            onClick={handleSync}
            disabled={syncingDevices}
            className={`flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md ${
              syncingDevices ? 'opacity-75 cursor-not-allowed' : 'hover:bg-blue-600'
            }`}
          >
            <RefreshCw className={`w-5 h-5 ${syncingDevices ? 'animate-spin' : ''}`} />
            {syncingDevices ? t('devices.syncing') : t('devices.syncDevices')}
          </button>

          {/* 新增設備按鈕 */}
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-violet-500 text-white rounded-md hover:bg-violet-600"
          >
            <Plus className="w-5 h-5" />
            {t('devices.addDevice')}
          </button>

          {/* 批量刪除按鈕 */}
          <button
            onClick={handleBatchDelete}
            className="flex items-center gap-2 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
          >
            <Trash2 className="w-5 h-5" />
            {t('common.delete')}
          </button>

          {/* 欄位管理器 */}
          <div className="relative" ref={fieldManagerRef}>
            <button
              className="flex items-center gap-2 px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600"
              onClick={() => setShowFieldManager(!showFieldManager)}
              ref={fieldManagerButtonRef}
            >
              <Grid className="w-5 h-5" />
              {t('devices.fieldManagement')}
            </button>

            {showFieldManager && (
              <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg z-50 py-2 px-3 border border-gray-200">
                <div className="pb-2 border-b border-gray-200">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-sm text-gray-700">{t('database.displayFieldSettings')}</h3>
                    <div>
                      <label className="text-xs flex items-center text-gray-600 cursor-pointer">
                        <input
                          type="checkbox"
                          className="mr-1 rounded"
                          checked={areAllFieldsSelected()}
                          onChange={toggleAllFields}
                        />
                        {t('common.selectAll')}
                      </label>
                    </div>
                  </div>
                </div>                <div className="pt-2 max-h-60 overflow-y-auto">
                  {Object.keys(visibleFields).map((field) => (
                    <label key={field} className={`block py-1 px-2 text-sm ${field === 'macAddress' ? 'text-gray-500' : 'text-gray-700'} hover:bg-gray-100 rounded cursor-pointer`}>
                      <input
                        type="checkbox"
                        className="mr-2 rounded"
                        checked={visibleFields[field] || false}
                        onChange={() => toggleFieldVisibility(field)}
                        disabled={field === 'macAddress'}
                      />                      {field === 'macAddress' ? `${t('devices.macAddress')} (${t('common.required')})` :
                       field === 'size' ? t('devices.size') :
                       field === 'colorType' ? t('devices.colorType') :
                       field === 'rssi' ? t('devices.rssi') :
                       field === 'battery' ? t('devices.battery') :
                       field === 'status' ? t('devices.status') :
                       field === 'dataId' ? t('devices.dataId') :
                       field === 'templateId' ? t('templates.templateName') :
                       field === 'imageUpdateStatus' ? '圖片更新狀態' :
                       field === 'lastSeen' ? t('devices.lastSeen') :
                       field === 'code' ? t('devices.code') :
                       field === 'note' ? t('devices.note') : field}
                    </label>
                  ))}

                </div>
              </div>
            )}
          </div>
        </div>        {/* 設備列表 */}
        <div className="bg-white rounded-lg shadow relative">
          {/* 使用三個固定位置的表格實現左右固定，中間滾動的效果 */}
          <div className="relative overflow-x-auto" style={{ maxWidth: "100%" }}>
            <div className="flex relative">
              {/* 左側固定欄位（勾選框和序號） */}
              <div className="sticky left-0 z-20 bg-white shadow-sm" style={{ width: "130px" }}>
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 w-12 border-b border-gray-200 text-white">
                        <input
                          type="checkbox"
                          className="rounded"
                          checked={selectAll}
                          onChange={handleSelectAll}
                        />
                      </th>                      <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 text-white">
                        {t('database.serialNumber')}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={2} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8">
                            <RefreshCw className="w-5 h-5 animate-spin mx-auto mb-2" />
                          </div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td colSpan={2} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8">{t('database.noData')}</div>
                        </td>
                      </tr>
                    ) : (
                      getCurrentPageItems().map((device, index) => (
                        <tr key={`left-${device._id || index}`} className={`h-[53px] ${index % 2 === 0 ? 'bg-amber-50' : 'bg-blue-50'}`}>
                          <td className={`px-4 py-3 border-b border-gray-200 align-middle`}>
                            <input
                              type="checkbox"
                              className="rounded"
                              checked={selectedItems.includes(device._id || '')}
                              onChange={(e) => handleSelectItem(e, device._id || '')}
                            />
                          </td>
                          <td className={`px-4 py-3 border-b border-gray-200 align-middle`}>
                            {/* 顯示流水號，從1開始 */}
                            {(currentPage - 1) * itemsPerPage + index + 1}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* 中間可滾動區域（動態欄位） */}
              <div className="flex-1 overflow-x-auto">
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      {visibleFields.macAddress && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('devices.macAddress')}
                        </th>
                      )}
                      {visibleFields.size && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[80px] text-white">
                          {t('devices.size')}
                        </th>
                      )}
                      {visibleFields.colorType && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[100px] text-white">
                          {t('devices.colorType')}
                        </th>
                      )}
                      {visibleFields.rssi && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[120px] text-white">
                          {t('devices.rssi')}
                        </th>
                      )}
                      {visibleFields.battery && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[100px] text-white">
                          {t('devices.battery')}
                        </th>
                      )}
                      {visibleFields.status && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[80px] text-white">
                          {t('devices.status')}
                        </th>
                      )}
                      {visibleFields.imageUpdateStatus && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[120px] text-white">
                          圖片更新狀態
                        </th>
                      )}
                                            {visibleFields.dataId && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('devices.dataId')}
                        </th>
                      )}
                      {visibleFields.templateId && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('templates.templateName')}
                        </th>
                      )}                      {visibleFields.lastSeen && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[180px] text-white">
                          {t('devices.lastSeen')}
                        </th>
                      )}
                      {visibleFields.code && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[100px] text-white">
                          {t('devices.code')}
                        </th>
                      )}                      {visibleFields.note && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('devices.note')}
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={Object.values(visibleFields).filter(Boolean).length} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td colSpan={Object.values(visibleFields).filter(Boolean).length} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8">
                            {filteredDevices.length === 0 && devices.length > 0
                              ? t('devices.noFilteredDevices')
                              : t('devices.noDevices')}
                          </div>
                        </td>
                      </tr>
                    ) : (
                      getCurrentPageItems().map((device, index) => (
                        <tr
                          key={`mid-${device._id || index}`}
                          className={`h-[53px] ${index % 2 === 0 ? 'bg-amber-50' : 'bg-blue-50'} cursor-pointer hover:bg-amber-100`}
                          onClick={() => onViewDeviceDetail && onViewDeviceDetail(device)}
                        >
                          {visibleFields.macAddress && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.macAddress}
                            </td>
                          )}                          {visibleFields.size && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[80px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.data?.size || '未知'}
                            </td>
                          )}
                          {visibleFields.colorType && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[140px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              <ColorTypeGradient colorType={device.data?.colorType || '未知'} size="sm" />
                            </td>
                          )}
                          {visibleFields.rssi && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[120px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {renderSignalStrength(device.data?.rssi)}
                            </td>
                          )}
                          {visibleFields.battery && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[100px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {renderBatteryIndicator(device.data?.battery)}
                            </td>
                          )}{visibleFields.status && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[80px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              <DeviceStatusBadge status={device.status} />
                            </td>
                          )}                          {visibleFields.imageUpdateStatus && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[120px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>                              <ImageUpdateStatusBadge
                                deviceImageCode={device.data?.imageCode}
                                updateStatus={device.imageUpdateStatus}
                                size="sm"
                              />
                            </td>
                          )}
                          {visibleFields.dataId && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[200px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {(() => {
                                // 顯示所有綁定數據
                                if (device.dataBindings) {
                                  try {
                                    // 解析數據綁定
                                    const bindings = typeof device.dataBindings === 'string'
                                      ? JSON.parse(device.dataBindings)
                                      : device.dataBindings;

                                    // 將綁定數據值去重
                                    const uniqueValues = [...new Set(Object.values(bindings))];

                                    // 從門店數據中查找對應的ID值
                                    const displayValues = uniqueValues.map(uid => {
                                      // 嘗試在門店數據中查找對應的項目
                                      const storeItem = storeData.find(item =>
                                        item.uid === uid ||
                                        item._uid === uid ||
                                        (item._id && item._id.toString() === uid)
                                      );

                                      // 如果找到了對應的項目，顯示其ID或名稱
                                      if (storeItem) {
                                        if (storeItem.name) {
                                          return storeItem.name;
                                        } else if (storeItem.id) {
                                          return storeItem.id;
                                        }
                                      }

                                      // 如果是UID格式（24位十六進制字符串），不顯示
                                      const uidPattern = /^[0-9a-f]{24}$/i;
                                      if (typeof uid === 'string' && uidPattern.test(uid)) {
                                        return ''; // 不顯示UID
                                      }

                                      // 如果都不符合，顯示原始值
                                      return uid;
                                    }).filter(Boolean); // 過濾掉空字符串

                                    // 如果沒有有效的顯示值，顯示默認值
                                    if (displayValues.length === 0) {
                                      return device.dataId || '-';
                                    }

                                    return displayValues.join(', ');
                                  } catch (e) {
                                    return device.dataId || '-';
                                  }
                                } else {
                                  return device.dataId || '-';
                                }
                              })()}
                            </td>
                          )}
                          {visibleFields.templateId && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.templateId ? (templateMap[device.templateId] || device.templateId) : '-'}
                            </td>
                          )}{visibleFields.lastSeen && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[180px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.lastSeen
                                ? new Date(device.lastSeen).toLocaleString()
                                : '無記錄'}
                            </td>
                          )}
                          {visibleFields.code && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[100px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.code || '-'}
                            </td>
                          )}                          {visibleFields.note && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.note || '-'}
                            </td>
                          )}
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* 右側固定區域（操作欄） */}
              <div className="sticky right-0 z-20 bg-white shadow-sm" style={{ width: "180px" }}>
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap text-white">
                        {t('devices.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : (
                      getCurrentPageItems().map((device, index) => (
                        <tr key={`right-${device._id || index}`} className={`h-[53px] align-middle ${index % 2 === 0 ? 'bg-amber-50' : 'bg-blue-50'}`}>
                          <td className="px-4 py-3 border-b border-gray-200 whitespace-nowrap overflow-hidden text-ellipsis">
                            <div className="flex items-center gap-2">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation(); // 防止觸發行點擊事件
                                  handleSendPreviewToGateway(device);
                                }}
                                className="text-gray-500 hover:text-green-600"
                                title={t('devices.sendPreview')}
                              >
                                <Send className="w-5 h-5" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation(); // 防止觸發行點擊事件
                                  setSelectedDevice(device);
                                  setShowBindModal(true);
                                }}
                                className="text-gray-500 hover:text-blue-600"
                                title={t('devices.bindData')}
                              >
                                <Link className="w-5 h-5" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation(); // 防止觸發行點擊事件
                                  handleEdit(device);
                                }}
                                className="text-gray-500 hover:text-blue-600"
                                title={t('devices.editDevice')}
                              >
                                <Edit className="w-5 h-5" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation(); // 防止觸發行點擊事件
                                  handleDelete(device._id || '');
                                }}
                                className="text-gray-500 hover:text-red-600"
                                title={t('common.delete')}
                              >
                                <Trash className="w-5 h-5" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          {/* 分頁控制 */}
          {filteredDevices.length > 0 && (
            <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200">
              <div>
                <p className="text-sm text-gray-700">
                  {t('common.showing')}
                  <span className="font-medium mx-1">
                    {filteredDevices.length === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1}
                  </span>
                  {t('common.to')}
                  <span className="font-medium mx-1">
                    {Math.min(currentPage * itemsPerPage, filteredDevices.length)}
                  </span>
                  {t('common.of')}
                  <span className="font-medium mx-1">{filteredDevices.length}</span>
                  {t('common.entries')}
                </p>
              </div>

              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  {/* 上一頁按鈕 */}
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">{t('common.previousPage')}</span>
                    &laquo;
                  </button>

                  {/* 頁碼按鈕 */}
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(pageNum => (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium
                        ${currentPage === pageNum
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                    >
                      {pageNum}
                    </button>
                  ))}

                  {/* 下一頁按鈕 */}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">{t('common.nextPage')}</span>
                    &raquo;
                  </button>
                </nav>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 新增設備模態窗口 */}
      <AddDeviceModal
        isOpen={showAddModal}
        storeId={store?.id || ''}
        onClose={() => setShowAddModal(false)}
        onSuccess={() => {
          fetchDevices();
          showNotification('設備新增成功', 'success');
        }}
      />

      {/* 編輯設備模態窗口 */}
      <EditDeviceModal
        isOpen={showEditModal}
        device={selectedDevice}
        storeId={store?.id}
        onClose={() => {
          setShowEditModal(false);
          setSelectedDevice(null);
        }}
        onSuccess={() => {
          fetchDevices();
          showNotification('設備更新成功', 'success');
        }}
      />

      {/* 綁定數據模態窗口 */}
      <BindDeviceDataModal
        isOpen={showBindModal}
        device={selectedDevice}
        store={store} // 明確傳遞當前門店信息
        onClose={() => {
          setShowBindModal(false);
          setSelectedDevice(null);
        }}
        onSuccess={() => {
          fetchDevices();
          showNotification('數據綁定成功', 'success');
        }}
      />
    </div>
  );
}
