// filepath: c:\Users\<USER>\Desktop\code\git\epd-manager-lite\server\services\sendPreviewToGateway.js
/**
 * 發送設備預覽圖到網關的服務
 * 用於將設備中綁定的數據預覽圖發送到對應的主要網關
 */

const { ObjectId } = require('mongodb');
const { getScreenDimensionsMap } = require('../utils/screenConfigs');

// 創建 ObjectId 的安全函數，處理各種輸入類型
const safeObjectId = (id) => {
  if (id instanceof ObjectId) {
    return id;
  }

  // 確保輸入是字符串
  const idStr = String(id);

  if (!ObjectId.isValid(idStr)) {
    throw new Error(`無效的 ObjectId 格式: ${idStr}`);
  }

  return new ObjectId(idStr);
};

// 引入預覽服務
const previewService = require('./previewService');

// 引入 EPD 轉換模組
const epdConversion = require('../utils/epdConversion');

/**
 * 映射設備 colorType 到 DisplayColorType
 */
function mapDeviceColorType(deviceColorType) {
  if (!deviceColorType) return epdConversion.DisplayColorType.BW; // 默認值

  // 如果已經是正確的格式，直接返回
  if (Object.values(epdConversion.DisplayColorType).includes(deviceColorType)) {
    return deviceColorType;
  }

  // 處理簡短代碼
  switch (deviceColorType.toUpperCase()) {
    case 'BW':
    case 'GRAY16':
      return epdConversion.DisplayColorType.BW; // "Gray16"
    case 'BWR':
      return epdConversion.DisplayColorType.BWR; // "Black & White & Red"
    case 'BWRY':
      return epdConversion.DisplayColorType.BWRY; // "Black & White & Red & Yellow"
    default:
      console.warn(`未知的設備顏色類型: ${deviceColorType}，使用默認值`);
      return epdConversion.DisplayColorType.BW;
  }
}

/**
 * 解析設備尺寸
 */
function parseDeviceSize(sizeStr) {
  if (!sizeStr) {
    throw new Error('設備尺寸為空，無法解析');
  }

  // 首先嘗試解析 "寬x高" 格式
  const directMatch = sizeStr.match(/(\d+)x(\d+)/);
  if (directMatch && directMatch.length === 3) {
    return {
      width: parseInt(directMatch[1], 10),
      height: parseInt(directMatch[2], 10)
    };
  }

  // 嘗試解析尺寸標識符（如 "2.9"", "6"" 等）
  const sizeIdentifierMatch = sizeStr.match(/(\d+(?:\.\d+)?)"?/);
  if (sizeIdentifierMatch) {
    const sizeIdentifier = sizeIdentifierMatch[1];

    // 從螢幕配置動態獲取尺寸映射表
    const dimensionsMap = getScreenDimensionsMap();

    if (dimensionsMap[sizeIdentifier]) {
      const dimensions = dimensionsMap[sizeIdentifier];
      console.log(`解析設備尺寸: ${sizeStr} -> ${dimensions.width}x${dimensions.height}`);
      return dimensions;
    }
  }

  throw new Error(`無法解析設備尺寸: ${sizeStr}，請確認尺寸格式正確`);
}

/**
 * 生成 rawdata
 */
async function generateRawData(imageDataStr, device, imageCode, template) {
  try {
    // 創建臨時 Canvas
    const canvas = await epdConversion.createCanvasFromImageData(imageDataStr);

    // 獲取設備的 colorType 和尺寸
    const colorType = mapDeviceColorType(device.data?.colorType);
    const { width, height } = parseDeviceSize(device.data?.size);
    const templateRotation = epdConversion.getTemplateRotation(template);

    console.log(`設備 ${device.macAddress} - 模板旋轉角度: ${templateRotation}°`);

    // 轉換選項
    const options = {
      colorType,
      width,
      height,
      imagecode: parseInt(imageCode, 16), // 將 hex 字符串轉為數字
      x: 0,        // 圖片在設備上的 X 座標
      y: 0,        // 圖片在設備上的 Y 座標
      templateRotation // 模板旋轉角度，轉換時會反向旋轉
    };

    // 執行轉換
    const result = await epdConversion.convertImageToEPDRawData(canvas, options);

    if (result.success) {
      return Array.from(result.rawdata); // 轉為數組便於 JSON 傳輸
    } else {
      console.error('EPD 轉換失敗:', result.error);
      return null;
    }
  } catch (error) {
    console.error('生成 rawdata 時發生錯誤:', error);
    return null;
  }
}

// 共享的資料庫連接
let getDbConnection = null;

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

/**
 * 獲取設備集合
 * @returns {Promise<{collection, client}>} 設備集合和資料庫客戶端
 */
const getDeviceCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('devices');
  return { collection, client };
};

/**
 * 獲取網關集合
 * @returns {Promise<{collection, client}>} 網關集合和資料庫客戶端
 */
const getGatewayCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('gateways');
  return { collection, client };
};

/**
 * 獲取模板集合
 * @returns {Promise<{collection, client}>} 模板集合和資料庫客戶端
 */
const getTemplateCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('templates');
  return { collection, client };
};

/**
 * 獲取門店集合
 * @returns {Promise<{collection, client}>} 門店集合和資料庫客戶端
 */
const getStoreCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('stores');
  return { collection, client };
};

/**
 * 獲取資料欄位集合
 * @returns {Promise<{collection, client}>} 資料欄位集合和資料庫客戶端
 */
const getDataFieldCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('dataFields');
  return { collection, client };
};

/**
 * 發送單個設備預覽圖到其主要網關
 * @param {string} deviceId 設備ID
 * @param {Object} options 可選參數
 * @param {boolean} options.sendToAllGateways 是否發送到所有發現此設備的網關
 * @returns {Promise<Object>} 結果
 */
const sendDevicePreviewToGateway = async (deviceId, options = {}) => {
  try {
    // 獲取設備資料
    const { collection: deviceCollection } = await getDeviceCollection();
    const { collection: gatewayCollection } = await getGatewayCollection();

    // 確保 deviceId 是有效的 ObjectId 格式
    if (!deviceId) {
      throw new Error(`設備ID不能為空`);
    }

    let objectId;
    try {
      objectId = safeObjectId(deviceId);
    } catch (error) {
      throw new Error(`無效的設備ID格式: ${deviceId}`);
    }
    const device = await deviceCollection.findOne({ _id: objectId });
    if (!device) {
      throw new Error(`找不到ID為 ${deviceId} 的設備`);
    }

    // 檢查設備是否有綁定的數據
    if (!device.dataBindings && !device.dataId) {
      throw new Error(`設備 ${deviceId} 沒有綁定數據`);
    }

    // 每次發送前都重新生成預覽圖，確保使用最新的數據
    // 不再檢查設備是否有預覽圖，而是始終重新生成
    console.log(`為設備 ${deviceId} 重新生成預覽圖...`);

      // 檢查設備是否有模板和綁定數據
      if (device.templateId && device.dataBindings) {
        try {
          // 獲取模板
          const { collection: templateCollection } = await getTemplateCollection();

          let template;

          // 嘗試使用不同的查詢方式找到模板
          console.log(`嘗試查找模板，設備ID: ${deviceId}, 模板ID: ${device.templateId}, 類型: ${typeof device.templateId}`);

          // 嘗試方法1: 使用 _id 查詢 (如果是有效的 ObjectId)
          if (ObjectId.isValid(device.templateId)) {
            console.log(`模板ID ${device.templateId} 是有效的 ObjectId 格式，使用 _id 查詢`);
            try {
              // 使用安全的 ObjectId 創建函數
              const templateObjectId = safeObjectId(device.templateId);
              template = await templateCollection.findOne({ _id: templateObjectId });
              console.log(`使用 _id 查詢結果: ${template ? '找到模板' : '未找到模板'}`);
            } catch (error) {
              console.warn(`創建模板ObjectId失敗:`, error.message);
            }
          }

          // 嘗試方法2: 使用 id 字段查詢 (字符串形式)
          if (!template) {
            console.log(`嘗試使用 id 字段(字符串)查詢模板: ${device.templateId}`);
            template = await templateCollection.findOne({ id: device.templateId });
            console.log(`使用 id 字段(字符串)查詢結果: ${template ? '找到模板' : '未找到模板'}`);
          }

          // 嘗試方法3: 使用 id 字段查詢 (數字形式)
          if (!template && !isNaN(device.templateId)) {
            const numericId = Number(device.templateId);
            console.log(`嘗試將模板ID轉換為數字: ${numericId}`);
            template = await templateCollection.findOne({ id: numericId });
            console.log(`使用 id 字段(數字)查詢結果: ${template ? '找到模板' : '未找到模板'}`);
          }

          // 嘗試方法4: 使用 templateId 字段查詢
          if (!template) {
            console.log(`嘗試使用 templateId 字段查詢`);
            template = await templateCollection.findOne({ templateId: device.templateId });
            console.log(`使用 templateId 字段查詢結果: ${template ? '找到模板' : '未找到模板'}`);

            // 如果還是找不到，嘗試將 templateId 轉換為數字再查詢
            if (!template && !isNaN(device.templateId)) {
              const numericId = Number(device.templateId);
              template = await templateCollection.findOne({ templateId: numericId });
              console.log(`使用 templateId 字段(數字)查詢結果: ${template ? '找到模板' : '未找到模板'}`);
            }
          }

          // 如果還是找不到，嘗試查詢所有模板並打印出來，幫助診斷
          if (!template) {
            console.log(`無法找到模板，嘗試列出所有模板以診斷問題`);
            const allTemplates = await templateCollection.find({}).limit(10).toArray();
            console.log(`數據庫中的前10個模板:`, JSON.stringify(allTemplates.map(t => ({
              _id: t._id.toString(),
              id: t.id,
              templateId: t.templateId,
              name: t.name
            })), null, 2));

            // 嘗試查詢模板的所有字段名稱
            if (allTemplates.length > 0) {
              console.log(`模板集合中的字段名稱:`, Object.keys(allTemplates[0]));
            }
          }

          if (!template) {
            throw new Error(`找不到設備 ${deviceId} 使用的模板 ${device.templateId}`);
          }

          // 確保模板數據包含必要的屬性
          if (!template.width || !template.height) {
            console.log(`模板 ${device.templateId} 缺少尺寸信息，嘗試從 screenSize 解析...`);

            // 嘗試從 screenSize 解析寬度和高度
            if (template.screenSize) {
              const sizeMatch = template.screenSize.match(/(\d+)x(\d+)/);
              if (sizeMatch && sizeMatch.length === 3) {
                template.width = parseInt(sizeMatch[1], 10);
                template.height = parseInt(sizeMatch[2], 10);
                console.log(`從 screenSize 解析出尺寸: ${template.width}x${template.height}`);
              }
            }

            // 如果仍然沒有尺寸信息，設置默認值
            if (!template.width || !template.height) {
              console.log(`無法從 screenSize 解析尺寸，使用默認值 296x128`);
              template.width = 296;
              template.height = 128;
            }
          }

          // 確保模板有 elements 屬性
          if (!template.elements || !Array.isArray(template.elements)) {
            console.log(`模板 ${device.templateId} 缺少 elements 屬性，初始化為空數組`);
            template.elements = [];
          }

          // 確保模板有 color 屬性
          if (!template.color) {
            console.log(`模板 ${device.templateId} 缺少 color 屬性，設置為默認值 'bwr'`);
            template.color = 'bwr';
          }

          console.log(`處理後的模板數據:`, {
            id: template.id,
            width: template.width,
            height: template.height,
            elementsCount: template.elements.length,
            color: template.color
          });

          // 獲取門店數據
          const { collection: storeCollection } = await getStoreCollection();
          // 查詢設備所屬的門店
          const store = await storeCollection.findOne({ id: device.storeId });

          // 準備門店數據數組
          let storeData = [];

          if (store) {
            // 將門店添加到數組中
            storeData.push(store);

            // 如果門店有 storeSpecificData，確保它可以被正確處理
            if (store.storeSpecificData && Array.isArray(store.storeSpecificData)) {
              console.log(`找到門店 ${device.storeId} 的 storeSpecificData，共 ${store.storeSpecificData.length} 項`);
            } else {
              console.log(`門店 ${device.storeId} 沒有 storeSpecificData 或格式不正確`);
            }
          } else {
            console.log(`找不到設備 ${deviceId} 所屬的門店 ${device.storeId}`);
          }

          // 獲取資料欄位數據
          const { collection: dataFieldCollection } = await getDataFieldCollection();
          const dataFields = await dataFieldCollection.find({}).toArray();
          console.log(`獲取到 ${dataFields.length} 個資料欄位定義`);

          // 檢查資料欄位是否包含 prefix
          const fieldsWithPrefix = dataFields.filter(field => field.prefix);
          console.log(`其中 ${fieldsWithPrefix.length} 個欄位有 prefix 定義`);

          // 輸出一些欄位的 prefix 示例
          if (fieldsWithPrefix.length > 0) {
            console.log('欄位 prefix 示例:');
            fieldsWithPrefix.slice(0, 3).forEach(field => {
              console.log(`- 欄位 ${field.id}: prefix = "${field.prefix}"`);
            });
          }

          // 處理數據綁定 - 確保它是一個對象而不是字符串
          let dataBindings = device.dataBindings;
          if (typeof dataBindings === 'string') {
            try {
              dataBindings = JSON.parse(dataBindings);
              console.log(`成功將數據綁定從字符串解析為對象，字段數: ${Object.keys(dataBindings).length}`);
            } catch (error) {
              console.error(`解析數據綁定字符串失敗:`, error);
              throw new Error(`無法解析數據綁定: ${error.message}`);
            }
          } else if (!dataBindings) {
            // 如果沒有數據綁定，創建一個空對象
            dataBindings = {};
            console.log(`設備沒有數據綁定，使用空對象`);
          }

          // 使用前端完全相同的渲染邏輯生成預覽圖
          console.log(`使用前端完全相同的渲染邏輯為設備 ${deviceId} 生成預覽圖...`);
          console.log(`數據綁定類型: ${typeof dataBindings}, 字段數: ${Object.keys(dataBindings).length}`);

          // 直接使用 regeneratePreviewBeforeSend 函數，與前端 src/utils/previewImageManager.ts 中的完全一致
          // 確保每次發送前都重新生成最新的預覽圖
          const previewData = await previewService.regeneratePreviewBeforeSend(
            {
              _id: deviceId,
              templateId: template.id,
              dataBindings: dataBindings,
              storeId: device.storeId
            },
            storeData,
            template,
            dataFields
          );

          // 如果直接渲染失敗，嘗試使用預覽服務
          if (!previewData) {
            console.log(`直接渲染失敗，嘗試使用預覽服務...`);
            try {
              const servicePreviewData = await previewService.generatePreviewFromService(
                template,
                dataBindings,
                storeData,
                { effectType: 'blackAndWhite', threshold: 128 },
                dataFields
              );

              if (servicePreviewData) {
                console.log(`預覽服務成功生成預覽圖`);
                return servicePreviewData;
              }
            } catch (previewServiceError) {
              console.error(`預覽服務調用失敗，錯誤:`, previewServiceError.message);
              console.log(`預覽服務不可用，無法使用預覽服務生成預覽圖`);
            }

            // 如果直接渲染和預覽服務都失敗，拋出錯誤
            throw new Error(`無法生成預覽圖：直接渲染和預覽服務都失敗了`);
          }

          if (previewData) {
            // 更新設備的預覽圖
            await deviceCollection.updateOne(
              { _id: objectId },
              { $set: { previewImage: previewData } }
            );

            // 更新本地設備對象
            device.previewImage = previewData;
            console.log(`已成功為設備 ${deviceId} 生成預覽圖`);
          } else {
            throw new Error(`預覽服務未能生成預覽圖`);
          }
        } catch (error) {
          console.error(`無法為設備 ${deviceId} 生成預覽圖:`, error);
          throw new Error(`無法生成預覽圖: ${error.message}`);
        }
      } else {
        throw new Error(`設備 ${deviceId} 缺少模板或綁定數據，無法生成預覽圖`);
      }

    // 檢查設備是否有主要網關
    if (!device.primaryGatewayId) {
      throw new Error(`設備 ${deviceId} 沒有主要網關`);
    }

    // 確保 primaryGatewayId 是有效的 ObjectId 對象
    let primaryGatewayObjectId;
    let primaryGatewayId;

    // 處理不同類型的 primaryGatewayId
    if (typeof device.primaryGatewayId === 'string') {
      // 如果是字符串，檢查並轉換為 ObjectId
      if (!ObjectId.isValid(device.primaryGatewayId)) {
        console.log(`警告: 設備 ${deviceId} 的主要網關ID不是有效的ObjectId格式: ${device.primaryGatewayId}`);

        // 嘗試查詢網關集合，看是否有匹配的 id 字段
        const gatewayWithId = await gatewayCollection.findOne({ id: device.primaryGatewayId });
        if (gatewayWithId) {
          console.log(`找到了使用id字段匹配的網關: ${gatewayWithId._id}`);
          primaryGatewayObjectId = gatewayWithId._id;
          primaryGatewayId = device.primaryGatewayId;
        } else {
          throw new Error(`設備 ${deviceId} 的主要網關ID格式無效且找不到匹配的網關: ${device.primaryGatewayId}`);
        }
      } else {
        try {
          // 使用安全的 ObjectId 創建函數
          primaryGatewayObjectId = safeObjectId(device.primaryGatewayId);
          primaryGatewayId = device.primaryGatewayId.toString();
        } catch (error) {
          throw new Error(`設備 ${deviceId} 的主要網關ID格式無效: ${error.message}`);
        }
      }
    } else if (device.primaryGatewayId instanceof ObjectId) {
      // 如果已經是 ObjectId 實例，直接使用
      primaryGatewayObjectId = device.primaryGatewayId;
      primaryGatewayId = device.primaryGatewayId.toString();
    } else {
      console.log(`警告: 設備 ${deviceId} 的主要網關ID類型異常: ${typeof device.primaryGatewayId}, 值: ${device.primaryGatewayId}`);

      // 嘗試將值轉換為字符串，然後檢查是否是有效的 ObjectId
      const gatewayIdStr = String(device.primaryGatewayId);
      if (ObjectId.isValid(gatewayIdStr)) {
        console.log(`成功將異常類型的網關ID轉換為有效的ObjectId字符串: ${gatewayIdStr}`);
        try {
          primaryGatewayObjectId = safeObjectId(gatewayIdStr);
          primaryGatewayId = gatewayIdStr;
        } catch (error) {
          console.warn(`創建網關ObjectId失敗:`, error.message);
        }
      } else {
        // 最後嘗試查詢網關集合
        const gatewayWithId = await gatewayCollection.findOne({ id: gatewayIdStr });
        if (gatewayWithId) {
          console.log(`找到了使用id字段匹配的網關: ${gatewayWithId._id}`);
          primaryGatewayObjectId = gatewayWithId._id;
          primaryGatewayId = gatewayIdStr;
        } else {
          throw new Error(`設備 ${deviceId} 的主要網關ID類型無效且無法轉換: ${typeof device.primaryGatewayId}`);
        }
      }
    }

    // 引入 websocketService
    const websocketService = require('./websocketService');

    // 檢查主要網關是否在線
    if (!websocketService.isGatewayOnline(primaryGatewayId)) {
      throw new Error(`設備 ${deviceId} 的主要網關 ${primaryGatewayId} 不在線`);
    }

    // 獲取網關資料
    const primaryGateway = await gatewayCollection.findOne({ _id: primaryGatewayObjectId });
    if (!primaryGateway) {
      throw new Error(`找不到ID為 ${primaryGatewayId} 的網關`);
    }
      // 準備發送資訊    // 計算 imageData 的 CRC 驗證碼
    const calculateCRC = (data) => {
      // 簡單的 CRC32 實現，確保與前端計算邏輯一致
      let crc = 0;
      const str = typeof data === 'string' ? data : JSON.stringify(data);
      for(let i = 0; i < str.length; i++) {
        crc = ((crc << 5) + crc) ^ str.charCodeAt(i);
      }
      // 轉換為 4 字節的十六進制字符串
      return (crc >>> 0).toString(16).padStart(8, '0');
    };

    // 準備 imageData
    const imageData = device.previewImage;

    // 檢查 imageData 是否有效
    if (!imageData) {
      throw new Error(`設備 ${deviceId} 的預覽圖數據為空`);
    }

    // 確保 imageData 是字符串
    const imageDataStr = typeof imageData === 'string' ? imageData : JSON.stringify(imageData);

    // 計算 imageCode
    const imageCode = calculateCRC(imageDataStr);

    // 檢查設備當前的 imageCode 是否與新計算的相同
    // 注意：imageCode 存儲在 device.data.imageCode 中
    const currentImageCode = device.data?.imageCode;
    const imageUpdateStatus = device.imageUpdateStatus;

    // 如果設備狀態為"未更新"，則不管 imageCode 是否相同都要發送圖片
    if (imageUpdateStatus !== '未更新' && currentImageCode && currentImageCode === imageCode) {
      console.log(`設備 ${deviceId} 的圖片內容未變化，imageCode 相同: ${imageCode}，且狀態不是"未更新"，跳過發送`);
      return {
        success: true,
        deviceId,
        skipped: true,
        message: '圖片內容未變化，跳過發送',
        timestamp: new Date().toISOString()
      };
    }

    // 如果狀態為"未更新"但 imageCode 相同，記錄日誌
    if (imageUpdateStatus === '未更新' && currentImageCode && currentImageCode === imageCode) {
      console.log(`設備 ${deviceId} 的圖片內容未變化，但狀態為"未更新"，將強制發送圖片`);
    } else {
      console.log(`設備 ${deviceId} 的圖片內容有變化或首次發送，舊 imageCode: ${currentImageCode || '無'}，新 imageCode: ${imageCode}，將發送更新`);
    }

    // 更新設備的 imageCode 並將 imageUpdateStatus 設置為"未更新"
    try {
      // 更新 imageCode 和 imageUpdateStatus
      // 注意：imageCode 存儲在 data.imageCode 中
      await deviceCollection.updateOne(
        { _id: objectId },
        {
          $set: {
            'data.imageCode': imageCode,
            'imageUpdateStatus': '未更新',
            updatedAt: new Date()
          }
        }
      );

      console.log(`已將 imageCode 保存到設備數據中: ${imageCode}，並將 imageUpdateStatus 設置為"未更新"`);
    } catch (error) {
      console.error('保存 imageCode 和 imageUpdateStatus 到設備數據失敗:', error);
      // 繼續執行，不因保存失敗而中斷整個流程
    }

    // 獲取模板信息以確定旋轉角度
    let template = null;
    try {
      const { collection: templateCollection } = await getTemplateCollection();

      // 使用之前已經查找到的模板邏輯
      if (ObjectId.isValid(device.templateId)) {
        const templateObjectId = safeObjectId(device.templateId);
        template = await templateCollection.findOne({ _id: templateObjectId });
      }

      if (!template) {
        template = await templateCollection.findOne({ id: device.templateId });
      }

      if (!template && !isNaN(device.templateId)) {
        const numericId = Number(device.templateId);
        template = await templateCollection.findOne({ id: numericId });
      }
    } catch (templateError) {
      console.warn('獲取模板信息失敗，將不包含 rawdata:', templateError.message);
    }

    // 生成 rawdata
    const rawdata = await generateRawData(imageDataStr, device, imageCode, template);

    const message = {
      type: 'update_preview',
      deviceMac: device.macAddress,
      imageData: imageDataStr, // 確保是字符串格式
      rawdata: rawdata, // 新增：包含 ImageInfo + 像素數據
      imageCode: imageCode, // 添加計算的 CRC 校驗碼
      timestamp: new Date().toISOString()
    };

    // 發送到主要網關
    const primarySendResult = await websocketService.sendCommandToGateway(primaryGatewayId, message);

    // 記錄事件
    try {
      await websocketService.logDeviceEvent(objectId, 'preview_sent', {
        gatewayId: primaryGatewayId,
        gatewayName: primaryGateway.name || '未知',
        success: primarySendResult.success,
        message: primarySendResult.message || primarySendResult.error || ''
      });
    } catch (logError) {
      console.error(`記錄設備事件失敗:`, logError);
      // 繼續執行，不因記錄失敗而中斷整個流程
    }

    // 如需發送到其他網關
    let otherResults = [];
    if (options.sendToAllGateways && Array.isArray(device.otherGateways) && device.otherGateways.length > 0) {
      for (const otherGateway of device.otherGateways) {
        try {
          // 確保 otherGateway 是有效的 ObjectId 對象或字符串
          let otherGatewayObjectId;
          let gatewayIdStr;

          if (typeof otherGateway === 'string') {
            // 如果是字符串，檢查並轉換為 ObjectId
            if (!ObjectId.isValid(otherGateway)) {
              console.log(`警告: 其他網關ID不是有效的ObjectId格式: ${otherGateway}`);

              // 嘗試查詢網關集合，看是否有匹配的 id 字段
              const gatewayWithId = await gatewayCollection.findOne({ id: otherGateway });
              if (gatewayWithId) {
                console.log(`找到了使用id字段匹配的其他網關: ${gatewayWithId._id}`);
                otherGatewayObjectId = gatewayWithId._id;
                gatewayIdStr = otherGateway;
              } else {
                console.warn(`跳過無效的其他網關ID格式且找不到匹配的網關: ${otherGateway}`);
                continue;
              }
            } else {
              // 使用安全的 ObjectId 創建函數
              try {
                otherGatewayObjectId = safeObjectId(otherGateway);
                gatewayIdStr = otherGateway.toString();
              } catch (error) {
                console.warn(`創建其他網關ObjectId失敗:`, error.message);
                continue;
              }
            }
          } else if (otherGateway instanceof ObjectId) {
            // 如果已經是 ObjectId 實例，直接使用
            otherGatewayObjectId = otherGateway;
            gatewayIdStr = otherGateway.toString();
          } else {
            console.log(`警告: 其他網關ID類型異常: ${typeof otherGateway}, 值: ${otherGateway}`);

            // 嘗試將值轉換為字符串，然後檢查是否是有效的 ObjectId
            const otherGatewayIdStr = String(otherGateway);
            if (ObjectId.isValid(otherGatewayIdStr)) {
              console.log(`成功將異常類型的其他網關ID轉換為有效的ObjectId字符串: ${otherGatewayIdStr}`);
              try {
                otherGatewayObjectId = safeObjectId(otherGatewayIdStr);
                gatewayIdStr = otherGatewayIdStr;
              } catch (error) {
                console.warn(`創建其他網關ObjectId失敗:`, error.message);
                continue;
              }
            } else {
              // 最後嘗試查詢網關集合
              const gatewayWithId = await gatewayCollection.findOne({ id: otherGatewayIdStr });
              if (gatewayWithId) {
                console.log(`找到了使用id字段匹配的其他網關: ${gatewayWithId._id}`);
                otherGatewayObjectId = gatewayWithId._id;
                gatewayIdStr = otherGatewayIdStr;
              } else {
                console.warn(`跳過無效的其他網關ID類型且無法轉換: ${typeof otherGateway}`);
                continue;
              }
            }
          }

          // 檢查其他網關是否在線
          if (websocketService.isGatewayOnline(gatewayIdStr)) {
            const otherGatewayData = await gatewayCollection.findOne({ _id: otherGatewayObjectId });
            if (otherGatewayData) {
              const otherSendResult = await websocketService.sendCommandToGateway(gatewayIdStr, message);
              otherResults.push({
                gatewayId: gatewayIdStr,
                gatewayName: otherGatewayData.name || '未知',
                success: otherSendResult.success,
                message: otherSendResult.message || otherSendResult.error || ''
              });

              // 記錄事件
              try {
                await websocketService.logDeviceEvent(objectId, 'preview_sent', {
                  gatewayId: gatewayIdStr,
                  gatewayName: otherGatewayData.name || '未知',
                  success: otherSendResult.success,
                  message: otherSendResult.message || otherSendResult.error || '',
                  isPrimaryGateway: false
                });
              } catch (logError) {
                console.error(`記錄設備事件失敗:`, logError);
                // 繼續執行，不因記錄失敗而中斷整個流程
              }
            }
          }
        } catch (otherGatewayError) {
          console.error(`處理其他網關時發生錯誤:`, otherGatewayError);
          // 繼續處理下一個網關，不中斷整個流程
        }
      }
    }

    // 回傳結果
    return {
      success: primarySendResult.success,
      deviceId,
      deviceMac: device.macAddress,
      primaryGateway: {
        gatewayId: primaryGatewayId,
        gatewayName: primaryGateway.name || '未知',
        success: primarySendResult.success,
        message: primarySendResult.message || primarySendResult.error || ''
      },
      otherGateways: otherResults,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error(`發送設備 ${deviceId} 預覽圖到網關失敗:`, error);
    return {
      success: false,
      deviceId,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 批量發送多個設備預覽圖到其主要網關
 * @param {string[]} deviceIds 設備ID陣列
 * @param {Object} options 可選參數
 * @param {boolean} options.sendToAllGateways 是否發送到所有發現此設備的網關
 * @returns {Promise<Object>} 結果
 */
const sendMultipleDevicePreviewsToGateways = async (deviceIds, options = {}) => {
  const results = [];
  const failedDevices = [];
  const successDevices = [];

  // 逐一處理每個設備
  for (const deviceId of deviceIds) {
    try {
      const result = await sendDevicePreviewToGateway(deviceId, options);
      results.push(result);

      if (result.success) {
        successDevices.push(deviceId);
      } else {
        failedDevices.push({ deviceId, reason: result.error });
      }
    } catch (error) {
      console.error(`處理設備 ${deviceId} 失敗:`, error);
      results.push({
        success: false,
        deviceId,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      failedDevices.push({ deviceId, reason: error.message });
    }
  }

  // 回傳整體結果
  return {
    success: failedDevices.length === 0,
    totalCount: deviceIds.length,
    successCount: successDevices.length,
    failedCount: failedDevices.length,
    detailResults: results,
    successDevices,
    failedDevices,
    timestamp: new Date().toISOString()
  };
};

module.exports = {
  initDB,
  sendDevicePreviewToGateway,
  sendMultipleDevicePreviewsToGateways,
  getStoreCollection,
  getDataFieldCollection
};
