import React, { useState, useRef, useEffect } from 'react';
import { Search, Upload, X, Image, ExternalLink, ChevronLeft, ChevronRight } from 'lucide-react';
import { buildEndpointUrl } from '../../../utils/api/apiConfig';

interface ImageSelectorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (imageUrl: string) => void;
  currentUrl?: string;
}

interface FileItem {
  _id: string;
  filename: string;
  size: number;
}

// API 服務重用來自 ResourceManagementTab 的邏輯
const apiService = {  async uploadFile(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    
    try {
      const response = await fetch(buildEndpointUrl('files', 'upload'), {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error(`上傳失敗: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('上傳文件時出錯:', error);
      throw error;
    }
  },
    async getFiles() {
    try {
      const response = await fetch(buildEndpointUrl('files'));
      
      if (!response.ok) {
        throw new Error(`獲取文件失敗: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('獲取文件列表時出錯:', error);
      return [];
    }
  }
};

export const ImageSelectorModal: React.FC<ImageSelectorModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  currentUrl
}) => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFileId, setSelectedFileId] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>(currentUrl || '');
  const [uploadMode, setUploadMode] = useState<'local' | 'resource'>('resource');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  
  // 分頁相關狀態 - 修改預設每頁顯示為6個
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(6); // 改為預設6張圖片
  const [jumpToPage, setJumpToPage] = useState('');
  
  // 獲取資源庫中的圖片
  useEffect(() => {
    if (isOpen) {
      const loadFiles = async () => {
        try {
          const fetchedFiles = await apiService.getFiles();
          setFiles(fetchedFiles);
        } catch (error) {
          console.error('加載文件列表時出錯:', error);
        }
      };
      
      loadFiles();
    }
  }, [isOpen]);
  
  // 處理本地文件選擇
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (!selectedFile) return;
    
    // 創建本地預覽 URL
    const localPreviewUrl = URL.createObjectURL(selectedFile);
    setPreviewUrl(localPreviewUrl);
    
    // 顯示上傳中狀態
    setIsUploading(true);
    
    try {
      // 上傳文件
      await apiService.uploadFile(selectedFile);
      
      // 刷新文件列表
      const updatedFiles = await apiService.getFiles();
      setFiles(updatedFiles);
      
      // 找到剛上傳的文件
      const uploadedFile = updatedFiles.find((file: FileItem) => file.filename === selectedFile.name);      if (uploadedFile) {
        setSelectedFileId(uploadedFile._id);
        setPreviewUrl(buildEndpointUrl('files', uploadedFile._id));
      }
      
      setIsUploading(false);
    } catch (error) {
      console.error('上傳圖片失敗:', error);
      setIsUploading(false);
    }
  };
    // 選擇資源庫中的圖片
  const handleSelectResource = (fileId: string) => {
    setSelectedFileId(fileId);
    setPreviewUrl(buildEndpointUrl('files', fileId));
  };
  
  // 確認選擇
  const handleConfirm = () => {
    if (previewUrl) {
      onSelect(previewUrl);
      onClose();
    }
  };
  
  // 過濾檔案
  const filteredFiles = files.filter(file => 
    file.filename.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 計算總頁數，確保至少有一頁
  const totalPages = Math.max(1, Math.ceil(filteredFiles.length / itemsPerPage));
  
  // 獲取當前頁的文件
  const currentFiles = filteredFiles.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // 頁面切換函數
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  
  // 處理跳頁操作
  const handleJumpToPage = () => {
    const pageNumber = parseInt(jumpToPage);
    if (!isNaN(pageNumber) && pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      setJumpToPage('');
    } else if (totalPages > 0) {
      alert(`請輸入有效的頁碼 (1-${totalPages})`);
    }
  };
  
  // 當文件數量變化或每頁顯示數量變化時重置當前頁
  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(1);
    }
  }, [filteredFiles.length, itemsPerPage, totalPages, currentPage]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 text-white rounded-lg p-5 w-[800px] max-w-[90%] max-h-[80vh] flex flex-col overflow-hidden">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-lg font-semibold">圖片選擇器</h3>
          <button 
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-700"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        {/* 分頁切換 */}
        <div className="flex mb-3 border-b border-gray-700">
          <button
            className={`px-4 py-1.5 ${uploadMode === 'resource' ? 'border-b-2 border-blue-500' : ''}`}
            onClick={() => setUploadMode('resource')}
          >
            資源庫
          </button>
          <button
            className={`px-4 py-1.5 ${uploadMode === 'local' ? 'border-b-2 border-blue-500' : ''}`}
            onClick={() => setUploadMode('local')}
          >
            本地上傳
          </button>
        </div>
        
        <div className="flex flex-1 gap-4 min-h-0 overflow-hidden">
          {/* 左側：圖片選擇區域 */}
          <div className="w-2/3 flex flex-col h-full overflow-hidden">
            {uploadMode === 'resource' ? (
              <>
                {/* 搜尋欄 */}
                <div className="relative mb-2 flex-shrink-0">
                  <input
                    type="text"
                    placeholder="搜尋圖片名稱..."
                    className="pl-10 pr-4 py-1.5 bg-gray-700 border border-gray-600 rounded w-full text-white"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <Search className="absolute left-3 top-2 w-4 h-4 text-gray-400" />
                </div>
                
                {/* 圖片網格容器 */}
                <div className="flex-grow overflow-hidden flex flex-col">
                  {/* 圖片網格 - 恢復固定高度確保圖片可見 */}
                  <div className="grid grid-cols-3 gap-2 overflow-y-auto h-[170px] pr-2 mb-2">
                    {currentFiles.length > 0 ? (
                      currentFiles.map((file) => (                        <div 
                          key={file._id} 
                          className={`
                            relative rounded-lg overflow-hidden cursor-pointer bg-gray-700
                            ${selectedFileId === file._id ? 'ring-2 ring-blue-500' : ''}
                            h-[80px]
                          `}
                          onClick={() => handleSelectResource(file._id)}
                        >
                          <img
                            src={buildEndpointUrl('files', file._id)}
                            alt={file.filename}
                            className="w-full h-full object-cover"
                            loading="lazy"
                          />
                          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 p-0.5">
                            <p className="text-xs text-white truncate">{file.filename}</p>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="col-span-3 flex items-center justify-center p-8 text-gray-400">
                        <p>無圖片資源，請先上傳</p>
                      </div>
                    )}
                  </div>
                  
                  {/* 分頁控制 - 重新設計為單行布局 */}
                  <div className="flex-shrink-0 bg-gray-700 p-2 rounded">
                    {/* 整合分頁信息和控制到同一行 */}
                    <div className="flex flex-wrap items-center justify-between">
                      {/* 每頁顯示和頁面信息 */}
                      <div className="flex items-center space-x-2 mr-2">
                        <span className="text-xs text-gray-300">每頁：</span>
                        <select
                          value={itemsPerPage}
                          onChange={(e) => {
                            setItemsPerPage(Number(e.target.value));
                            setCurrentPage(1); // 重置到第一頁
                          }}
                          className="bg-gray-600 border border-gray-500 rounded px-1 py-0.5 text-xs"
                        >
                          <option value={6}>6</option>
                          <option value={9}>9</option>
                          <option value={12}>12</option>
                        </select>
                        <span className="text-xs text-gray-300">
                          {filteredFiles.length > 0 ? 
                            `${(currentPage - 1) * itemsPerPage + 1}-${Math.min(currentPage * itemsPerPage, filteredFiles.length)}/${filteredFiles.length}` : 
                            '無項目'
                          }
                        </span>
                      </div>
                      
                      {/* 分頁按鈕 - 直接接在頁面信息後面 */}
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => goToPage(1)}
                          disabled={currentPage === 1}
                          className="px-1 py-0.5 border border-gray-600 rounded text-xs hover:bg-gray-600"
                          style={{
                            color: currentPage === 1 ? '#6b7280' : 'inherit',
                            cursor: currentPage === 1 ? 'not-allowed' : 'pointer'
                          }}
                        >
                          首頁
                        </button>
                        
                        <button
                          onClick={() => goToPage(currentPage - 1)}
                          disabled={currentPage === 1}
                          className="p-0.5 border border-gray-600 rounded hover:bg-gray-600"
                          style={{
                            color: currentPage === 1 ? '#6b7280' : 'inherit',
                            cursor: currentPage === 1 ? 'not-allowed' : 'pointer'
                          }}
                        >
                          <ChevronLeft size={12} />
                        </button>
                        
                        {/* 頁碼按鈕 */}
                        {Array.from({ length: Math.min(3, totalPages) }, (_, index) => {
                          let pageNum;
                          
                          if (totalPages <= 3) {
                            pageNum = index + 1;
                          } else if (currentPage <= 2) {
                            pageNum = index + 1;
                          } else if (currentPage >= totalPages - 1) {
                            pageNum = totalPages - 2 + index;
                          } else {
                            pageNum = currentPage - 1 + index;
                          }
                          
                          return (
                            <button
                              key={pageNum}
                              onClick={() => goToPage(pageNum)}
                              className="min-w-[22px] h-5 border border-gray-600 rounded flex items-center justify-center text-xs"
                              style={{
                                backgroundColor: currentPage === pageNum ? '#2563eb' : 'transparent',
                                color: currentPage === pageNum ? 'white' : 'inherit'
                              }}
                            >
                              {pageNum}
                            </button>
                          );
                        })}
                        
                        <button
                          onClick={() => goToPage(currentPage + 1)}
                          disabled={currentPage === totalPages}
                          className="p-0.5 border border-gray-600 rounded hover:bg-gray-600"
                          style={{
                            color: currentPage === totalPages ? '#6b7280' : 'inherit',
                            cursor: currentPage === totalPages ? 'not-allowed' : 'pointer'
                          }}
                        >
                          <ChevronRight size={12} />
                        </button>
                        
                        <button
                          onClick={() => goToPage(totalPages)}
                          disabled={currentPage === totalPages}
                          className="px-1 py-0.5 border border-gray-600 rounded text-xs hover:bg-gray-600"
                          style={{
                            color: currentPage === totalPages ? '#6b7280' : 'inherit',
                            cursor: currentPage === totalPages ? 'not-allowed' : 'pointer'
                          }}
                        >
                          末頁
                        </button>
                      </div>
                      
                      {/* 跳頁輸入框 */}
                      <div className="flex items-center space-x-1 mt-1.5 w-full justify-end">
                        <span className="text-xs text-gray-300">跳至</span>
                        <input
                          type="text"
                          value={jumpToPage}
                          onChange={(e) => {
                            const value = e.target.value.replace(/\D/g, '');
                            setJumpToPage(value);
                          }}
                          className="w-8 bg-gray-600 border border-gray-500 rounded px-1 py-0.5 text-xs"
                          placeholder={currentPage.toString()}
                        />
                        <span className="text-xs text-gray-300">頁</span>
                        <button
                          onClick={handleJumpToPage}
                          className="px-1.5 py-0.5 bg-blue-600 text-white rounded hover:bg-blue-700 text-xs"
                        >
                          確定
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center h-full border-2 border-dashed border-gray-600 rounded-lg p-8">
                <Image className="w-16 h-16 text-gray-500 mb-4" />
                <p className="mb-4 text-gray-400">點擊下方按鈕上傳本地圖片</p>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
                  disabled={isUploading}
                >
                  <Upload className="w-5 h-5" />
                  {isUploading ? '上傳中...' : '選擇圖片'}
                </button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleFileSelect}
                />
              </div>
            )}
          </div>
          
          {/* 右側：預覽區域 - 縮小高度 */}
          <div className="w-1/3 flex flex-col h-full overflow-hidden">
            <h4 className="text-sm font-medium mb-2">圖片預覽</h4>
            <div className="border border-gray-700 rounded-lg flex-1 flex items-center justify-center bg-gray-900 p-2 overflow-hidden max-h-[70%]">
              {previewUrl ? (
                <img 
                  src={previewUrl} 
                  alt="預覽" 
                  className="max-w-full max-h-full object-contain"
                />
              ) : (
                <div className="text-gray-500 flex flex-col items-center">
                  <Image className="w-10 h-10 mb-1" />
                  <span className="text-xs">未選擇圖片</span>
                </div>
              )}
            </div>
            
            {previewUrl && (
              <div className="mt-1 text-xs text-gray-400 truncate">
                <span>圖片URL: {previewUrl}</span>
              </div>
            )}
            
            {currentUrl && currentUrl !== previewUrl && (
              <button
                onClick={() => {
                  setPreviewUrl(currentUrl);
                  setSelectedFileId(null);
                }}
                className="mt-1 text-xs text-blue-400 hover:text-blue-300 flex items-center gap-1"
              >
                <ExternalLink className="w-3 h-3" />
                恢復原圖片
              </button>
            )}
          </div>
        </div>
        
        {/* 按鈕區域 - 縮小間距 */}
        <div className="flex justify-end gap-2 mt-3 pt-2 border-t border-gray-700">
          <button
            onClick={onClose}
            className="px-3 py-1.5 bg-gray-700 hover:bg-gray-600 rounded text-sm"
          >
            取消
          </button>
          <button
            onClick={handleConfirm}
            disabled={!previewUrl}
            className={`px-3 py-1.5 rounded text-sm ${
              previewUrl 
                ? 'bg-blue-600 hover:bg-blue-500' 
                : 'bg-gray-700 cursor-not-allowed'
            }`}
          >
            確認選擇
          </button>
        </div>
      </div>
    </div>
  );
};