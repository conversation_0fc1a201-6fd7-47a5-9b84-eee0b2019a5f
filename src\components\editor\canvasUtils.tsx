import React from 'react';
import { TemplateElement } from '../../types';
import html2canvas from 'html2canvas';

/**
 * Utility functions for canvas operations
 */

/**
 * 計算元素旋轉後的實際邊界點
 * @param element 元素對象
 * @returns 元素的實際邊界 {minX, maxX, minY, maxY}
 */
export const calculateElementBounds = (element: TemplateElement) => {
  // 處理直線元素的特殊情況
  if (element.type === 'line') {
    // 計算直線的兩個端點
    const x1 = element.x;
    const y1 = element.y;
    const x2 = element.x + element.width;
    const y2 = element.y + element.height;

    // 返回正確的邊界（不論線段方向如何）
    return {
      minX: Math.min(x1, x2),
      maxX: Math.max(x1, x2),
      minY: Math.min(y1, y2),
      maxY: Math.max(y1, y2)
    };
  }

  // 如果元素沒有旋轉，直接返回基本邊界
  if (!element.rotation || element.rotation === 0) {
    // 對於普通元素，計算邊界
    // 處理可能的負值寬度/高度
    const minX = element.width < 0 ? element.x + element.width : element.x;
    const maxX = element.width < 0 ? element.x : element.x + element.width;
    const minY = element.height < 0 ? element.y + element.height : element.y;
    const maxY = element.height < 0 ? element.y : element.y + element.height;

    return {
      minX,
      maxX,
      minY,
      maxY
    };
  }

  // 元素的中心點
  const centerX = Math.round(element.x + element.width / 2);
  const centerY = Math.round(element.y + element.height / 2);

  // 元素的四個角點（相對於中心點）
  const halfWidth = Math.round(Math.abs(element.width) / 2);
  const halfHeight = Math.round(Math.abs(element.height) / 2);

  // 角點的相對座標 (相對於元素中心)
  const corners = [
    { x: -halfWidth, y: -halfHeight }, // 左上
    { x: halfWidth, y: -halfHeight },  // 右上
    { x: halfWidth, y: halfHeight },   // 右下
    { x: -halfWidth, y: halfHeight }   // 左下
  ];

  // 將角點旋轉並轉換為絕對座標
  const rotationRad = (element.rotation * Math.PI) / 180;
  const rotatedCorners = corners.map(corner => {
    // 旋轉公式: x' = x*cos(θ) - y*sin(θ), y' = x*sin(θ) + y*cos(θ)
    const rotatedX = corner.x * Math.cos(rotationRad) - corner.y * Math.sin(rotationRad);
    const rotatedY = corner.x * Math.sin(rotationRad) + corner.y * Math.cos(rotationRad);

    // 轉換為絕對座標
    return {
      x: Math.round(centerX + rotatedX),
      y: Math.round(centerY + rotatedY)
    };
  });

  // 計算旋轉後的最小和最大座標
  const xCoords = rotatedCorners.map(c => c.x);
  const yCoords = rotatedCorners.map(c => c.y);

  return {
    minX: Math.min(...xCoords),
    maxX: Math.max(...xCoords),
    minY: Math.min(...yCoords),
    maxY: Math.max(...yCoords)
  };
};

// Calculate screen dimensions from format string (e.g., "250x122")
export const getScreenDimensions = (sizeString: string) => {
  // Check if format is like "250x122"
  if (sizeString.includes('x')) {
    const [width, height] = sizeString.split('x').map(Number);
    return { width, height };
  }
  // Default fallback
  return { width: 250, height: 122 };
};

// Get canvas coordinates adjusted for zoom level
export const getCanvasCoordinates = (
  e: React.MouseEvent<HTMLDivElement>,
  rect: DOMRect,
  zoom: number
) => {
  const x = Math.round((e.clientX - rect.left) / (zoom / 100));
  const y = Math.round((e.clientY - rect.top) / (zoom / 100));
  return { x, y };
};

// Calculate element dimensions based on start and current points
export const calculateElementDimensions = (
  startPoint: { x: number; y: number },
  currentPoint: { x: number; y: number }
) => {
  return {
    x: Math.round(Math.min(startPoint.x, currentPoint.x)),
    y: Math.round(Math.min(startPoint.y, currentPoint.y)),
    width: Math.round(Math.abs(currentPoint.x - startPoint.x)),
    height: Math.round(Math.abs(currentPoint.y - startPoint.y))
  };
};

// 計算線條元素的維度，保持起始點不變
export const calculateLineDimensions = (
  startPoint: { x: number; y: number },
  currentPoint: { x: number; y: number }
) => {
  // 計算寬度和高度（可以為負數，表示方向）
  const width = Math.round(currentPoint.x - startPoint.x);
  const height = Math.round(currentPoint.y - startPoint.y);

  return {
    x: Math.round(startPoint.x),
    y: Math.round(startPoint.y),
    width: width,
    height: height,
    // 保存原始終點座標，用於繪製
    endX: Math.round(currentPoint.x),
    endY: Math.round(currentPoint.y)
  };
};

// 限制元素位置和大小在畫布內
export const constrainElementToCanvas = (
  element: Partial<TemplateElement>,
  canvasWidth: number,
  canvasHeight: number
): Partial<TemplateElement> => {
  const result = { ...element };

  // 檢查是否為圓形元素，需要特殊處理以保持寬高相等
  const isCircle = element.type === 'circle';

  // 限制 x 坐標不超出畫布左邊界
  if (result.x !== undefined) {
    result.x = Math.max(0, result.x);
  }

  // 限制 y 坐標不超出畫布上邊界
  if (result.y !== undefined) {
    result.y = Math.max(0, result.y);
  }

  // 限制元素右邊界不超出畫布右邊界
  if (result.x !== undefined && result.width !== undefined) {
    if (result.x + result.width > canvasWidth) {
      // 如果已經超出邊界，優先保持大小而調整位置
      if (result.width <= canvasWidth) {
        result.x = canvasWidth - result.width;
      } else {
        // 如果元素本身就比畫布寬，則調整大小並讓其左邊貼齊畫布
        result.width = canvasWidth;
        result.x = 0;

        // 如果是圓形，同時調整高度以保持圓形
        if (isCircle && result.height !== undefined) {
          result.height = result.width;
          // 重新計算 y 坐標以保持圓形在畫布內
          if (result.y !== undefined && result.y + result.height > canvasHeight) {
            result.y = Math.max(0, canvasHeight - result.height);
          }
        }
      }
    }
  }

  // 限制元素下邊界不超出畫布下邊界
  if (result.y !== undefined && result.height !== undefined) {
    if (result.y + result.height > canvasHeight) {
      // 如果已經超出邊界，優先保持大小而調整位置
      if (result.height <= canvasHeight) {
        result.y = canvasHeight - result.height;
      } else {
        // 如果元素本身就比畫布高，則調整大小並讓其上邊貼齊畫布
        result.height = canvasHeight;
        result.y = 0;

        // 如果是圓形，同時調整寬度以保持圓形
        if (isCircle && result.width !== undefined) {
          result.width = result.height;
          // 重新計算 x 坐標以保持圓形在畫布內
          if (result.x !== undefined && result.x + result.width > canvasWidth) {
            result.x = Math.max(0, canvasWidth - result.width);
          }
        }
      }
    }
  }

  // 對於圓形，最終確保寬高相等（取較小值以確保完全在畫布內）
  if (isCircle && result.width !== undefined && result.height !== undefined) {
    const maxSize = Math.min(canvasWidth, canvasHeight);
    const currentSize = Math.min(result.width, result.height);
    const finalSize = Math.min(currentSize, maxSize);

    result.width = finalSize;
    result.height = finalSize;

    // 重新調整位置確保圓形完全在畫布內
    if (result.x !== undefined && result.x + finalSize > canvasWidth) {
      result.x = canvasWidth - finalSize;
    }
    if (result.y !== undefined && result.y + finalSize > canvasHeight) {
      result.y = canvasHeight - finalSize;
    }
  }

  return result;
};

/**
 * 將畫布渲染為圖片
 * @param canvasRef 畫布的 React 引用
 * @param screenWidth 畫布真實寬度
 * @param screenHeight 畫布真實高度
 * @returns Promise<HTMLCanvasElement> 渲染後的 canvas 元素
 */
export const renderCanvasToImage = async (
  canvasRef: React.RefObject<HTMLDivElement>,
  screenWidth: number,
  screenHeight: number
): Promise<HTMLCanvasElement | null> => {
  if (!canvasRef.current) return null;

  try {
    // 保存當前狀態
    const canvasElement = canvasRef.current;
    const originalHTML = canvasElement.innerHTML;

    // 創建臨時的畫布容器，僅包含要下載的內容
    const tempContainer = document.createElement('div');
    tempContainer.innerHTML = originalHTML;
    tempContainer.style.width = `${screenWidth}px`;
    tempContainer.style.height = `${screenHeight}px`;
    tempContainer.style.position = 'relative';
    tempContainer.style.background = 'white';
    tempContainer.style.overflow = 'hidden'; // 確保所有模板都使用 overflow: hidden，防止內容溢出

    // 移除所有選擇框和控制點，但不移除圓形和橢圓元件
    // 1. 移除虛線選擇框
    const selectionBoxes = tempContainer.querySelectorAll('[style*="dashed"]');
    selectionBoxes.forEach(box => box.remove());

    // 2. 更精確地選擇控制點，避免移除圓形/橢圓元件
    // 控制點通常是小的、絕對定位的元素，且不是主要元件
    // 找到所有小尺寸的圓形元素（控制點）
    const controlHandles = tempContainer.querySelectorAll('div[style*="border-radius: 50%"][style*="width: 8px"][style*="height: 8px"]');
    controlHandles.forEach(handle => handle.remove());

    // 3. 移除旋轉控制線
    const rotationLines = tempContainer.querySelectorAll('div[style*="height: 15px"][style*="width: 1px"]');
    rotationLines.forEach(line => line.remove());

    // 將臨時容器添加到文檔中以便 html2canvas 可以處理它
    document.body.appendChild(tempContainer);
    console.log('臨時容器已添加到DOM', tempContainer);

    try {
      // 重新應用正確的樣式
      const originalElements = canvasElement.querySelectorAll('[data-element-id]');
      const tempElements = tempContainer.querySelectorAll('[data-element-id]');

      console.log('找到元素: 原始=', originalElements.length, '臨時=', tempElements.length);

      for (let i = 0; i < Math.min(originalElements.length, tempElements.length); i++) {
        const original = originalElements[i] as HTMLElement;
        const temp = tempElements[i] as HTMLElement;

        if (original && temp) {
          // 獲取原始元素的精確樣式
          const style = window.getComputedStyle(original);

          // 設置基本定位和尺寸
          temp.style.position = 'absolute';  // 保持絕對定位
          temp.style.left = style.left;
          temp.style.top = style.top;
          temp.style.width = style.width;
          temp.style.height = style.height;
          temp.style.transform = style.transform;
          temp.style.transformOrigin = style.transformOrigin || 'center center';
          temp.style.zIndex = style.zIndex;

          // 判斷是否為文字元件 (檢查內部結構特徵)
          const isTextElement = temp.querySelector('div') &&
            !temp.querySelector('svg') &&
            !temp.querySelector('img') &&
            !temp.querySelector('canvas');

          if (isTextElement) {
            console.log('修正文字元件樣式', temp);

            // 檢查是否為綁定數據的文字元件
            const hasBoundDataField = temp.getAttribute('data-has-binding') === 'true' ||
                                     temp.querySelector('[style*="dashed rgba(59, 130, 246"]') !== null;

            // 檢查是否為多行文字元素
            const isMultilineText = temp.getAttribute('data-element-type') === 'multiline-text';

            // 特殊處理文字元件，確保文字內容完全可見
            const textContainer = temp.querySelector('div');

            if (textContainer instanceof HTMLElement) {
              // 確保文字元素可見並正確顯示
              textContainer.style.visibility = 'visible';
              textContainer.style.opacity = '1';
              textContainer.style.width = '100%';
              textContainer.style.height = '100%';
              textContainer.style.overflow = 'visible';
              textContainer.style.position = 'static';

              // 根據文字元素類型設置不同的樣式
              if (isMultilineText) {
                // 多行文字樣式
                textContainer.style.whiteSpace = 'pre-wrap';
                textContainer.style.wordBreak = 'break-word';
                textContainer.style.display = 'flex';
                textContainer.style.alignItems = 'flex-start'; // 多行文字靠上對齊
                textContainer.style.justifyContent = 'flex-start';
              } else {
                // 單行文字樣式
                textContainer.style.whiteSpace = 'nowrap';
                textContainer.style.display = 'flex';
                textContainer.style.alignItems = 'center'; // 單行文字垂直居中
                textContainer.style.justifyContent = 'flex-start';
              }

              // 確保文字容器可以完全包含文字
              temp.style.overflow = 'visible';
              temp.style.padding = '0';
              temp.style.lineHeight = 'normal';

              // 特別處理綁定數據的文字元件
              if (hasBoundDataField) {
                console.log('處理綁定數據的文字元件');

                // 將綁定數據的文字元件轉換為普通文字元件
                // 保留原始顏色，不強制設置為黑色
                if (!textContainer.style.color || textContainer.style.color === 'transparent') {
                  textContainer.style.color = 'black'; // 只有在沒有顏色或透明時才設置為黑色
                }
                textContainer.style.backgroundColor = 'transparent';
                textContainer.style.border = 'none';
                textContainer.style.textOverflow = 'visible';
                textContainer.style.overflow = 'visible';
                textContainer.style.fontWeight = 'normal';
                textContainer.style.fontSize = style.fontSize;
                textContainer.style.fontFamily = style.fontFamily;

                // 移除綁定數據的標記元素
                const bindingMarker = textContainer.querySelector('div[style*="position: absolute"]');
                if (bindingMarker) {
                  bindingMarker.remove();
                }

                // 確保文字不會被截斷
                temp.style.overflow = 'visible';
              }
            }
          } else {
            // 所有元素保證可見
            temp.style.opacity = '1';
            temp.style.visibility = 'visible';
          }
        }
      }

      // 使用 html2canvas 轉換為圖片，優化設置以確保文字渲染
      const renderedCanvas = await html2canvas(tempContainer, {
        backgroundColor: '#FFFFFF',
        scale: 2, // 提高圖片質量，增加解析度有助於文字渲染
        useCORS: true, // 允許跨域圖片
        logging: false, // 關閉日誌
        width: screenWidth,
        height: screenHeight,
        allowTaint: true, // 允許污染畫布，可能改進某些情況下的文字渲染
        onclone: (clonedDoc) => {
          // 在克隆的文檔中再次確保文字元素都是可見的
          const clonedElements = clonedDoc.querySelectorAll('[data-element-id]');
          console.log('克隆文檔中找到元素數量:', clonedElements.length);

          clonedElements.forEach(element => {
            if (element instanceof HTMLElement) {
              // 確保元素是可見的
              element.style.visibility = 'visible';
              element.style.opacity = '1';
              element.style.display = 'block';

              // 檢查是否為文字元件
              const isTextElement = element.querySelector('div') &&
                !element.querySelector('svg') &&
                !element.querySelector('img') &&
                !element.querySelector('canvas');

              if (isTextElement) {
                // 檢查是否為綁定數據的文字元件
                const hasBoundDataField = element.getAttribute('data-has-binding') === 'true' ||
                                         element.querySelector('[style*="dashed rgba(59, 130, 246"]') !== null;

                // 檢查是否為多行文字元素
                const isMultilineText = element.getAttribute('data-element-type') === 'multiline-text';

                // 特別處理文字元件
                const textContainer = element.querySelector('div');
                if (textContainer instanceof HTMLElement) {
                  // 確保文字容器可見
                  textContainer.style.visibility = 'visible';
                  textContainer.style.opacity = '1';
                  textContainer.style.position = 'static';
                  textContainer.style.width = '100%';
                  textContainer.style.height = '100%';
                  textContainer.style.overflow = 'visible';

                  // 根據文字元素類型設置不同的樣式
                  if (isMultilineText) {
                    // 多行文字樣式
                    textContainer.style.whiteSpace = 'pre-wrap';
                    textContainer.style.wordBreak = 'break-word';
                    textContainer.style.display = 'flex';
                    textContainer.style.alignItems = 'flex-start'; // 多行文字靠上對齊
                    textContainer.style.justifyContent = 'flex-start';
                  } else {
                    // 單行文字樣式
                    textContainer.style.whiteSpace = 'nowrap';
                    textContainer.style.display = 'flex';
                    textContainer.style.alignItems = 'center'; // 單行文字垂直居中
                    textContainer.style.justifyContent = 'flex-start';
                  }

                  // 確保文字容器可以完全包含文字
                  element.style.overflow = 'visible';
                  element.style.padding = '0';
                  element.style.lineHeight = 'normal';

                  if (hasBoundDataField) {
                    console.log('克隆文檔中處理綁定數據的文字元件');

                    // 將綁定數據的文字元件轉換為普通文字元件
                    // 保留原始顏色，不強制設置為黑色
                    if (!textContainer.style.color || textContainer.style.color === 'transparent') {
                      textContainer.style.color = '#000000'; // 只有在沒有顏色或透明時才設置為黑色
                    }
                    textContainer.style.backgroundColor = 'transparent';
                    textContainer.style.border = 'none';
                    textContainer.style.textOverflow = 'visible';
                    textContainer.style.fontWeight = 'normal';

                    // 移除綁定數據的標記元素
                    const bindingMarker = textContainer.querySelector('div[style*="position: absolute"]');
                    if (bindingMarker) {
                      bindingMarker.remove();
                    }

                    // 如果文字內容為空，設置為預設文字
                    if (!textContainer.textContent || textContainer.textContent.trim() === '') {
                      textContainer.textContent = 'Text';
                    }
                  }
                }
              }
            }
          });
        }
      });

      // 清理臨時容器
      document.body.removeChild(tempContainer);

      console.log('圖片已成功渲染，尺寸：', screenWidth, 'x', screenHeight);
      return renderedCanvas;
    } catch (error) {
      console.error('渲染畫布時發生錯誤:', error);
      return null;
    }
  } catch (error) {
    console.error('渲染畫布時發生錯誤:', error);
    return null;
  }
};

/**
 * 下載圖片
 * @param canvas 要下載的 canvas 元素
 * @param fileName 下載的文件名
 */
export const downloadImage = (
  canvas: HTMLCanvasElement,
  fileName: string = 'template'
) => {
  if (!canvas) return;

  try {
    // 創建下載鏈接
    const link = document.createElement('a');
    link.download = `${fileName}.png`;
    link.href = canvas.toDataURL('image/png');
    link.click();

    console.log('圖片已準備下載');
  } catch (error) {
    console.error('下載圖片時發生錯誤:', error);
  }
};

/**
 * 下載畫布內容為圖片
 * @param canvasRef 畫布的 React 引用
 * @param fileName 下載的文件名
 * @param screenWidth 畫布真實寬度
 * @param screenHeight 畫布真實高度
 */
export const downloadCanvasAsImage = async (
  canvasRef: React.RefObject<HTMLDivElement>,
  fileName: string = 'template',
  screenWidth: number,
  screenHeight: number
) => {
  try {
    const renderedCanvas = await renderCanvasToImage(canvasRef, screenWidth, screenHeight);
    if (renderedCanvas) {
      downloadImage(renderedCanvas, fileName);
    }
  } catch (error) {
    console.error('下載畫布時發生錯誤:', error);
  }
};