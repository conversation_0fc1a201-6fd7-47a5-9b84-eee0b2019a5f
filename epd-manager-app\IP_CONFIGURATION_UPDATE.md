# EPD Manager App - IP 地址配置更新

## 📋 更新概述

本次更新將 EPD Manager App 的服務器設置簡化為只需要輸入 IP 地址，系統會自動添加固定端口 3001，提升用戶體驗。

## 🎯 更新目標

- **簡化配置**：用戶只需輸入 IP 地址，無需關心端口和協議
- **自動化處理**：系統自動構建完整的服務器 URL
- **持久化存儲**：保存用戶設置的 IP 地址，下次啟動自動載入
- **驗證機制**：提供 IP 地址格式驗證和連接測試

## 🔧 修改的文件

### 1. `src/screens/LoginScreen.tsx`
**主要修改**：
- 將 `serverUrl` 狀態改為 `serverIp`
- 添加 IP 地址格式驗證
- 自動構建完整的服務器 URL
- 添加 IP 地址持久化存儲
- 更新 UI 顯示和提示文字

**新增功能**：
- 自動載入保存的 IP 地址
- IP 地址格式驗證
- 連接測試時顯示完整 URL

### 2. `src/utils/generators.ts`
**新增函數**：
- `buildServerUrl(ip, port, protocol)`: 將 IP 地址轉換為完整 URL
- `extractIpFromUrl(url)`: 從完整 URL 中提取 IP 地址

### 3. `src/utils/constants.ts`
**新增常量**：
- `STORAGE_KEYS.SERVER_IP`: 用於存儲服務器 IP 地址的鍵名

### 4. `README.md`
**更新內容**：
- 更新配置說明，反映新的 IP 地址設置方式
- 添加應用內配置的使用說明

## 🚀 新功能特點

### 1. 簡化的用戶界面
```
舊版本：需要輸入完整 URL
輸入框：http://*************:3001

新版本：只需輸入 IP 地址
輸入框：*************
提示：系統將自動使用端口 3001
```

### 2. 智能 URL 構建
```typescript
// 用戶輸入：*************
// 系統自動構建：http://*************:3001

// 支持各種輸入格式：
buildServerUrl('*************')           // -> http://*************:3001
buildServerUrl('http://*************')    // -> http://*************:3001
buildServerUrl('*************:8080')      // -> http://*************:3001
```

### 3. IP 地址驗證
```typescript
// 有效的 IP 地址
isValidIpAddress('*************')  // -> true
isValidIpAddress('********')       // -> true

// 無效的 IP 地址
isValidIpAddress('192.168.1.256')  // -> false
isValidIpAddress('192.168.1')      // -> false
isValidIpAddress('localhost')      // -> false
```

### 4. 持久化存儲
- 用戶設置的 IP 地址會自動保存到本地存儲
- 下次啟動應用時自動載入並設置 API 服務
- 支持跨會話保持服務器設置

## 📱 用戶使用流程

### 首次使用
1. 打開應用，進入登入頁面
2. 點擊「顯示服務器設置」
3. 輸入服務器 IP 地址（例如：*************）
4. 點擊「測試連接」驗證服務器
5. 輸入用戶名和密碼登入

### 後續使用
1. 打開應用，系統自動載入保存的 IP 地址
2. 直接輸入用戶名和密碼登入
3. 如需更改服務器，重複首次使用的步驟

## 🔍 技術實現細節

### IP 地址驗證正則表達式
```typescript
const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
```

### URL 構建邏輯
```typescript
function buildServerUrl(ip: string, port: number = 3001, protocol: string = 'http'): string {
  // 清理輸入，移除可能的協議前綴和端口
  const cleanIp = ip.replace(/^https?:\/\//, '').replace(/:\d+$/, '');
  return `${protocol}://${cleanIp}:${port}`;
}
```

### 存儲管理
```typescript
// 保存 IP 地址
await AsyncStorage.setItem(STORAGE_KEYS.SERVER_IP, serverIp);

// 載入 IP 地址
const savedIp = await AsyncStorage.getItem(STORAGE_KEYS.SERVER_IP);
```

## ✅ 測試驗證

已通過以下測試案例：
- ✅ 有效 IP 地址驗證（*************, ********, **********）
- ✅ 無效 IP 地址拒絕（192.168.1.256, localhost, 不完整地址）
- ✅ URL 構建功能（各種輸入格式）
- ✅ IP 地址提取功能
- ✅ 持久化存儲功能

## 🎉 更新完成

EPD Manager App 現在提供更簡潔、更用戶友好的服務器配置體驗。用戶只需要知道服務器的 IP 地址即可完成配置，大大簡化了使用流程。
