@tailwind base;
@tailwind components;
@tailwind utilities;

/* 流動邊框效果 */
@keyframes flowingBorder {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.auto-mode-container {
  position: relative;
  overflow: hidden;
}

.flowing-border {
  background: linear-gradient(
    90deg,
    rgba(59, 130, 246, 0.5),
    rgba(37, 99, 235, 0.8),
    rgba(96, 165, 250, 0.5),
    rgba(59, 130, 246, 0.5)
  );
  background-size: 300% 100%;
  animation: flowingBorder 3s ease infinite;
  z-index: 1;
  pointer-events: none;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 電路板背景動畫效果 */
.circuit-background {
  animation: circuit-fade 10s infinite alternate;
}

.circuit-background circle {
  animation: circuit-pulse 4s infinite alternate;
  filter: drop-shadow(0 0 4px rgba(125, 211, 252, 0.8));
}

.circuit-background circle.animated-node {
  animation: circuit-pulse 4s infinite alternate;
  filter: drop-shadow(0 0 5px rgba(125, 211, 252, 0.9));
}

.circuit-background circle:nth-child(odd) {
  animation-delay: 1s;
}

.circuit-background circle:nth-child(3n) {
  animation-delay: 2s;
}

.circuit-background circle:nth-child(4n) {
  animation-delay: 3s;
}

/* 電子元件動畫 */
.circuit-background rect {
  animation: component-glow 6s infinite alternate;
}

.circuit-background rect:nth-child(odd) {
  animation-delay: 1s;
}

.circuit-background rect:nth-child(3n) {
  animation-delay: 2s;
}

@keyframes component-glow {
  0% {
    filter: drop-shadow(0 0 1px rgba(125, 211, 252, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 3px rgba(125, 211, 252, 0.5));
  }
  100% {
    filter: drop-shadow(0 0 1px rgba(125, 211, 252, 0.3));
  }
}

/* 電路路徑動畫 - 從中心向外流動 */
.circuit-background path.circuit-path {
  stroke-dasharray: 15;
  stroke-dashoffset: -1000; /* 負值使動畫從起點向終點流動 */
  animation: circuit-flow-outward 8s linear infinite;
  filter: drop-shadow(0 0 3px rgba(125, 211, 252, 0.7)); /* 添加發光效果 */
}

/* 主要電路路徑 - 粗線 */
.circuit-background path.circuit-path[stroke-width="3"] {
  animation-duration: 6s;
  stroke-dasharray: 12;
  filter: drop-shadow(0 0 5px rgba(125, 211, 252, 0.8)); /* 更強的發光效果 */
}

/* 次要電路路徑 - 中等線 */
.circuit-background path.circuit-path[stroke-width="2"] {
  animation-duration: 8s;
  stroke-dasharray: 10;
  filter: drop-shadow(0 0 4px rgba(125, 211, 252, 0.7)); /* 中等發光效果 */
}

/* 微型電路路徑 - 細線 */
.circuit-background path.circuit-path[stroke-width="1.5"] {
  animation-duration: 4s;
  stroke-dasharray: 5;
  filter: drop-shadow(0 0 3px rgba(125, 211, 252, 0.6)); /* 輕微發光效果 */
}

/* 交錯動畫時間 */
.circuit-background path.circuit-path:nth-child(even) {
  animation-delay: 0.5s;
}

.circuit-background path.circuit-path:nth-child(3n) {
  animation-delay: 1s;
}

.circuit-background path.circuit-path:nth-child(4n) {
  animation-delay: 1.5s;
}

@keyframes circuit-fade {
  0% {
    opacity: 0.4;
  }
  100% {
    opacity: 0.7;
  }
}

@keyframes circuit-pulse {
  0% {
    opacity: 0.6;
    r: 4;
    filter: drop-shadow(0 0 3px rgba(125, 211, 252, 0.7));
  }
  50% {
    opacity: 1;
    r: 6;
    filter: drop-shadow(0 0 8px rgba(125, 211, 252, 1));
  }
  100% {
    opacity: 0.6;
    r: 4;
    filter: drop-shadow(0 0 3px rgba(125, 211, 252, 0.7));
  }
}

@keyframes circuit-flow-outward {
  from {
    stroke-dashoffset: 0;
  }
  to {
    stroke-dashoffset: -2000;
  }
}

@keyframes pulse-wave {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0.5;
    box-shadow: 0 0 20px 10px rgba(125, 211, 252, 0.3);
  }
  50% {
    opacity: 0.3;
    box-shadow: 0 0 40px 20px rgba(125, 211, 252, 0.2);
  }
  100% {
    transform: translate(-50%, -50%) scale(1.8);
    opacity: 0;
    box-shadow: 0 0 60px 30px rgba(125, 211, 252, 0.1);
  }
}

.animate-pulse-wave {
  animation: pulse-wave 2s cubic-bezier(0.1, 0.7, 0.6, 1) forwards;
}

/* 數據流動點動畫 */
.animate-data-flow-1 {
  animation: data-flow-circle 8s infinite;
}

.animate-data-flow-2 {
  animation: data-flow-circle 8s infinite;
  animation-delay: 1s;
}

.animate-data-flow-3 {
  animation: data-flow-circle 8s infinite;
  animation-delay: 2s;
}

.animate-data-flow-4 {
  animation: data-flow-circle 8s infinite;
  animation-delay: 3s;
}

.animate-data-flow-5 {
  animation: data-flow-circle 6s infinite;
  animation-delay: 0.5s;
}

.animate-data-flow-6 {
  animation: data-flow-circle 6s infinite;
  animation-delay: 1.5s;
}

.animate-data-flow-7 {
  animation: data-flow-circle 6s infinite;
  animation-delay: 2.5s;
}

.animate-data-flow-8 {
  animation: data-flow-circle 6s infinite;
  animation-delay: 3.5s;
}

@keyframes data-flow-circle {
  0% {
    opacity: 0;
    r: 1;
  }
  20% {
    opacity: 1;
    r: 2;
  }
  80% {
    opacity: 1;
    r: 2;
  }
  100% {
    opacity: 0;
    r: 1;
  }
}
