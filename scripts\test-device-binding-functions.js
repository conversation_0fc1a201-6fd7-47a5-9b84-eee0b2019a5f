/**
 * 測試設備模型功能
 * 該腳本測試設備模型的網關關聯和用戶綁定功能
 */

// 模擬設備數據
const device = {
  _id: '123456',
  macAddress: '11:22:33:44:55:66',
  status: 'online',
  dataId: 'data123',
  storeId: 'store1',
  initialized: true,
  primaryGatewayId: '60d2f4a45d432a1f345678ab', // 主要網關ID
  otherGateways: ['60d2f4a45d432a1f345678cd'],   // 其他網關ID
  userId: '609f1c9b5f8c443e9f7654ab',           // 綁定用戶ID
  lastSeen: new Date(),
  note: '重要設備',
  data: {
    size: '10.3"',
    rssi: -75,
    battery: 85,
    imgcode: 'IMG12345'                         // 圖片編碼
  },
  createdAt: new Date(),
  updatedAt: new Date()
};

// 模擬網關數據
const gateways = [
  {
    _id: '60d2f4a45d432a1f345678ab',
    name: '主要網關',
    macAddress: '22:33:44:55:66:77',
    status: 'online',
    storeId: 'store1',
    devices: ['123456'],  // 發現的設備ID
    lastSeen: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: '60d2f4a45d432a1f345678cd',
    name: '次要網關',
    macAddress: '33:44:55:66:77:88',
    status: 'online',
    storeId: 'store1',
    devices: ['123456'],
    lastSeen: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// 模擬用戶數據
const user = {
  _id: '609f1c9b5f8c443e9f7654ab',
  username: 'testuser',
  name: '測試用戶',
  email: '<EMAIL>',
  status: 'active',
  createdAt: new Date(),
  updatedAt: new Date()
};

/**
 * 測試設備綁定用戶功能
 */
function testDeviceUserBinding() {
  console.log('===== 設備用戶綁定測試 =====');
  
  // 檢查設備是否已綁定用戶
  if (device.userId) {
    console.log(`設備 ${device.macAddress} 已綁定用戶 ${user.username}`);
  } else {
    console.log(`設備 ${device.macAddress} 未綁定用戶`);
  }
  
  // 測試綁定操作
  console.log('\n執行綁定操作...');
  const bindResult = bindDeviceToUser(device._id, user._id);
  console.log('綁定結果:', bindResult ? '成功' : '失敗');
  
  // 測試解除綁定操作
  console.log('\n執行解除綁定操作...');
  const unbindResult = unbindDeviceFromUser(device._id);
  console.log('解除綁定結果:', unbindResult ? '成功' : '失敗');
  console.log('========================\n');
}

/**
 * 測試設備網關關聯功能
 */
function testDeviceGatewayAssociation() {
  console.log('===== 設備網關關聯測試 =====');
  
  // 檢查設備的主要網關
  const primaryGateway = gateways.find(g => g._id === device.primaryGatewayId);
  if (primaryGateway) {
    console.log(`設備 ${device.macAddress} 的主要網關是 ${primaryGateway.name}(${primaryGateway.macAddress})`);
  } else {
    console.log(`設備 ${device.macAddress} 未設置主要網關`);
  }
  
  // 檢查設備的其他網關
  if (device.otherGateways.length > 0) {
    console.log(`設備 ${device.macAddress} 關聯的其他網關:`);
    device.otherGateways.forEach(gatewayId => {
      const gateway = gateways.find(g => g._id === gatewayId);
      if (gateway) {
        console.log(` - ${gateway.name}(${gateway.macAddress})`);
      }
    });
  } else {
    console.log(`設備 ${device.macAddress} 沒有其他關聯的網關`);
  }
  
  // 測試設置主要網關
  console.log('\n執行設置主要網關操作...');
  const newPrimaryGatewayId = '60d2f4a45d432a1f345678cd'; // 使用第二個網關作為新的主要網關
  const setPrimaryResult = setDevicePrimaryGateway(device._id, newPrimaryGatewayId);
  console.log('設置結果:', setPrimaryResult ? '成功' : '失敗');
  console.log('========================\n');
}

/**
 * 模擬綁定設備到用戶功能
 */
function bindDeviceToUser(deviceId, userId) {
  console.log(`  嘗試綁定設備 ${deviceId} 到用戶 ${userId}`);
  
  // 實際應用中，這裡會進行數據庫操作
  // 這裡只做模擬演示
  device.userId = userId;
  device.updatedAt = new Date();
  
  console.log(`  已將設備 ${device.macAddress} 綁定到用戶 ${user.username}`);
  return true;
}

/**
 * 模擬解除設備與用戶的綁定
 */
function unbindDeviceFromUser(deviceId) {
  console.log(`  嘗試解除設備 ${deviceId} 的用戶綁定`);
  
  // 實際應用中，這裡會進行數據庫操作
  // 這裡只做模擬演示
  if (device.userId) {
    device.userId = null;
    device.updatedAt = new Date();
    console.log(`  已解除設備 ${device.macAddress} 的用戶綁定`);
    return true;
  } else {
    console.log(`  設備 ${device.macAddress} 未綁定用戶，無需解除`);
    return false;
  }
}

/**
 * 模擬設置設備主要網關
 */
function setDevicePrimaryGateway(deviceId, gatewayId) {
  console.log(`  嘗試設置設備 ${deviceId} 的主要網關為 ${gatewayId}`);
  
  // 實際應用中，這裡會進行數據庫操作
  // 這裡只做模擬演示
  const oldPrimaryGatewayId = device.primaryGatewayId;
  
  // 設置新的主要網關
  device.primaryGatewayId = gatewayId;
  device.initialized = true;
  device.updatedAt = new Date();
  
  // 處理其他網關列表
  if (oldPrimaryGatewayId && oldPrimaryGatewayId !== gatewayId) {
    // 如果已有主要網關，將其添加到其他網關列表
    if (!device.otherGateways.includes(oldPrimaryGatewayId)) {
      device.otherGateways.push(oldPrimaryGatewayId);
    }
  }
  
  // 如果新的主要網關之前在其他網關列表中，從列表中移除
  if (device.otherGateways.includes(gatewayId)) {
    device.otherGateways = device.otherGateways.filter(id => id !== gatewayId);
  }
  
  console.log(`  已將設備 ${device.macAddress} 的主要網關設置為 ${gatewayId}`);
  return true;
}

// 執行測試
console.log('開始測試設備模型功能...\n');
testDeviceUserBinding();
testDeviceGatewayAssociation();
console.log('測試結束');
