import { Gateway } from '../../types/gateway';
import { buildEndpointUrl } from './apiConfig';
import { useAuthStore } from '../../store/authStore';

// 獲取所有網關
export async function getAllGateways(storeId?: string): Promise<Gateway[]> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建 URL，如果提供了 storeId，則添加到查詢參數中
    const url = storeId
      ? `${buildEndpointUrl('gateways')}?storeId=${encodeURIComponent(storeId)}`
      : buildEndpointUrl('gateways');

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('獲取網關列表失敗:', error);
    throw error;
  }
}

// 獲取單個網關詳情
export async function getGateway(id: string, storeId?: string): Promise<Gateway> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('gateways', id);

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`獲取網關詳情失敗 (ID: ${id}):`, error);
    throw error;
  }
}

// 新增網關
export async function createGateway(gateway: Omit<Gateway, '_id' | 'createdAt' | 'updatedAt'>): Promise<Gateway> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('gateways'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(gateway),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('新增網關失敗:', error);
    throw error;
  }
}

// 更新網關
export async function updateGateway(id: string, gateway: Partial<Gateway>, storeId?: string): Promise<Gateway> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('gateways', id);

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(gateway),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`更新網關失敗 (ID: ${id}):`, error);
    throw error;
  }
}

// 刪除網關
export async function deleteGateway(id: string, storeId?: string): Promise<void> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('gateways', id);

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
  } catch (error) {
    console.error(`刪除網關失敗 (ID: ${id}):`, error);
    throw error;
  }
}

// 同步網關狀態（獲取最新網關數據）
export async function syncGateways(storeId?: string): Promise<{success: boolean, gateways: Gateway[], count: number}> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('gateways', 'sync');

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('同步網關狀態失敗:', error);
    throw error;
  }
}

// 升級網關 WiFi 固件
export async function upgradeWifiFirmware(ids: string[], version: string, storeId?: string): Promise<boolean> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('gateways', 'upgrade-wifi');

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify({ ids, version }),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return (await response.json()).success;
  } catch (error) {
    console.error('升級網關 WiFi 固件失敗:', error);
    throw error;
  }
}

// 升級網關藍芽固件
export async function upgradeBtFirmware(ids: string[], version: string, storeId?: string): Promise<boolean> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('gateways', 'upgrade-bt');

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify({ ids, version }),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return (await response.json()).success;
  } catch (error) {
    console.error('升級網關藍芽固件失敗:', error);
    throw error;
  }
}

// 重啟網關
export async function restartGateway(id: string, storeId?: string): Promise<boolean> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    // 構建基本 URL
    let url = buildEndpointUrl('gateways', `${id}/restart`);

    // 如果提供了 storeId，則添加到查詢參數中
    if (storeId) {
      url = `${url}?storeId=${encodeURIComponent(storeId)}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      throw new Error(`API error: ${response.status}`);
    }
    return (await response.json()).success;
  } catch (error) {
    console.error(`重啟網關失敗 (ID: ${id}):`, error);
    throw error;
  }
}
