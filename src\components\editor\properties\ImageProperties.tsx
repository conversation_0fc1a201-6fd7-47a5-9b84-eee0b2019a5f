import React, { useState } from 'react';
import { TemplateElement } from '../../../types';
import { Image, Upload } from 'lucide-react';
import { ImageSelectorModal } from './ImageSelectorModal';

interface ImagePropertiesProps {
  element: TemplateElement;
  updateElement: (updates: Partial<TemplateElement>) => void;
}

export const ImageProperties: React.FC<ImagePropertiesProps> = ({
  element,
  updateElement
}) => {
  // 顯示/隱藏圖片選擇器的狀態
  const [showImageSelector, setShowImageSelector] = useState(false);

  // 只有當元素類型為圖片時才顯示
  if (element.type !== 'image') {
    return null;
  }

  // 處理圖片 URL 變更
  const handleImageUrlChange = (newImageUrl: string) => {
    updateElement({ imageUrl: newImageUrl });
  };

  // 開啟圖片選擇器
  const openImageSelector = () => {
    setShowImageSelector(true);
  };

  // 關閉圖片選擇器
  const closeImageSelector = () => {
    setShowImageSelector(false);
  };

  return (
    <>
      <div className="mb-6">
        <h4 className="text-sm font-medium mb-2 pb-1 border-b border-gray-700">圖片設置</h4>
        
        <div className="mb-4">
          <label className="text-xs text-gray-400 block mb-1">圖片內容</label>
          <button
            onClick={openImageSelector}
            className="w-full flex items-center justify-between px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white hover:bg-gray-600"
          >
            <div className="flex items-center">
              <Image className="w-4 h-4 mr-2 text-gray-400" />
              <span className="text-sm truncate max-w-[180px]">
                {element.imageUrl ? element.imageUrl.split('/').pop() : '選擇圖片...'}
              </span>
            </div>
            <Upload className="w-4 h-4" />
          </button>
          
          {element.imageUrl && (
            <div className="mt-2 p-2 border border-gray-700 rounded bg-gray-800 flex justify-center">
              <img 
                src={element.imageUrl} 
                alt="預覽" 
                className="max-h-20 object-contain"
              />
            </div>
          )}
        </div>
      </div>
      
      {/* 圖片選擇器彈窗 */}
      <ImageSelectorModal
        isOpen={showImageSelector}
        onClose={closeImageSelector}
        onSelect={handleImageUrlChange}
        currentUrl={element.imageUrl}
      />
    </>
  );
};