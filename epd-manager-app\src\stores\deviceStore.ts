// EPD Manager App - 設備狀態管理

import { create } from 'zustand';
import { Device, DeviceState, DeviceConfig } from '../types';
import { webSocketService } from '../services/WebSocketService';
import { generateRandomMac, generateDeviceSize, generateColorType } from '../utils/generators';

interface DeviceStore extends DeviceState {
  // Actions
  addCustomDevice: (deviceConfig: DeviceConfig) => boolean;
  removeCustomDevice: (index: number) => boolean;
  requestDeviceImage: (macAddress: string) => void;
  refreshDeviceList: () => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Getters
  getAllDevices: () => Device[];
  getCustomDevices: () => Device[];
  getDeviceByMac: (macAddress: string) => Device | undefined;
  getDeviceCount: () => number;
  getCustomDeviceCount: () => number;
}

export const useDeviceStore = create<DeviceStore>()((set, get) => ({
  // Initial state
  devices: [],
  customDevices: [],
  loading: false,
  error: null,

  // Actions
  addCustomDevice: (deviceConfig: DeviceConfig) => {
    try {
      // 驗證 MAC 地址格式
      if (!deviceConfig.macAddress) {
        set({ error: 'MAC 地址不能為空' });
        return false;
      }

      // 檢查 MAC 地址是否已存在
      const { customDevices } = get();
      const existingDevice = customDevices.find(device => device.macAddress === deviceConfig.macAddress);
      if (existingDevice) {
        set({ error: 'MAC 地址已存在' });
        return false;
      }

      // 添加設備到 WebSocket 服務
      webSocketService.addCustomDevice(deviceConfig);

      // 更新本地狀態
      const newDevice: Device = {
        macAddress: deviceConfig.macAddress,
        status: deviceConfig.status || 'online',
        data: {
          size: deviceConfig.size || generateDeviceSize(),
          battery: Math.floor(Math.random() * 101),
          rssi: -1 * Math.floor(Math.random() * 101),
          colorType: deviceConfig.colorType || generateColorType(),
          imageCode: deviceConfig.imageCode
        }
      };

      const updatedCustomDevices = [...customDevices, newDevice];
      set({ 
        customDevices: updatedCustomDevices,
        error: null 
      });

      console.log('添加自定義設備成功:', deviceConfig.macAddress);
      return true;
    } catch (error: any) {
      set({ error: error.message || '添加設備失敗' });
      return false;
    }
  },

  removeCustomDevice: (index: number) => {
    try {
      const { customDevices } = get();
      
      if (index < 0 || index >= customDevices.length) {
        set({ error: '無效的設備索引' });
        return false;
      }

      // 從 WebSocket 服務移除設備
      const success = webSocketService.removeCustomDevice(index);
      
      if (success) {
        // 更新本地狀態
        const updatedCustomDevices = customDevices.filter((_, i) => i !== index);
        set({ 
          customDevices: updatedCustomDevices,
          error: null 
        });

        console.log('移除自定義設備成功');
        return true;
      } else {
        set({ error: '移除設備失敗' });
        return false;
      }
    } catch (error: any) {
      set({ error: error.message || '移除設備失敗' });
      return false;
    }
  },

  requestDeviceImage: (macAddress: string) => {
    try {
      webSocketService.requestDeviceImage(macAddress);
      console.log(`請求設備 ${macAddress} 的預覽圖像`);
    } catch (error: any) {
      set({ error: error.message || '請求圖像失敗' });
    }
  },

  refreshDeviceList: () => {
    try {
      // 從 WebSocket 服務獲取最新的設備列表
      const allDevices = webSocketService.getDeviceList();
      
      // 分離預設設備和自定義設備
      const customDevices = get().customDevices;
      const defaultDeviceCount = allDevices.length - customDevices.length;
      const defaultDevices = allDevices.slice(0, defaultDeviceCount);
      
      set({ 
        devices: defaultDevices,
        error: null 
      });

      console.log('刷新設備列表成功');
    } catch (error: any) {
      set({ error: error.message || '刷新設備列表失敗' });
    }
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  // Getters
  getAllDevices: () => {
    const { devices, customDevices } = get();
    return [...devices, ...customDevices];
  },

  getCustomDevices: () => {
    const { customDevices } = get();
    return customDevices;
  },

  getDeviceByMac: (macAddress: string) => {
    const { devices, customDevices } = get();
    const allDevices = [...devices, ...customDevices];
    return allDevices.find(device => device.macAddress === macAddress);
  },

  getDeviceCount: () => {
    const { devices, customDevices } = get();
    return devices.length + customDevices.length;
  },

  getCustomDeviceCount: () => {
    const { customDevices } = get();
    return customDevices.length;
  }
}));

// 導出便捷的 hooks
export const useDevices = () => {
  const store = useDeviceStore();
  return {
    // State
    devices: store.devices,
    customDevices: store.customDevices,
    loading: store.loading,
    error: store.error,
    
    // Actions
    addCustomDevice: store.addCustomDevice,
    removeCustomDevice: store.removeCustomDevice,
    requestDeviceImage: store.requestDeviceImage,
    refreshDeviceList: store.refreshDeviceList,
    clearError: store.clearError,
    
    // Getters
    getAllDevices: store.getAllDevices(),
    getCustomDevices: store.getCustomDevices(),
    getDeviceByMac: store.getDeviceByMac,
    getDeviceCount: store.getDeviceCount(),
    getCustomDeviceCount: store.getCustomDeviceCount(),
  };
};

export const useDeviceActions = () => {
  const store = useDeviceStore();
  return {
    addCustomDevice: store.addCustomDevice,
    removeCustomDevice: store.removeCustomDevice,
    requestDeviceImage: store.requestDeviceImage,
    refreshDeviceList: store.refreshDeviceList,
    clearError: store.clearError,
    setLoading: store.setLoading,
    setError: store.setError,
  };
};

export const useDeviceState = () => {
  const store = useDeviceStore();
  return {
    devices: store.devices,
    customDevices: store.customDevices,
    loading: store.loading,
    error: store.error,
    getAllDevices: store.getAllDevices(),
    getDeviceCount: store.getDeviceCount(),
    getCustomDeviceCount: store.getCustomDeviceCount(),
  };
};

// 快速添加設備的便捷函數
export const useQuickAddDevice = () => {
  const { addCustomDevice } = useDeviceActions();
  
  return {
    addRandomDevice: () => {
      const deviceConfig: DeviceConfig = {
        macAddress: generateRandomMac(),
        status: 'online',
        size: generateDeviceSize(),
        colorType: generateColorType()
      };
      return addCustomDevice(deviceConfig);
    },
    
    addDeviceWithMac: (macAddress: string) => {
      const deviceConfig: DeviceConfig = {
        macAddress,
        status: 'online',
        size: generateDeviceSize(),
        colorType: generateColorType()
      };
      return addCustomDevice(deviceConfig);
    }
  };
};
