/**
 * 測試設備數據綁定功能
 * 該腳本測試設備數據綁定服務的功能
 */

// 引入必要的模組
const { MongoClient, ObjectId } = require('mongodb');
const deviceBindingService = require('../server/services/deviceBindingService');
const { logDeviceEvent } = require('../server/services/websocketService');

// 模擬 WebSocket 服務
jest.mock('../server/services/websocketService', () => ({
  logDeviceEvent: jest.fn().mockResolvedValue({}),
  sendCommandToGateway: jest.fn().mockResolvedValue({ success: true, message: '命令已發送到網關' })
}));

// 資料庫連接設定
const dbConfig = {
  url: 'mongodb://localhost:27017',
  dbName: 'epd-manager-lite'
};

// 測試設備數據
const testDevice = {
  macAddress: '11:22:33:44:55:66',
  status: 'online',
  dataId: 'data123',
  storeId: 'store1',
  initialized: true,
  primaryGatewayId: new ObjectId(),
  otherGateways: [],
  lastSeen: new Date(),
  note: '測試設備',
  data: {
    size: '10.3"',
    rssi: -75,
    battery: 85
  },
  createdAt: new Date(),
  updatedAt: new Date()
};

// 測試綁定數據
const testBindingData = {
  templateId: 'template123',
  dataBindings: {
    'default': 'data123',
    'field1': 'data456',
    'field2': 'data789'
  }
};

// 測試函數
async function testDeviceDataBindingService() {
  let client;
  
  try {
    // 連接到 MongoDB
    client = new MongoClient(dbConfig.url);
    await client.connect();
    console.log('已連接到 MongoDB');
    
    const db = client.db(dbConfig.dbName);
    const deviceCollection = db.collection('devices');
    
    // 初始化設備數據綁定服務
    deviceBindingService.initDB(async () => ({ client, db }));
    
    // 清理測試數據
    await deviceCollection.deleteMany({ macAddress: testDevice.macAddress });
    
    // 插入測試設備
    const result = await deviceCollection.insertOne(testDevice);
    const deviceId = result.insertedId;
    console.log(`已創建測試設備，ID: ${deviceId}`);
    
    // 測試更新設備數據綁定
    console.log('測試更新設備數據綁定...');
    const updatedDevice = await deviceBindingService.updateDeviceDataBindings(
      deviceId.toString(),
      testBindingData,
      'user123',
      'store1'
    );
    
    console.log('更新後的設備數據:');
    console.log(JSON.stringify(updatedDevice, null, 2));
    
    // 驗證更新結果
    if (updatedDevice.templateId === testBindingData.templateId) {
      console.log('✅ 模板ID更新成功');
    } else {
      console.error('❌ 模板ID更新失敗');
    }
    
    if (updatedDevice.dataBindings) {
      try {
        const parsedBindings = JSON.parse(updatedDevice.dataBindings);
        if (parsedBindings.default === testBindingData.dataBindings.default) {
          console.log('✅ 數據綁定更新成功');
        } else {
          console.error('❌ 數據綁定更新失敗');
        }
      } catch (e) {
        console.error('❌ 數據綁定格式錯誤:', e);
      }
    } else {
      console.error('❌ 數據綁定不存在');
    }
    
    // 驗證事件記錄
    if (logDeviceEvent.mock.calls.length > 0) {
      console.log('✅ 數據綁定變更事件已記錄');
    } else {
      console.error('❌ 數據綁定變更事件未記錄');
    }
    
    // 清理測試數據
    await deviceCollection.deleteMany({ _id: deviceId });
    console.log('已清理測試數據');
    
    console.log('測試完成');
  } catch (error) {
    console.error('測試失敗:', error);
  } finally {
    // 關閉資料庫連接
    if (client) {
      await client.close();
      console.log('資料庫連接已關閉');
    }
  }
}

// 執行測試
testDeviceDataBindingService().catch(console.error);
