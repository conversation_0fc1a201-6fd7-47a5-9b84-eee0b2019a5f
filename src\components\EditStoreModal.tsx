import React, { useState, useEffect } from 'react';
import { X, AlertCircle } from 'lucide-react';
import { Store } from '../types/store';
import { updateStore, checkStoreIdExists } from '../utils/api/storeApi';

interface EditStoreModalProps {
  isOpen: boolean;
  store: Store;
  onClose: () => void;
  onSuccess: () => void;
}

export function EditStoreModal({ isOpen, store, onClose, onSuccess }: EditStoreModalProps) {
  const [formData, setFormData] = useState<{
    id: string;
    name: string;
    address: string;
    phone: string;
    managerId: string;
    status: 'active' | 'inactive';
  }>({
    id: '',
    name: '',
    address: '',
    phone: '',
    managerId: '',
    status: 'active'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCheckingId, setIsCheckingId] = useState(false);
  const [idExists, setIdExists] = useState(false);
  const [originalId, setOriginalId] = useState('');

  // 當門店數據變更時，更新表單數據
  useEffect(() => {
    if (store) {
      setFormData({
        id: store.id || '',
        name: store.name || '',
        address: store.address || '',
        phone: store.phone || '',
        managerId: store.managerId || '',
        status: store.status || 'active'
      });
      setOriginalId(store.id || '');
    }
  }, [store]);

  // 當欄位值變更時
  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // 清除該欄位的錯誤
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }

    // 如果是 ID 欄位，且值不為空，且與原始 ID 不同，則檢查 ID 是否已存在
    if (field === 'id' && value && value.trim() !== '' && value !== originalId) {
      checkIdExistence(value);
    } else if (field === 'id') {
      // 如果 ID 欄位為空或與原始 ID 相同，重置檢查狀態
      setIdExists(false);
    }
  };

  // 檢查 ID 是否已存在
  const checkIdExistence = async (id: string) => {
    try {
      setIsCheckingId(true);
      const exists = await checkStoreIdExists(id);
      setIdExists(exists);

      if (exists) {
        setErrors(prev => ({
          ...prev,
          id: 'ID 已存在，請使用其他 ID'
        }));
      }
    } catch (error) {
      console.error('檢查 ID 失敗:', error);
    } finally {
      setIsCheckingId(false);
    }
  };

  // 表單提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 驗證表單
    const newErrors: Record<string, string> = {};

    // 檢查必填欄位
    if (!formData.id || formData.id.trim() === '') {
      newErrors.id = 'ID 為必填欄位';
    }

    if (!formData.name || formData.name.trim() === '') {
      newErrors.name = '名稱為必填欄位';
    }

    // 如果有錯誤，顯示錯誤並停止提交
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // 如果 ID 已存在且不是原始 ID，停止提交
    if (idExists && formData.id !== originalId) {
      setErrors(prev => ({
        ...prev,
        id: 'ID 已存在，請使用其他 ID'
      }));
      return;
    }

    try {
      setIsSubmitting(true);

      // 呼叫 API 更新門店
      await updateStore(store._id || store.id, formData);

      onSuccess();
    } catch (err: any) {
      console.error('更新門店失敗:', err);
      setErrors({
        form: err.message || '更新門店失敗，請重試'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 如果模態窗口不開啟或沒有門店數據，不渲染任何內容
  if (!isOpen || !store) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
        {/* 標題欄 */}
        <div className="flex justify-between items-center border-b border-gray-200 px-6 py-4">
          <h2 className="text-xl font-bold text-gray-800">編輯門店</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        {/* 表單 */}
        <form onSubmit={handleSubmit} className="px-6 py-4">
          {/* 一般錯誤信息 */}
          {errors.form && (
            <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700">
              {errors.form}
            </div>
          )}

          {/* ID 欄位 */}
          <div className="mb-4">
            <label
              htmlFor="store-id"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              門店 ID <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="store-id"
              value={formData.id}
              onChange={(e) => handleChange('id', e.target.value)}
              className={`w-full px-3 py-2 border ${
                errors.id ? 'border-red-500' : 'border-gray-300'
              } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
              placeholder="例如: TP001"
            />
            {errors.id && (
              <div className="flex items-center mt-1">
                <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
                <p className="text-sm text-red-500">{errors.id}</p>
              </div>
            )}
          </div>

          {/* 名稱欄位 */}
          <div className="mb-4">
            <label
              htmlFor="store-name"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              門店名稱 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="store-name"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className={`w-full px-3 py-2 border ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
              placeholder="例如: 台北總店"
            />
            {errors.name && (
              <div className="flex items-center mt-1">
                <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
                <p className="text-sm text-red-500">{errors.name}</p>
              </div>
            )}
          </div>

          {/* 地址欄位 */}
          <div className="mb-4">
            <label
              htmlFor="store-address"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              門店地址
            </label>
            <input
              type="text"
              id="store-address"
              value={formData.address}
              onChange={(e) => handleChange('address', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="例如: 台北市信義區101號"
            />
          </div>

          {/* 按鈕 */}
          <div className="flex justify-end gap-3 mt-6 border-t border-gray-200 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
              disabled={isSubmitting}
            >
              取消
            </button>
            <button
              type="submit"
              className={`px-4 py-2 bg-blue-600 text-white rounded-md ${
                isSubmitting ? 'opacity-75 cursor-not-allowed' : 'hover:bg-blue-700'
              }`}
              disabled={isSubmitting}
            >
              {isSubmitting ? '提交中...' : '確認更新'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
