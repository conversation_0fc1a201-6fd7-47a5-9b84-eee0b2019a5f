{"translation": {"common": {"add": "Add", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "back": "Back", "next": "Next", "yes": "Yes", "no": "No", "search": "Search", "loading": "Loading...", "pageUnderDevelopment": "This page is under development, please stay tuned...", "reload": "Reload", "selectAll": "Select All", "unselectAll": "Unselect All", "copy": "Copy", "more": "More", "processing": "Processing...", "submitting": "Submitting...", "saving": "Saving...", "updating": "Updating...", "page": "page", "pagesTotal": "pages in total", "goTo": "Go to", "showing": "Showing", "to": "to", "of": "of", "entries": "entries", "previousPage": "Previous Page", "nextPage": "Next Page", "required": "Required", "clearSearch": "Clear Search", "itemsPerPage": "Items per page", "items": "items", "firstPage": "First", "lastPage": "Last", "jumpTo": "Jump to", "upload": "Upload", "type": "Type", "name": "Name", "prefix": "Prefix", "actions": "Actions"}, "sidebar": {"devices": "Equipment Management", "templates": "Store Templates", "database": "Store Data", "storeOverview": "Store Profile", "systemConfig": "System Config", "deploy": "Gateway Management", "analytics": "System Analytics", "users": "Store Settings", "storeManagement": "Store Management", "systemData": "System Data", "systemTemplates": "System Templates", "permissionManagement": "Permission Management", "systemLogs": "System Logs"}, "devices": {"title": "Devices", "addDevice": "Add <PERSON>", "editDevice": "<PERSON>", "deviceName": "Device Name", "deviceId": "Device ID", "deviceType": "Device Type", "status": "Status", "actions": "Actions", "noDevices": "No devices yet, please click the 'Add Device' button to add a device", "noFilteredDevices": "No devices match the filter criteria", "searchPlaceholder": "Search devices...", "allStatuses": "All Statuses", "online": "Online", "offline": "Offline", "allSizes": "All Sizes", "syncing": "Syncing...", "syncDevices": "Sync Devices", "fieldManagement": "Field Management", "bindData": "Bind Data", "macAddress": "MAC Address", "size": "Size", "battery": "Battery", "rssi": "Signal Strength", "code": "Code", "note": "Note", "dataId": "Data ID", "lastSeen": "Last Seen", "confirmDelete": "Are you sure you want to delete this device?", "confirmDeleteMultiple": "Are you sure you want to delete {{count}} selected devices?"}, "templates": {"title": "Templates", "addTemplate": "Add Template", "editTemplate": "Edit Template", "templateName": "Template Name", "templateType": "Template Type", "screen": "Screen", "actions": "Actions", "noTemplates": "No templates created yet", "templateEditor": "Template Editor", "systemTemplate": "System Template", "templateRepository": "Template Repository", "allSizes": "All Sizes", "allColors": "All Colors", "blackWhite": "Black & White", "blackWhiteRed": "Black & White & Red", "searchPlaceholder": "Name, screen size, color...", "copyTemplate": "Copy Template", "deleteTemplate": "Delete Template", "moreOptions": "More Options", "selectedCount": "Selected {{selected}} / {{total}} templates", "deleteSelected": "Delete Selected", "cancelSelection": "Cancel Selection", "multiSelectMode": "Multi-select Mode", "searchResults": "Search results for \"{{term}}\": Found {{count}} templates", "exportTemplate": "Export Template (JSON)", "noContentOnPage": "No content on current page", "noMatchingTemplates": "No templates matching \"{{term}}\" found", "confirmDeleteTemplate": "Are you sure you want to delete template \"{{name}}\"?", "confirmDeleteSelected": "Are you sure you want to delete {{count}} selected templates?"}, "database": {"title": "Database", "addData": "Add Data", "editData": "Edit Data", "dataName": "Data Name", "dataType": "Data Type", "value": "Value", "actions": "Actions", "noData": "No data found", "searchPlaceholder": "Search store data...", "import": "Import", "export": "Export", "syncFields": "S<PERSON> <PERSON>", "syncing": "Syncing...", "fieldManager": "Field Manager", "fieldManagement": "Field Management", "fieldManagerTitle": "Field Manager", "displayFieldSettings": "Display Field Settings", "serialNumber": "S/N", "id": "ID", "add": "Add", "batchDelete": "<PERSON><PERSON> Delete", "saveFieldsView": "Save Field Settings", "selectAll": "Select All", "deselectAll": "Deselect All", "addStoreData": "Add Store Data", "editStoreData": "Edit Store Data", "confirmAdd": "Confirm Add", "confirmEdit": "Confirm Edit", "noStoreData": "No store data yet, please click the 'Add' button to add store data", "noSystemSpecificData": "No system specific data yet, please click the 'Add' button to add system specific data"}, "storeOverview": {"title": "Store Profile", "storeName": "Store Name", "storeId": "Store ID", "location": "Location", "deviceCount": "<PERSON><PERSON>", "lastUpdated": "Last Updated"}, "systemConfig": {"title": "System Configuration", "language": "Language", "theme": "Theme", "serverSettings": "Server Settings", "backupSettings": "Backup Settings", "dataField": "Data Field", "userInfo": "User Info", "resourceManagement": "Resource Management", "gatewaySettings": "Gateway Settings", "paramSetting": "<PERSON><PERSON>", "keyTool": "Key Tool", "username": "Username", "email": "Email", "role": "Role", "admin": "Admin", "user": "User", "guest": "Guest", "uploadImage": "Upload Image", "searchName": "Search Name", "ordinaryDataFields": "Ordinary Data Fields", "iconFields": "Icon Fields", "imageFields": "Image Fields", "videoFields": "Video Fields", "syncToDatabase": "Sync to Database", "gatewayIp": "Gateway IP", "port": "Port", "protocol": "Protocol", "connectionTimeout": "Connection Timeout (ms)", "retryCount": "Retry Count", "bufferSize": "<PERSON><PERSON><PERSON> (KB)", "maxBindingDataCount": "Max Binding Data Count", "maxBindingDataCountDesc": "Set the maximum number of data that can be bound to each template (1-20)", "saveSettings": "Save Settings", "apiKey": "API Key", "secretKey": "Secret Key", "selectImage": "Select Image"}, "errors": {"required": "This field is required", "invalidFormat": "Invalid format", "serverError": "Server error occurred", "error": "Error", "renderError": "An error occurred while rendering the application"}, "storeManagement": {"title": "Store Management", "storeList": "Store List", "addStore": "Add Store", "editStore": "Edit Store", "storeName": "Store Name", "storeId": "Store ID", "location": "Location", "status": "Status", "actions": "Actions", "noStores": "No stores available", "viewDetails": "View Details"}, "stores": {"importSystemData": "Import System Data", "importSystemDataDescription": "Check this option to automatically import system data to the new store"}, "test": {"colorRestriction": "Color Tool"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "rememberMe": "Remember Me", "loggingIn": "Logging in...", "welcomeBack": "Welcome Back", "usernamePlaceholder": "Please enter username", "passwordPlaceholder": "Please enter password", "confirmPasswordPlaceholder": "Please enter password again", "passwordMismatch": "Passwords do not match", "initializeSystem": "System Initialization", "createAdminAccount": "Create Ad<PERSON> Account", "initialize": "Initialize", "initializing": "Initializing...", "initializeSuccess": "System initialized successfully, please login with your created account", "initializeFailed": "System initialization failed, please try again", "checkInitStatusFailed": "Failed to check initialization status, please refresh the page", "changePassword": "Change Password", "changePasswordDescription": "Please enter your current password and new password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "currentPasswordPlaceholder": "Please enter current password", "newPasswordPlaceholder": "Please enter new password", "confirmNewPasswordPlaceholder": "Please enter new password again", "passwordChanged": "Password changed successfully", "dismiss": "<PERSON><PERSON><PERSON>", "showAdvancedSettings": "Show Advanced Settings", "hideAdvancedSettings": "Hide Advanced Settings", "name": "Name", "email": "Email", "phone": "Phone", "namePlaceholder": "Please enter name", "emailPlaceholder": "Please enter email", "phonePlaceholder": "Please enter phone number"}, "permission": {"title": "Permission Management", "roleManagement": "Role Management", "userManagement": "User Management", "permissionAssignment": "Permission Assignment", "addRole": "Add Role", "editRole": "Edit Role", "deleteRole": "Delete Role", "roleName": "Role Name", "roleDescription": "Role Description", "roleType": "Role Type", "permissions": "Permissions", "system": "System", "store": "Store", "addUser": "Add User", "editUser": "Edit User", "deleteUser": "Delete User", "resetPassword": "Reset Password", "username": "Username", "name": "Name", "email": "Email", "phone": "Phone", "status": "Status", "active": "Active", "inactive": "Inactive", "addPermission": "Add Permission", "editPermission": "Edit Permission", "deletePermission": "Delete Permission", "scope": "<PERSON><PERSON>", "scopeType": "Scope Type", "user": "User", "role": "Role", "batchAdd": "<PERSON><PERSON> Add", "batchDelete": "<PERSON><PERSON> Delete", "search": "Search", "filter": "Filter", "all": "All", "confirmDelete": "Confirm Delete", "confirmDeleteRole": "Are you sure you want to delete role '{{name}}'?", "confirmDeleteUser": "Are you sure you want to delete user '{{name}}'?", "confirmDeletePermission": "Are you sure you want to delete this permission assignment?", "confirmResetPassword": "Are you sure you want to reset password for user '{{name}}'?", "defaultPassword": "Default Password", "passwordResetSuccess": "Password reset successfully, new password is: {{password}}"}, "gateways": {"title": "Gateway Management", "name": "Gateway Name", "macAddress": "MAC Address", "status": "Status", "lastSeen": "Last Updated", "model": "Model", "wifiFirmwareVersion": "WiFi Firmware Version", "btFirmwareVersion": "Bluetooth Firmware Version", "ipAddress": "IP Address", "actions": "Actions", "addGateway": "Add Gateway", "editGateway": "Edit Gateway", "deleteGateway": "Delete Gateway", "confirmDelete": "Are you sure you want to delete this gateway?", "confirmBatchDelete": "Are you sure you want to delete {{count}} selected gateways?", "deleteSuccess": "Gateway deleted successfully", "deleteFailed": "Failed to delete gateway", "batchDeleteSuccess": "Batch delete gateways successfully", "batchDeleteFailed": "Failed to batch delete gateways", "noItemSelected": "No gateway selected", "searchPlaceholder": "Search gateway name, MAC address...", "allStatus": "All Statuses", "online": "Online", "offline": "Offline", "allModels": "All Models", "syncGateways": "Sync Gateways", "syncing": "Syncing...", "syncSuccess": "Gateway sync successful", "syncFailed": "Gateway sync failed", "restart": "<PERSON><PERSON>", "confirmRestart": "Are you sure you want to restart this gateway?", "restartSuccess": "Gateway restart successful", "restartFailed": "Gateway restart failed", "upgradeWifiFirmware": "Upgrade WiFi Firmware", "upgradeBtFirmware": "Upgrade Bluetooth Firmware", "selectFirmwareVersion": "Select Firmware Version", "selectVersion": "Please select a version", "upgradeWarning": "Upgrade Warning", "upgradeWarning1": "Do not disconnect the gateway during upgrade", "upgradeWarning2": "Upgrade may take several minutes", "upgradeWarning3": "Gateway will restart automatically after upgrade", "versionRequired": "Please select a firmware version", "noGatewaySelected": "No gateway selected", "upgradeFailed": "Firmware upgrade failed", "selectedGatewaysCount": "Selected {{count}} gateways", "namePlaceholder": "Please enter gateway name", "modelPlaceholder": "Please enter gateway model", "nameRequired": "Gateway name is required", "macAddressRequired": "MAC address is required", "invalidMacAddress": "Invalid MAC address format, should be XX:XX:XX:XX:XX:XX or XX-XX-XX-XX-XX-XX", "invalidIpAddress": "Invalid IP address format", "addFailed": "Failed to add gateway", "updateFailed": "Failed to update gateway", "macAddressFormat": "Format: XX:XX:XX:XX:XX:XX or XX-XX-XX-XX-XX-XX", "macAddressReadOnly": "MAC address cannot be modified", "fieldManagement": "Field Management", "manageFields": "Manage Display Fields", "never": "Never", "copyWsInfo": "Copy WebSocket Login Info", "wsInfoCopied": "WebSocket login info copied to clipboard", "copyWsInfoFailed": "Failed to copy WebSocket login info", "noWsInfo": "Unable to get WebSocket login info"}}}