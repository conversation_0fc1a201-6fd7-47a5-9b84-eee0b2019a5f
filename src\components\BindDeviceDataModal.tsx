import React, { useState, useEffect } from 'react';
import { X, AlertCircle } from 'lucide-react';
import { Device } from '../types/device';
import { buildEndpointUrl } from '../utils/api/apiConfig';
import { getAllStoreData } from '../utils/api/storeDataApi';
import { updateDevice, updateDeviceDataBindings } from '../utils/api/deviceApi';
import { processDataFieldBindings } from '../utils/previewUtils';
import { getAllDataFields } from '../utils/api/dataFieldApi';
import { TemplateElement } from '../types';
import PreviewComponent from './PreviewComponent';
import { getScreenSizeMap } from '../screens/screenSizeMap';
import { Store } from '../types/store';
import { savePreviewImageToDevice } from '../utils/previewImageManager';

interface BindDeviceDataModalProps {
  isOpen: boolean;
  device: Device | null;
  store?: Store | null; // 添加可選的store參數
  onClose: () => void;
  onSuccess: () => void;
}

// 使用默認導出
function BindDeviceDataModal({ isOpen, device, store, onClose, onSuccess }: BindDeviceDataModalProps) {
  // 修改為存儲多個數據欄位的映射
  const [formData, setFormData] = useState({
    templateId: '',
    dataBindings: {} as Record<string, string>, // 存儲 {欄位ID: 數據ID} 的映射
    _updateTimestamp: Date.now(), // 添加時間戳，用於強制更新預覽圖
    updateDisplay: true // 是否立即更新顯示，默認為 true
  });

  const [templates, setTemplates] = useState<any[]>([]);
  const [storeData, setStoreData] = useState<any[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [templateFields, setTemplateFields] = useState<string[]>([]); // 存儲模板中的數據欄位
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [isPortraitTemplate, setIsPortraitTemplate] = useState(false); // 是否為縱向模板

  // 當設備數據變化時更新
  useEffect(() => {
    if (device) {
      // 初始化 formData，保留現有的綁定關係
      const initialDataBindings: Record<string, string> = {};

      // 如果設備已有綁定的數據，解析並填充到對應欄位
      if (device.dataBindings) {
        try {
          // 嘗試解析 JSON 格式的綁定數據
          if (typeof device.dataBindings === 'string') {
            const bindings = JSON.parse(device.dataBindings);
            Object.assign(initialDataBindings, bindings);
          } else if (typeof device.dataBindings === 'object') {
            Object.assign(initialDataBindings, device.dataBindings);
          }
        } catch (error) {
          console.error('解析數據綁定失敗:', error);
        }
      } else if (device.dataId) {
        // 向下兼容：如果有舊版本的單一 dataId，將其設為默認值
        initialDataBindings['default'] = device.dataId;
      }

      setFormData({
        templateId: device.templateId || '',
        dataBindings: initialDataBindings,
        _updateTimestamp: Date.now(), // 添加時間戳
        updateDisplay: true // 默認為 true
      });

      // 重置所有相關狀態，確保不會殘留上一次的 UI
      setSelectedTemplate(null);
      setTemplateFields([]);
      setDataIndices([]);
      setPreviewImage(null);
      setErrors({});

      // 檢測是否為縱向模板
      if (device.templateId) {
        // 如果設備已有模板ID，預先檢查是否為縱向模板
        fetchTemplateDetail(device.templateId).then(templateDetail => {
          if (templateDetail && templateDetail.screenSize) {
            const sizeMatch = templateDetail.screenSize.match(/(\d+)x(\d+)/);
            if (sizeMatch && sizeMatch[1] && sizeMatch[2]) {
              const width = parseInt(sizeMatch[1], 10);
              const height = parseInt(sizeMatch[2], 10);
              setIsPortraitTemplate(height > width);
              console.log(`初始化時檢測模板方向: ${height > width ? '縱向' : '橫向'}, 尺寸: ${width}x${height}`);
            }
          }
        });
      }
    }
  }, [device]);
  // 加載模板和數據
  useEffect(() => {
    if (isOpen) {
      // 顯示調試信息
      console.log('=== 綁定數據頁面被打開 ===');
      console.log('設備信息:', device);
      console.log('Store 屬性:', store);
      console.log('Window.selectedStore:', (window as any).selectedStore);
      console.log('URL 參數:', window.location.search);
      console.log('=========================');

      const fetchData = async () => {
        try {
          // 獲取模板列表
          let templateList = [];

          // 獲取當前門店ID - 優先使用傳入的 store 屬性
          let currentStoreId = null;

          // 1. 從傳入的store屬性獲取 (最優先)
          if (store && store.id) {
            currentStoreId = store.id;
            console.log('從傳入的store屬性獲取門店ID:', currentStoreId);
          }
          // 2. 從App上下文中獲取
          else if ((window as any).selectedStore && (window as any).selectedStore.id) {
            currentStoreId = (window as any).selectedStore.id;
            console.log('從App上下文獲取門店ID:', currentStoreId);

            // 將門店信息設置到 store 屬性中，以便後續使用
            if (!store) {
              console.log('將全局門店信息設置到本地 store 屬性');
            }
          }
          // 3. 從URL參數中獲取
          else {
            const urlParams = new URLSearchParams(window.location.search);
            const storeIdFromUrl = urlParams.get('storeId');
            if (storeIdFromUrl) {
              currentStoreId = storeIdFromUrl;
              console.log('從URL參數獲取門店ID:', currentStoreId);
            }
          }

          // 如果仍然沒有獲取到門店ID，則嘗試從設備屬性獲取
          if (!currentStoreId && device && (device as any).storeId) {
            currentStoreId = (device as any).storeId;
            console.log('從設備屬性獲取門店ID:', currentStoreId);
          }

          // 添加更多調試信息
          console.log('門店ID獲取結果:', {
            '從props獲取的store': store,
            '從window獲取的selectedStore': (window as any).selectedStore,
            '最終使用的門店ID': currentStoreId
          });

          if (currentStoreId) {
            console.log('最終使用的門店ID:', currentStoreId);

            try {
              // 構建帶有門店ID參數的URL
              const templateUrl = `${buildEndpointUrl('templates')}?storeId=${encodeURIComponent(currentStoreId)}`;
              const templateResponse = await fetch(templateUrl);

              if (!templateResponse.ok) {
                throw new Error(`獲取模板失敗: ${templateResponse.status}`);
              }

              templateList = await templateResponse.json();
              console.log(`獲取到門店 ${currentStoreId} 的模板:`, templateList);

              // 過濾模板，只保留當前門店的模板和系統模板
              templateList = templateList.filter((template: any) =>
                template.isSystemTemplate ||
                (template.storeId && template.storeId === currentStoreId)
              );

              console.log('過濾後的模板列表:', templateList);
            } catch (error) {
              console.error('獲取模板時出錯:', error);
              templateList = [];
            }
          } else {
            console.warn('未選擇門店，無法獲取門店模板');
            // 返回空模板列表
            templateList = [];
          }

          // 獲取門店數據
          let storeDataList: any[] = [];
          try {
            // 使用之前獲取的門店ID
            if (currentStoreId) {
              console.log('=== 開始獲取門店數據 ===');
              console.log('使用門店ID獲取數據:', currentStoreId);
              console.log('API 端點:', buildEndpointUrl('storeData') + `?storeId=${encodeURIComponent(currentStoreId)}`);

              // 獲取指定門店的數據
              storeDataList = await getAllStoreData(currentStoreId);

              // 詳細記錄獲取到的數據
              console.log('獲取到門店數據 (長度):', storeDataList?.length || 0);
              console.log('獲取到門店數據 (類型):', Array.isArray(storeDataList) ? 'Array' : typeof storeDataList);

              // 如果是數組且有數據，顯示第一個元素的結構
              if (Array.isArray(storeDataList) && storeDataList.length > 0) {
                console.log('門店數據第一項結構:', Object.keys(storeDataList[0]));
                console.log('門店數據第一項示例:', storeDataList[0]);
              }

              console.log('=== 門店數據獲取完成 ===');
            } else {
              console.warn('未選擇門店，無法獲取門店數據');
            }
          } catch (error) {
            console.error('獲取門店數據失敗:', error);
          }          // 根據裝置尺寸和顏色類型篩選模板
          let filteredTemplates = templateList;
          if (device && device.data?.size) {
            // 從 screens 目錄動態獲取裝置尺寸映射
            const deviceSizeMap = getScreenSizeMap();

            // 記錄詳細的診斷信息
            console.log('裝置尺寸字符串:', device.data.size);
            console.log('裝置尺寸類型:', typeof device.data.size);
            console.log('裝置顏色類型:', device.data && device.data.colorType ? device.data.colorType : '未設置');
            console.log('尺寸映射表:', deviceSizeMap);

            // 處理不同格式的尺寸字符串，例如 "2.9" 和 "2.9\"" 都應匹配
            let normalizedSize = device.data.size;

            // 移除尺寸字符串中可能存在的引號或其他非數字字符
            if (typeof normalizedSize === 'string') {
              normalizedSize = normalizedSize.replace(/[^0-9.]/g, '');
            }

            console.log('標準化後的尺寸:', normalizedSize);

            // 尋找匹配的尺寸映射
            let targetSize = '';

            // 嘗試直接匹配
            if (deviceSizeMap[normalizedSize]) {
              targetSize = deviceSizeMap[normalizedSize];
            } else {
              // 嘗試寬松匹配 - 查找包含相同數字的鍵
              for (const [key, value] of Object.entries(deviceSizeMap)) {
                const normalizedKey = key.replace(/[^0-9.]/g, '');
                if (normalizedKey === normalizedSize) {
                  targetSize = value;
                  break;
                }
              }              // 如果還是找不到，使用原始值
              if (!targetSize) {
                targetSize = device.data?.size || '';
              }
            }

            console.log('最終目標尺寸:', targetSize);

            // 篩選符合裝置尺寸的模板
            filteredTemplates = templateList.filter((template: any) => {
              // 如果模板沒有指定尺寸，預設不符合
              if (!template.screenSize) return false;

              console.log(`比較模板 ${template.name}: ${template.screenSize} vs ${targetSize}`);

              // 檢查模板尺寸是否與裝置尺寸相符
              // 考慮旋轉的情況：同時檢查原始尺寸和旋轉後的尺寸（寬高互換）
              let sizeMatch = template.screenSize === targetSize;

              // 如果原始尺寸不匹配，檢查旋轉後的尺寸
              if (!sizeMatch && template.screenSize) {
                // 從模板尺寸中提取寬高
                const [width, height] = template.screenSize.split('x').map(Number);
                // 創建旋轉後的尺寸字符串（寬高互換）
                const rotatedSize = `${height}x${width}`;
                // 檢查旋轉後的尺寸是否匹配
                sizeMatch = rotatedSize === targetSize;

                if (sizeMatch) {
                  console.log(`模板 ${template.name} 旋轉後尺寸匹配: ${rotatedSize} vs ${targetSize}`);
                }
              }

              // 檢查顏色類型是否匹配
              let colorMatch = true; // 默認為 true，如果設備沒有設置 colorType

              if (device.data && device.data.colorType && template.color) {
                // 如果設備和模板都有顏色類型，則進行比較
                colorMatch = device.data.colorType === template.color;
                console.log(`比較顏色類型: 設備=${device.data.colorType}, 模板=${template.color}, 匹配=${colorMatch}`);
              }

              // 同時滿足尺寸和顏色類型匹配
              return sizeMatch && colorMatch;
            });

            console.log(`裝置尺寸: ${device.data?.size}, 顏色類型: ${device.data && device.data.colorType ? device.data.colorType : '未設置'}, 符合條件的模板數量: ${filteredTemplates.length}`);

            // 如果沒有找到匹配的模板，嘗試比較數字部分，但仍然保持顏色類型匹配
            if (filteredTemplates.length === 0) {
              console.log('嘗試進行部分匹配...');
              filteredTemplates = templateList.filter((template: any) => {
                if (!template.screenSize) return false;

                // 檢查顏色類型是否匹配
                let colorMatch = true; // 默認為 true，如果設備沒有設置 colorType

                if (device.data && device.data.colorType && template.color) {
                  // 如果設備和模板都有顏色類型，則進行比較
                  colorMatch = device.data.colorType === template.color;
                }

                // 如果顏色類型不匹配，直接返回 false
                if (!colorMatch) return false;

                // 從尺寸字符串中提取分辨率數值
                const templateWidthHeight = template.screenSize.split('x').map(Number);
                const targetWidthHeight = targetSize.split('x').map(Number);

                // 如果寬高相差不超過 5 像素，也視為匹配
                if (templateWidthHeight.length === 2 && targetWidthHeight.length === 2) {
                  // 檢查原始方向
                  const widthDiff = Math.abs(templateWidthHeight[0] - targetWidthHeight[0]);
                  const heightDiff = Math.abs(templateWidthHeight[1] - targetWidthHeight[1]);
                  const originalMatch = widthDiff <= 5 && heightDiff <= 5;

                  // 檢查旋轉方向（寬高互換）
                  const rotatedWidthDiff = Math.abs(templateWidthHeight[1] - targetWidthHeight[0]);
                  const rotatedHeightDiff = Math.abs(templateWidthHeight[0] - targetWidthHeight[1]);
                  const rotatedMatch = rotatedWidthDiff <= 5 && rotatedHeightDiff <= 5;

                  // 任一方向匹配即可
                  const isMatch = originalMatch || rotatedMatch;

                  if (isMatch) {
                    if (originalMatch) {
                      console.log(`寬鬆比較 ${template.screenSize} vs ${targetSize}: 原始方向匹配`);
                    } else {
                      console.log(`寬鬆比較 ${template.screenSize} vs ${targetSize}: 旋轉方向匹配`);
                    }
                  } else {
                    console.log(`寬鬆比較 ${template.screenSize} vs ${targetSize}: 不匹配`);
                  }

                  return isMatch;
                }

                return false;
              });

              console.log(`寬鬆匹配後，符合條件的模板數量: ${filteredTemplates.length}`);
            }
          }

          setTemplates(filteredTemplates);

          // 處理門店數據
          console.log('=== 開始處理門店數據 ===');
          console.log('當前門店ID:', currentStoreId);
          console.log('門店數據類型:', Array.isArray(storeDataList) ? 'Array' : typeof storeDataList);
          console.log('門店數據長度:', storeDataList?.length || 0);

          if (currentStoreId && Array.isArray(storeDataList)) {
            // 獲取門店名稱 - 優先使用傳入的 store 屬性
            let storeName = '未知門店';
            if (store && store.name) {
              storeName = store.name;
              console.log('從傳入的 store 屬性獲取門店名稱:', storeName);
            } else if ((window as any).selectedStore && (window as any).selectedStore.name) {
              storeName = (window as any).selectedStore.name;
              console.log('從全局 window 對象獲取門店名稱:', storeName);
            }

            // 為每個數據項添加 storeId 屬性
            const processedData = storeDataList.map(item => {
              // 確保每個項目都有 uid 屬性
              const uid = item.uid || item._id?.toString() || item.id;

              return {
                ...item,
                uid: uid, // 確保有 uid
                storeId: currentStoreId, // 確保每個數據項都有 storeId
                _storeName: storeName // 添加門店名稱
              };
            });

            // 添加調試信息
            console.log('處理後的門店數據 (長度):', processedData.length);
            if (processedData.length > 0) {
              console.log('處理後的門店數據 (第一項):', processedData[0]);
              console.log('門店數據中的 storeId:', processedData.map(item => item.storeId));
              console.log('門店數據中的 ID/UID:', processedData.map(item => item.uid || item._id || item.id));
            }

            setStoreData(processedData);
            console.log('已設置門店數據到狀態');
          } else {
            if (!currentStoreId) {
              console.warn('未獲取到門店ID，無法處理門店數據');
            } else if (!Array.isArray(storeDataList)) {
              console.warn('獲取到的門店數據不是數組格式:', typeof storeDataList);
            } else if (storeDataList.length === 0) {
              console.warn('獲取到的門店數據為空數組');
            }
            setStoreData([]);
            console.log('已設置空數組到門店數據狀態');
          }
          console.log('=== 門店數據處理完成 ===');

          // 如果設備已有模板ID，則預先加載該模板詳情
          if (device && device.templateId) {
            const templateDetail = await fetchTemplateDetail(device.templateId);
            if (templateDetail) {              // 驗證已綁定的模板是否與裝置尺寸相符
              if (device && device.data?.size) {
                // 從 screens 目錄動態獲取裝置尺寸映射
                const deviceSizeMap = getScreenSizeMap();

                // 處理不同格式的尺寸字符串
                let normalizedSize = device.data.size;
                if (typeof normalizedSize === 'string') {
                  normalizedSize = normalizedSize.replace(/[^0-9.]/g, '');
                }

                // 尋找匹配的尺寸映射
                let targetSize = '';

                // 嘗試直接匹配
                if (deviceSizeMap[normalizedSize]) {
                  targetSize = deviceSizeMap[normalizedSize];
                } else {
                  // 嘗試寬松匹配
                  for (const [key, value] of Object.entries(deviceSizeMap)) {
                    const normalizedKey = key.replace(/[^0-9.]/g, '');
                    if (normalizedKey === normalizedSize) {
                      targetSize = value;
                      break;
                    }
                  }

                  if (!targetSize) {
                    targetSize = device.data.size;
                  }
                }

                // 寬鬆比較模板尺寸
                let isMatch = templateDetail.screenSize === targetSize;

                // 如果原始尺寸不匹配，檢查旋轉後的尺寸
                if (!isMatch && templateDetail.screenSize) {
                  try {
                    // 從模板尺寸中提取寬高
                    const [width, height] = templateDetail.screenSize.split('x').map(Number);
                    // 創建旋轉後的尺寸字符串（寬高互換）
                    const rotatedSize = `${height}x${width}`;
                    // 檢查旋轉後的尺寸是否匹配
                    isMatch = rotatedSize === targetSize;

                    if (isMatch) {
                      console.log(`已綁定模板 ${templateDetail.name} 旋轉後尺寸匹配: ${rotatedSize} vs ${targetSize}`);
                    }
                  } catch (e) {
                    console.error('比較旋轉尺寸時出錯:', e);
                  }
                }

                // 如果仍不匹配，嘗試數值比較
                if (!isMatch && targetSize && templateDetail.screenSize) {
                  try {
                    const templateWidthHeight = templateDetail.screenSize.split('x').map(Number);
                    const targetWidthHeight = targetSize.split('x').map(Number);

                    if (templateWidthHeight.length === 2 && targetWidthHeight.length === 2) {
                      // 檢查原始方向
                      const widthDiff = Math.abs(templateWidthHeight[0] - targetWidthHeight[0]);
                      const heightDiff = Math.abs(templateWidthHeight[1] - targetWidthHeight[1]);
                      const originalMatch = widthDiff <= 5 && heightDiff <= 5;

                      // 檢查旋轉方向（寬高互換）
                      const rotatedWidthDiff = Math.abs(templateWidthHeight[1] - targetWidthHeight[0]);
                      const rotatedHeightDiff = Math.abs(templateWidthHeight[0] - targetWidthHeight[1]);
                      const rotatedMatch = rotatedWidthDiff <= 5 && rotatedHeightDiff <= 5;

                      // 任一方向匹配即可
                      isMatch = originalMatch || rotatedMatch;

                      if (isMatch) {
                        if (originalMatch) {
                          console.log(`已綁定模板寬鬆比較: ${templateDetail.screenSize} vs ${targetSize}: 原始方向匹配`);
                        } else {
                          console.log(`已綁定模板寬鬆比較: ${templateDetail.screenSize} vs ${targetSize}: 旋轉方向匹配`);
                        }
                      }
                    }
                  } catch (e) {
                    console.error('比較尺寸時出錯:', e);
                  }
                }

                if (!isMatch) {
                  console.warn(`警告: 已綁定的模板尺寸(${templateDetail.screenSize})與裝置尺寸(${targetSize})不符`);
                  setErrors(prev => ({
                    ...prev,
                    templateId: '已綁定的模板尺寸與裝置不符，請重新選擇'
                  }));
                }
              }

              // 設置模板的預覽圖（如果有）
              if (templateDetail.previewImage) {
                setPreviewImage(templateDetail.previewImage);
                console.log('已設置初始模板的預覽圖');
              }

              setSelectedTemplate(templateDetail);
              // 分析模板中的數據欄位
              const fields = extractTemplateDataFields(templateDetail);
              setTemplateFields(fields);

              // 檢測是否為縱向模板
              if (templateDetail.screenSize) {
                const sizeMatch = templateDetail.screenSize.match(/(\d+)x(\d+)/);
                if (sizeMatch && sizeMatch[1] && sizeMatch[2]) {
                  const width = parseInt(sizeMatch[1], 10);
                  const height = parseInt(sizeMatch[2], 10);
                  setIsPortraitTemplate(height > width);
                  console.log(`模板加載時檢測方向: ${height > width ? '縱向' : '橫向'}, 尺寸: ${width}x${height}`);
                }
              }
            }
          }
        } catch (error) {
          console.error('獲取數據失敗:', error);
          setErrors({
            submit: '獲取數據失敗，請稍後再試'
          });
        }
      };

      fetchData();
    }
  }, [isOpen, device]);

  // 獲取模板詳情
  const fetchTemplateDetail = async (templateId: string) => {
    try {
      const response = await fetch(buildEndpointUrl('templates', templateId));
      if (!response.ok) {
        console.error(`獲取模板詳情失敗: ${response.status}`);
        return null;
      }
      return await response.json();
    } catch (error) {
      console.error('獲取模板詳情失敗:', error);
      return null;
    }  };

  // 用於調試的檢查函數
  const logTemplateInfo = (template: any, message: string) => {
    console.log(`【檢查】${message}`);

    if (!template || !template.elements) {
      console.log('模板為空或沒有元素');
      return;
    }

    const elementsWithBinding = template.elements.filter((el: any) =>
      (el.dataBinding && el.dataBinding.dataIndex !== undefined) || el.dataFieldId
    );

    console.log(`找到 ${elementsWithBinding.length} 個帶綁定的元素`);
    elementsWithBinding.forEach((el: any, index: number) => {
      if (el.dataBinding) {
        console.log(`元素 ${index}: fieldId=${el.dataBinding.fieldId}, dataIndex=${el.dataBinding.dataIndex}`);
      } else if (el.dataFieldId) {
        console.log(`元素 ${index}: 舊版格式 dataFieldId=${el.dataFieldId}`);
      }
    });
  };
    // 用於儲存每個資料索引對應的顯示名稱
  const [dataIndices, setDataIndices] = useState<Array<{fieldId: string, displayName: string}>>([]);
  // 用於存儲欄位映射關係
  const [fieldMappings, setFieldMappings] = useState<Map<number, string[]>>(new Map());
  // 添加資料欄位狀態
  const [dataFields, setDataFields] = useState<any[]>([]);

  // 定義介面
  interface DataIndexInfo {
    dataIndex: number;
    displayName: string;
    fieldId: string;
  }

  interface BindingElement {
    element: any;
    dataIndex: number;
    fieldId: string;
    originalFieldId: string;
  }

  // 從模板中提取數據欄位和對應的資料索引名稱
  const extractTemplateDataFields = (template: any): string[] => {
    if (!template || !template.elements || !Array.isArray(template.elements)) {
      console.log('模板為空或沒有元素，返回空數據欄位');
      return [];
    }

    // 調試檢查
    logTemplateInfo(template, '開始提取數據欄位');

    // 收集所有帶有資料綁定的元素
    const elementsWithDataBinding: BindingElement[] = [];
    for (const element of template.elements) {
      if (element.dataBinding && element.dataBinding.dataIndex !== undefined && element.dataBinding.fieldId) {
        const dataIndex = element.dataBinding.dataIndex;
        const fieldId = element.dataBinding.fieldId;
        // 創建唯一標識符 - 組合 fieldId 和 dataIndex
        const uniqueFieldId = `${fieldId}_idx${dataIndex}`;

        elementsWithDataBinding.push({
          element,
          dataIndex,
          fieldId: uniqueFieldId,  // 使用組合的唯一標識符
          originalFieldId: fieldId // 保存原始 fieldId 以便後續使用
        });
      } else if (element.dataFieldId) {
        // 處理舊版數據綁定格式
        const match = element.dataFieldId.match(/(\d+)/);
        if (match) {
          const dataIndex = parseInt(match[1]) - 1; // 將 "data1" 映射到索引 0
          const fieldId = element.dataFieldId;
          // 創建唯一標識符
          const uniqueFieldId = `${fieldId}_idx${dataIndex}`;

          elementsWithDataBinding.push({
            element,
            dataIndex,
            fieldId: uniqueFieldId, // 使用組合的唯一標識符
            originalFieldId: fieldId // 保存原始 fieldId
          });
        }
      }
    }

    // 如果沒有找到任何綁定元素，返回空數組
    if (elementsWithDataBinding.length === 0) {
      console.log('模板中沒有找到任何數據綁定元素，這是一個靜態模板');
      setDataIndices([]);
      return [];
    }

    // 按照 dataIndex 排序
    elementsWithDataBinding.sort((a, b) => a.dataIndex - b.dataIndex);

    console.log("排序後的綁定元素:", elementsWithDataBinding);

    // 按照 dataIndex 分組，保留每個 dataIndex 只出現一次
    const uniqueDataIndices: DataIndexInfo[] = [];
    const seenDataIndices = new Set<number>();
    const fieldIdsByDataIndex = new Map<number, string[]>(); // 用於追踪每個 dataIndex 對應的所有 fieldId

    elementsWithDataBinding.forEach(item => {
      // 如果這個 dataIndex 還沒處理過
      if (!seenDataIndices.has(item.dataIndex)) {
        seenDataIndices.add(item.dataIndex);
        uniqueDataIndices.push({
          dataIndex: item.dataIndex,
          displayName: `資料${item.dataIndex + 1}`, // dataIndex 是從 0 開始的，顯示時 +1
          fieldId: `dataIndex${item.dataIndex}` // 使用 dataIndex 作為唯一標識符
        });
          // 為這個 dataIndex 創建一個 fieldId 數組
        fieldIdsByDataIndex.set(item.dataIndex, [item.fieldId]);
      } else {
        // 如果這個 dataIndex 已經處理過，將當前 fieldId 添加到對應的數組中
        const existingFieldIds = fieldIdsByDataIndex.get(item.dataIndex);
        if (existingFieldIds) {
          existingFieldIds.push(item.fieldId);
        }
      }
    });

    console.log("唯一的資料索引:", uniqueDataIndices);
    console.log("每個資料索引對應的欄位:", fieldIdsByDataIndex);
      // 更新狀態
    setDataIndices(uniqueDataIndices);
    // 更新欄位映射關係
    setFieldMappings(fieldIdsByDataIndex);

    // 返回唯一的資料索引列表
    return uniqueDataIndices.map(item => item.fieldId);
  };

  // 這些輔助函數已經不再使用，但保留註釋以便將來參考
  // 原本用於獲取模板中帶數據綁定的元素
  // 原本用於獲取資料索引的顯示名稱  // 處理預覽圖生成完成的回調
  const handlePreviewGenerated = async (previewData: string | null) => {
    if (previewData) {
      // 只在UI中更新預覽圖，不保存到數據庫
      setPreviewImage(previewData);
      console.log('預覽圖生成成功，僅在UI中顯示，等待用戶按下確定按鈕後才會保存到數據庫');
    } else {
      console.error('預覽圖生成失敗');
      // 失敗時使用模板的靜態預覽圖
      if (selectedTemplate && selectedTemplate.previewImage) {
        setPreviewImage(selectedTemplate.previewImage);
      } else {
        setPreviewImage(null);
      }
    }
  };

  // 觸發預覽圖更新
  const generatePreview = () => {
    // 使用 PreviewComponent 生成預覽圖
    // 由於我們已經在 UI 中使用了 PreviewComponent，
    // 這裡需要觸發狀態更新，讓 React 重新渲染
    // 實際的預覽圖生成已由 PreviewComponent 處理

    // 先檢查是否有選擇模板和綁定數據
    if (!selectedTemplate || !Object.keys(formData.dataBindings).length) {
      console.log('無法生成預覽圖：未選擇模板或未綁定數據');
      return;
    }

    // 檢查是否有門店數據
    if (!storeData || storeData.length === 0) {
      console.log('無法生成預覽圖：沒有門店數據');
      return;
    }

    console.log('觸發預覽圖更新，當前綁定數據:', formData.dataBindings);
    console.log('當前門店數據數量:', storeData.length);

    // 使用一個臨時狀態變量來強制 PreviewComponent 重新渲染
    const timestamp = Date.now();
    setFormData(prev => ({
      ...prev,
      _updateTimestamp: timestamp // 添加一個時間戳，確保狀態變化
    }));
  };

  // 處理模板選擇變更
  const handleTemplateChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const templateId = e.target.value;

    // 重置數據欄位
    setTemplateFields([]);
    setSelectedTemplate(null);
    // 重置預覽圖
    setPreviewImage(null);

    // 初始化一個新的空綁定對象
    setFormData(prev => ({
      ...prev,
      templateId,
      dataBindings: {},
      _updateTimestamp: Date.now() // 更新時間戳，強制重新渲染
    }));

    if (!templateId) return;

    try {
      // 獲取選中模板的詳情
      const templateDetail = await fetchTemplateDetail(templateId);
      if (templateDetail) {
        setSelectedTemplate(templateDetail);

        // 檢測模板方向（縱向或橫向）
        if (templateDetail.screenSize) {
          try {
            // 解析模板的螢幕尺寸
            const sizeMatch = templateDetail.screenSize.match(/(\d+)x(\d+)/);
            if (sizeMatch && sizeMatch[1] && sizeMatch[2]) {
              const width = parseInt(sizeMatch[1], 10);
              const height = parseInt(sizeMatch[2], 10);
              const isPortrait = height > width;
              setIsPortraitTemplate(isPortrait);
              console.log(`模板方向: ${isPortrait ? '縱向' : '橫向'}, 尺寸: ${width}x${height}`);
            }
          } catch (error) {
            console.error('解析模板尺寸時出錯:', error);
          }
        }

        // 立即設置模板的預覽圖（如果有）
        if (templateDetail.previewImage) {
          setPreviewImage(templateDetail.previewImage);
          console.log('已設置模板的預覽圖:', templateDetail.previewImage.substring(0, 50) + '...');
        } else {
          console.log('模板沒有預覽圖，使用空白預覽');
          // 不嘗試從服務器獲取預覽圖，因為端點可能不存在
          // 對於無數據綁定的模板，我們可以直接使用空白預覽

          // 創建一個空白的預覽圖（1x1像素的透明圖片）
          const emptyPreview = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
          setPreviewImage(emptyPreview);
          console.log('已設置空白預覽圖');
        }

        // 分析模板中的數據欄位
        const fields = extractTemplateDataFields(templateDetail);
        setTemplateFields(fields);

        console.log('模板數據欄位數量:', fields.length);

        // 獲取所有資料欄位定義
        const allDataFields = await getAllDataFields();
        setDataFields(allDataFields);

        // 如果模板沒有數據欄位，直接使用靜態預覽圖
        if (fields.length === 0) {
          console.log('模板沒有數據欄位，使用靜態預覽圖');
          // 確保清空綁定數據，並更新時間戳以強制重新渲染預覽圖
          setFormData(prev => ({
            ...prev,
            dataBindings: {},
            _updateTimestamp: Date.now()
          }));

          // 不自動提交，讓用戶手動確認
          console.log('模板沒有數據欄位，用戶可以直接點擊確定按鈕提交');

          // 強制觸發預覽圖生成
          setTimeout(() => {
            console.log('強制觸發靜態模板預覽圖生成');
            generatePreview();
          }, 100);
        }

        // 如果有舊的綁定數據，嘗試沿用
        if (device && device.dataBindings) {
          let oldBindings = {};

          try {
            // 如果是字串格式，嘗試解析
            if (typeof device.dataBindings === 'string') {
              oldBindings = JSON.parse(device.dataBindings);
            } else if (typeof device.dataBindings === 'object') {
              oldBindings = device.dataBindings;
            }

            // 只保留當前模板中存在的欄位的綁定
            const newBindings: Record<string, string> = {};
            fields.forEach(field => {
              // 使用類型斷言來告訴TypeScript這個索引操作是有效的
              if (oldBindings && typeof oldBindings === 'object' && (oldBindings as Record<string, string>)[field]) {
                newBindings[field] = (oldBindings as Record<string, string>)[field];
              }
            });

            // 更新表單數據
            setFormData(prev => ({
              ...prev,
              dataBindings: newBindings,
              _updateTimestamp: Date.now() // 更新時間戳，強制重新渲染
            }));

            // 如果有綁定數據，立即觸發預覽圖更新
            if (Object.keys(newBindings).length > 0) {
              console.log('從舊綁定中恢復了數據，立即更新預覽圖');
              setTimeout(() => generatePreview(), 100);
            }
          } catch (error) {
            console.error('解析舊綁定數據失敗:', error);
          }
        }
      }
    } catch (error) {
      console.error('獲取模板詳情失敗:', error);
      setErrors(prev => ({
        ...prev,
        templateId: '獲取模板詳情失敗'
      }));
    }
  };
  // 處理數據綁定變更 - 對於相同資料索引的所有欄位，一次性設置相同的數據ID
  const handleDataBindingChange = (dataIndexFieldId: string, dataId: string) => {
    // 使用組件內部狀態而不是全局變量
    const fieldIdsByDataIndex = fieldMappings;

    console.log('數據綁定變更:', { dataIndexFieldId, dataId });
    console.log('當前門店數據數量:', storeData.length);

    // 從 dataIndexFieldId 獲取實際的 dataIndex 值 (例如 "dataIndex0" -> 0)
    const dataIndexMatch = dataIndexFieldId.match(/dataIndex(\d+)/);
    if (!dataIndexMatch) {
      // 如果沒有匹配到正確的格式，則按原來的方式處理
      setFormData(prev => ({
        ...prev,
        dataBindings: {
          ...prev.dataBindings,
          [dataIndexFieldId]: dataId
        },
        _updateTimestamp: Date.now() // 更新時間戳，強制重新渲染
      }));

      console.log('單一欄位綁定更新:', dataIndexFieldId, '->', dataId);

      // 立即觸發預覽圖更新
      setTimeout(() => {
        console.log('觸發預覽圖更新 (單一欄位)');
        generatePreview();
      }, 100);
      return;
    }

    const dataIndex = parseInt(dataIndexMatch[1]);
    const fieldIds = fieldIdsByDataIndex.get(dataIndex) || [];

    console.log(`為資料索引 ${dataIndex} 設置數據ID:`, dataId);
    console.log(`相關欄位:`, fieldIds);

    // 為該 dataIndex 對應的所有欄位設置相同的 dataId
    setFormData(prev => {
      const newBindings = { ...prev.dataBindings };

      // 設置主索引的綁定
      newBindings[dataIndexFieldId] = dataId;

      // 設置所有相關欄位的綁定
      fieldIds.forEach(fieldId => {
        newBindings[fieldId] = dataId;
        console.log(`設置欄位 ${fieldId} -> ${dataId}`);
      });

      return {
        ...prev,
        dataBindings: newBindings,
        _updateTimestamp: Date.now() // 更新時間戳，強制重新渲染
      };
    });

    // 立即觸發預覽圖更新 (在狀態更新後執行)
    setTimeout(() => {
      console.log('觸發預覽圖更新 (多欄位)');
      generatePreview();
    }, 100);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (!device) {
        throw new Error('未選擇設備');
      }

      // 檢查是否有選擇板模
      if (!formData.templateId) {
        setErrors({ templateId: '請選擇板模' });
        setIsSubmitting(false);
        return;
      }

      console.log('提交綁定數據，模板ID:', formData.templateId);
      console.log('模板數據欄位數量:', templateFields.length);
      console.log('當前綁定數據:', formData.dataBindings);

      // 只有當模板有數據欄位時才檢查綁定
      if (templateFields.length > 0) {
        console.log('模板有數據欄位，檢查綁定狀態');
        // 檢查是否所有必要的數據欄位都有綁定
        const missingFields: string[] = [];
        templateFields.forEach(field => {
          if (!formData.dataBindings[field]) {
            missingFields.push(field);
          }
        });

        if (missingFields.length > 0) {
          setErrors({
            submit: `請為以下欄位選擇數據ID: ${missingFields.join(', ')}`
          });
          setIsSubmitting(false);
          return;
        }
      } else {
        console.log('模板沒有數據欄位，跳過綁定檢查');
      }

      // 清除所有UID格式的綁定數據
      const cleanedBindings = { ...formData.dataBindings };
      const uidPattern = /^[0-9a-f]{24}$/i;

      // 檢查每個綁定值，刪除UID格式的值
      Object.entries(cleanedBindings).forEach(([key, value]) => {
        if (typeof value === 'string' && uidPattern.test(value)) {
          console.log(`清除UID格式的綁定: ${key} -> ${value}`);
          delete cleanedBindings[key];
        }
      });

      console.log('清理前的綁定數量:', Object.keys(formData.dataBindings).length);
      console.log('清理後的綁定數量:', Object.keys(cleanedBindings).length);

      // 獲取默認的數據ID（用於向下兼容）
      // 如果模板沒有數據欄位，則使用空字符串作為默認值
      let defaultDataId = '';
      if (templateFields.length > 0) {
        defaultDataId = cleanedBindings['default'] ||
                       (cleanedBindings[templateFields[0]] || '');
      }

      // 處理預覽圖和更新狀態
      if (device._id) {
        try {
          if (formData.updateDisplay) {
            // 用戶選擇了"立即更新顯示"，直接保存預覽圖，讓預覽圖判斷區塊自動改變更新狀態
            if (previewImage) {
              console.log('用戶選擇了立即更新，保存預覽圖到設備記錄，預覽圖判斷區塊會自動改變更新狀態');
              // 不更新imageCode，因為sendDevicePreviewToGateway會處理
              await savePreviewImageToDevice(device._id, previewImage, store?.id, false, false);
              console.log('預覽圖成功保存到設備記錄，預覽圖判斷區塊會自動處理狀態更新');
            }
          } else {
            // 用戶未選擇"立即更新顯示"，先保存預覽圖並更新imageCode，然後再修改狀態為"未更新"
            if (previewImage) {
              console.log('用戶未選擇立即更新，保存預覽圖到數據庫並更新imageCode和imageUpdateStatus');

              // 保存預覽圖到數據庫，同時更新imageCode和imageUpdateStatus
              await savePreviewImageToDevice(device._id, previewImage, store?.id, true, true);
              console.log('預覽圖、imageCode和imageUpdateStatus已更新');
            } else {
              // 沒有預覽圖，只更新狀態
              console.log('沒有預覽圖，只設置imageUpdateStatus為"未更新"');
              await updateDevice(device._id, {
                imageUpdateStatus: '未更新',
                updatedAt: new Date()
              }, store?.id);
            }
          }
        } catch (previewError) {
          console.error('處理預覽圖或更新狀態失敗:', previewError);
          // 繼續執行，不中斷流程
        }
      }

      // 使用新的專用函數更新設備數據綁定
      // 如果用戶選擇了"立即更新顯示"，則自動發送到網關
      // 否則，設置 sendToGateway 為 false
      await updateDeviceDataBindings(
        device._id || '',
        {
          templateId: formData.templateId,
          dataId: defaultDataId, // 為保持向下兼容
          dataBindings: cleanedBindings // 直接傳遞對象，API會處理轉換
        },
        store?.id,
        {
          // 如果用戶選擇了"立即更新顯示"，則自動發送到網關
          sendToGateway: formData.updateDisplay
        }
      );

      onSuccess();
      onClose();
    } catch (error) {
      console.error('綁定數據失敗:', error);
      setErrors(prev => ({
        ...prev,
        submit: '綁定數據失敗，請稍後再試'
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen || !device) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-5xl">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">綁定數據</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        {errors.submit && (
          <div className="p-4 bg-red-50 border-l-4 border-red-500 text-red-700 flex items-center">
            <AlertCircle size={18} className="mr-2" />
            <span>{errors.submit}</span>
          </div>
        )}

        <div className="flex flex-row">
          {/* 左側表單 */}
          <div className="w-1/2 border-r">
            <form onSubmit={handleSubmit} className="p-4">
              <div className="space-y-4">
                {/* 裝置信息 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    裝置 MAC : {device.macAddress}
                  </label>
                </div>

                {/* 板模 ID 選擇 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    板模選擇
                  </label>
                  <select
                    name="templateId"
                    value={formData.templateId}
                    onChange={handleTemplateChange}
                    className={`w-full p-2 border rounded-md ${errors.templateId ? 'border-red-500' : 'border-gray-300'}`}
                  >
                    <option value="">請選擇板模</option>
                    {templates.map(template => (
                      <option key={template.id} value={template.id}>
                        {template.name}
                      </option>
                    ))}
                  </select>
                  {errors.templateId && (
                    <p className="mt-1 text-sm text-red-500 flex items-center">
                      <AlertCircle size={16} className="mr-1" />
                      {errors.templateId}
                    </p>
                  )}
                </div>                  {/* 預覽區塊 */}
                <div>
                  <div className="mb-2 font-medium text-gray-700">預覽</div>
                  <div
                    className={`border border-gray-300 rounded-md bg-gray-50 flex items-center justify-center relative`}
                    style={{
                      overflow: 'hidden',
                      height: isPortraitTemplate ? '400px' : '300px', // 縱向模板高度增加
                      transition: 'height 0.3s' // 平滑過渡效果
                    }}>
                    <div className="w-full h-full flex items-center justify-center" style={{ padding: '20px' }}>
                      {selectedTemplate ? (
                        <div className="flex items-center justify-center" style={{
                          width: isPortraitTemplate ? '50%' : '80%', // 縱向模板寬度縮小
                          height: isPortraitTemplate ? '90%' : '80%', // 縱向模板高度增加
                          backgroundColor: '#f9f9f9',
                          border: '1px solid #e1e1e1',
                          position: 'relative',
                          overflow: 'hidden', // 防止內容溢出
                          transition: 'width 0.3s, height 0.3s' // 平滑過渡效果
                        }}>
                          {/* 根據模板類型和數據綁定狀態決定如何顯示預覽 */}
                          {selectedTemplate ? (
                            // 無論是否有數據欄位，都使用 PreviewComponent 生成預覽圖
                            <div style={{
                              position: 'absolute',
                              top: '0',
                              left: '0',
                              right: '0',
                              bottom: '0',
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center',
                              overflow: 'hidden' // 防止內容溢出
                            }}>
                              <PreviewComponent
                                key={`${formData.templateId}_${formData._updateTimestamp || 'initial'}`} // 添加 key 屬性，確保在模板或數據更新時重新渲染
                                template={selectedTemplate}
                                bindingData={formData.dataBindings}
                                storeData={storeData}
                                effectType="blackAndWhite"
                                threshold={128}
                                onPreviewGenerated={handlePreviewGenerated}
                              />
                            </div>
                          ) : (
                            // 未選擇模板
                            <p className="text-gray-500">請選擇板模以查看預覽</p>
                          )}
                        </div>
                      ) : previewImage ? (
                        /* 如果沒有選擇模板但有預覽圖（可能是從之前的選擇保留的），顯示預覽圖 */
                        <div className="flex items-center justify-center" style={{
                          width: '80%',
                          height: '80%',
                          backgroundColor: '#f9f9f9',
                          border: '1px solid #e1e1e1'
                        }}>
                          <img
                            src={previewImage}
                            alt="預覽"
                            style={{
                              maxWidth: isPortraitTemplate ? '70%' : '95%',
                              maxHeight: isPortraitTemplate ? '95%' : '95%',
                              objectFit: 'contain'
                            }}
                            onLoad={(e) => {
                              // 獲取圖片的原始尺寸
                              const img = e.target as HTMLImageElement;
                              const imgIsPortrait = img.naturalHeight > img.naturalWidth;

                              // 根據圖片方向調整樣式
                              if (imgIsPortrait) {
                                img.style.maxHeight = '95%';
                                img.style.maxWidth = '70%';
                              } else {
                                img.style.maxWidth = '95%';
                                img.style.maxHeight = '95%';
                              }
                            }}
                          />
                        </div>
                      ) : (
                        /* 如果既沒有選擇模板也沒有預覽圖，顯示提示信息 */
                        <p className="text-gray-500">請選擇板模以查看預覽</p>
                      )}
                    </div>

                    {/* 下載按鈕 */}
                    {previewImage && (
                      <button
                        onClick={(e) => {
                          // 阻止事件冒泡，防止觸發關閉視窗的操作
                          e.preventDefault();
                          e.stopPropagation();

                          // 創建一個臨時的a標籤來下載圖片
                          const link = document.createElement('a');
                          link.href = previewImage;
                          link.download = `預覽圖_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.png`;
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);

                          console.log('下載預覽圖');

                          // 防止事件繼續傳播
                          return false;
                        }}
                        className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white p-1.5 rounded-md hover:bg-opacity-70 transition-opacity"
                        title="下載預覽圖"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                          <polyline points="7 10 12 15 17 10"></polyline>
                          <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                      </button>
                    )}
                  </div>
                </div>


              </div>
            </form>
          </div>          {/* 右側綁定資料索引 */}
          <div className="w-1/2 p-4">
            <div className="h-full">
              <div className="mb-2 font-medium text-gray-700">綁定資料索引</div>
              {selectedTemplate && dataIndices.length > 0 ? (
                <div className="border border-gray-200 rounded-md p-3 bg-gray-50">
                  <div className="max-h-80 overflow-y-auto">
                    {dataIndices.map(dataIndex => (
                      <div key={dataIndex.fieldId} className="mb-3">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {dataIndex.displayName}
                        </label>
                        <select
                          value={formData.dataBindings[dataIndex.fieldId] || ''}
                          onChange={(e) => handleDataBindingChange(dataIndex.fieldId, e.target.value)}
                          className="w-full p-2 border border-gray-300 rounded-md"
                        >
                          <option value="">請選擇數據</option>
                          {/* 添加調試信息 */}
                          {(() => {
                            console.log('=== 下拉選單渲染 ===');
                            console.log('storeData 類型:', Array.isArray(storeData) ? 'Array' : typeof storeData);
                            console.log('storeData 長度:', storeData?.length || 0);
                            if (storeData.length > 0) {
                              console.log('storeData 第一項:', storeData[0]);
                            }
                            console.log('=== 下拉選單選項 ===');
                            return null;
                          })()}

                          {storeData.map(data => {
                            // 生成唯一的鍵值 - 優先使用 uid 作為鍵值，因為它是唯一的
                            const key = data.uid || data._id || data.id;

                            // 生成顯示的標籤
                            let label = '';

                            // 根據需求，只顯示ID內容，不顯示門店名稱前綴
                            if (data.id) {
                              // 優先使用名稱
                              label = data.id;
                            } else if (data.id) {
                              // 其次使用ID
                              label = data.name;
                            } else if (data.uid) {
                              // 如果有 uid 但沒有 id 和 name
                              label = `數據 ${data.uid.substring(0, 8)}`;
                            } else {
                              // 如果都沒有，顯示未命名
                              label = '未命名';
                            }

                            // 使用id作為值，如果沒有則使用uid或_id
                            const value = data.id || data.uid || data._id;
                            if (!value) {
                              console.warn('數據項缺少唯一標識符:', data);
                              return null; // 跳過沒有唯一標識符的數據項
                            }

                            // 添加調試信息
                            console.log(`選項 ${key}:`, {
                              key,
                              value,
                              label,
                              uid: data.uid,
                              _id: data._id,
                              id: data.id,
                              name: data.name,
                              storeId: data.storeId,
                              _storeName: data._storeName
                            });

                            return (
                              <option key={key} value={value}>
                                {label}
                              </option>
                            );
                          }).filter(Boolean) /* 過濾掉 null 值 */}

                          {(() => {
                            console.log('=== 下拉選單渲染完成 ===');
                            return null;
                          })()}

                        </select>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center p-3 border border-gray-200 rounded-md bg-gray-50">
                  <p className="text-gray-500">
                    {formData.templateId ? '無須綁定資料' : '請先選擇板模'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 更新選項區域 */}
        <div className="border-t border-gray-200 pt-4 px-4">
          <div className="flex items-center mb-4">
            <input
              type="checkbox"
              id="updateDisplay"
              checked={formData.updateDisplay}
              onChange={(e) => setFormData({
                ...formData,
                updateDisplay: e.target.checked
              })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="updateDisplay" className="ml-2 block text-sm text-gray-700">
              立即更新顯示 (將數據綁定更新發送到網關)
            </label>
          </div>
        </div>

        {/* 固定在底部的按鈕區域 */}
        <div className="border-t border-gray-200 p-4 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            disabled={isSubmitting}
          >
            取消
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
            disabled={isSubmitting}
          >
            {isSubmitting ? '處理中...' : '確定'}
          </button>
        </div>
      </div>
    </div>
  );
}

// 確保使用正確的默認導出語法
export default BindDeviceDataModal;
