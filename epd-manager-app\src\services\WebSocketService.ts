// EPD Manager App - WebSocket 服務

import { 
  Gateway, 
  Device, 
  DeviceConfig, 
  WebSocketMessage, 
  ConnectionStatus 
} from '../types';
import { 
  WEBSOCKET_CONFIG, 
  WS_MESSAGE_TYPES, 
  CONNECTION_STATUS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES 
} from '../utils/constants';
import { 
  generateRandomMac, 
  generateBatteryLevel, 
  generateRSSI, 
  generateImageCode,
  generateDefaultDevices 
} from '../utils/generators';

export class WebSocketService {
  private ws: WebSocket | null = null;
  private isConnected: boolean = false;
  private connectionStatus: ConnectionStatus = CONNECTION_STATUS.DISCONNECTED;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = WEBSOCKET_CONFIG.MAX_RECONNECT_ATTEMPTS;
  
  // 定時器
  private pingInterval: NodeJS.Timeout | null = null;
  private deviceStatusInterval: NodeJS.Timeout | null = null;
  private gatewayInfoInterval: NodeJS.Timeout | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  
  // 設備管理
  private customDevices: Device[] = [];
  private deviceImageCodes: Record<string, string> = {};
  
  // 事件監聽器
  private messageHandlers: Map<string, (message: WebSocketMessage) => void> = new Map();
  private statusChangeHandlers: Set<(status: ConnectionStatus) => void> = new Set();
  
  // 當前網關信息
  private currentGateway: Gateway | null = null;
  private currentStoreId: string | null = null;

  constructor() {
    this.setupDefaultMessageHandlers();
  }

  /**
   * 設置默認消息處理器
   */
  private setupDefaultMessageHandlers(): void {
    this.addMessageHandler(WS_MESSAGE_TYPES.WELCOME, this.handleWelcomeMessage.bind(this));
    this.addMessageHandler(WS_MESSAGE_TYPES.PONG, this.handlePongMessage.bind(this));
    this.addMessageHandler(WS_MESSAGE_TYPES.GATEWAY_INFO_ACK, this.handleGatewayInfoAck.bind(this));
    this.addMessageHandler(WS_MESSAGE_TYPES.UPDATE_PREVIEW, this.handleImageUpdate.bind(this));
  }

  /**
   * 連接到網關
   */
  async connectToGateway(gateway: Gateway, storeId: string): Promise<boolean> {
    try {
      this.currentGateway = gateway;
      this.currentStoreId = storeId;
      
      // 檢查 WebSocket 配置
      if (!gateway.websocket) {
        throw new Error('網關缺少 WebSocket 配置');
      }

      this.setConnectionStatus(CONNECTION_STATUS.CONNECTING);

      // 構建 WebSocket URL
      const url = `${gateway.websocket.url}?token=${gateway.websocket.token}`;
      console.log('嘗試連接到 WebSocket:', url);

      this.ws = new WebSocket(url);
      
      return new Promise((resolve, reject) => {
        if (!this.ws) {
          reject(false);
          return;
        }

        this.ws.onopen = () => {
          console.log('WebSocket 連接已建立');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.setConnectionStatus(CONNECTION_STATUS.CONNECTED);
          this.setupMessageHandler();
          resolve(true);
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket 連接錯誤:', error);
          this.setConnectionStatus(CONNECTION_STATUS.ERROR);
          reject(false);
        };

        this.ws.onclose = (event) => {
          console.log(`WebSocket 連接已關閉: ${event.code} ${event.reason}`);
          this.handleDisconnection();
        };

        // 設置連接超時
        setTimeout(() => {
          if (!this.isConnected) {
            this.ws?.close();
            reject(false);
          }
        }, 10000); // 10 秒超時
      });
    } catch (error: any) {
      console.error('連接到網關失敗:', error);
      this.setConnectionStatus(CONNECTION_STATUS.ERROR);
      return false;
    }
  }

  /**
   * 開始網關模擬
   */
  startGatewaySimulation(gateway: Gateway): void {
    if (!this.isConnected || !this.ws) {
      throw new Error('WebSocket 未連接');
    }

    console.log('開始網關模擬行為（僅保持連線，不發送設備報告）');

    // 發送初始 ping 消息
    this.sendPingMessage();

    // 設置定期心跳（25秒間隔）
    this.pingInterval = setInterval(() => {
      this.sendPingMessage();
    }, WEBSOCKET_CONFIG.PING_INTERVAL);

    // 設置定期網關信息發送（30秒間隔）
    this.gatewayInfoInterval = setInterval(() => {
      this.sendGatewayInfoMessage(gateway);
    }, WEBSOCKET_CONFIG.GATEWAY_INFO_INTERVAL);

    // 移除自動發送設備狀態的部分，因為這是模擬 GATEWAY 發送 REPORT
    // 實際的 GATEWAY 會自己發送設備狀態，APP 不應該模擬這個行為
    console.log('注意：已移除自動發送設備狀態報告，等待實際 GATEWAY 連接');
  }

  /**
   * 設置消息處理器
   */
  private setupMessageHandler(): void {
    if (!this.ws) return;

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('解析 WebSocket 消息失敗:', error);
      }
    };
  }

  /**
   * 處理接收到的消息
   */
  private handleMessage(message: WebSocketMessage): void {
    console.log('收到 WebSocket 消息:', message.type, message);

    // 調用對應的消息處理器
    const handler = this.messageHandlers.get(message.type);
    if (handler) {
      handler(message);
    } else {
      console.warn('未知的消息類型:', message.type);
    }

    // 調用通用消息處理器
    const allHandler = this.messageHandlers.get('all');
    if (allHandler) {
      allHandler(message);
    }
  }

  /**
   * 處理歡迎消息
   */
  private handleWelcomeMessage(message: WebSocketMessage): void {
    console.log('收到歡迎消息，連接成功建立');
    
    // 收到歡迎消息後立即發送網關信息
    if (this.currentGateway) {
      setTimeout(() => {
        this.sendGatewayInfoMessage(this.currentGateway!);
      }, 1000);
    }
  }

  /**
   * 處理 pong 消息
   */
  private handlePongMessage(message: WebSocketMessage): void {
    console.log('收到服務器 pong 回應');
  }

  /**
   * 處理網關信息確認消息
   */
  private handleGatewayInfoAck(message: WebSocketMessage): void {
    if (message.success === false && message.fatal === true) {
      console.error('嚴重錯誤: 服務器因安全問題強制中斷連線');
      console.error('錯誤原因:', message.message);
      this.disconnect();
    } else if (message.success === false) {
      console.warn('網關信息更新失敗:', message.message);
    } else {
      console.log('網關信息更新成功');
    }
  }

  /**
   * 處理圖像更新消息
   */
  private handleImageUpdate(message: WebSocketMessage): void {
    console.log('收到圖像更新消息');

    if (message.deviceMac && message.imageCode) {
      this.deviceImageCodes[message.deviceMac] = message.imageCode;
      console.log(`更新設備 ${message.deviceMac} 的 imageCode: ${message.imageCode}`);
    }

    // 如果有圖像數據，可以進一步處理
    if (message.imageData) {
      console.log('收到圖像數據，長度:', message.imageData.length);
      // 這裡可以添加圖像保存邏輯
    }
  }

  /**
   * 發送 ping 消息
   */
  private sendPingMessage(): void {
    const pingMessage = {
      type: WS_MESSAGE_TYPES.PING,
      timestamp: Date.now()
    };
    this.sendMessage(pingMessage);
  }

  /**
   * 發送設備狀態消息
   */
  private sendDeviceStatusMessage(): void {
    // 生成預設設備
    const defaultDevices = generateDefaultDevices(3);
    
    // 為預設設備添加 imageCode
    defaultDevices.forEach(device => {
      if (this.deviceImageCodes[device.macAddress]) {
        device.data.imageCode = this.deviceImageCodes[device.macAddress];
      }
    });

    // 處理自定義設備
    const processedCustomDevices = this.customDevices.map(device => {
      const processedDevice = { ...device };
      if (this.deviceImageCodes[device.macAddress]) {
        processedDevice.data = {
          ...processedDevice.data,
          imageCode: this.deviceImageCodes[device.macAddress]
        };
      }
      // 更新動態數據
      processedDevice.data.battery = generateBatteryLevel();
      processedDevice.data.rssi = generateRSSI();
      return processedDevice;
    });

    // 合併所有設備
    const allDevices = [...defaultDevices, ...processedCustomDevices];

    const deviceStatusMessage = {
      type: WS_MESSAGE_TYPES.DEVICE_STATUS,
      devices: allDevices,
      timestamp: Date.now()
    };

    this.sendMessage(deviceStatusMessage);
    console.log(`發送設備狀態消息，共 ${allDevices.length} 個設備`);
  }

  /**
   * 發送網關信息消息
   */
  private sendGatewayInfoMessage(gateway: Gateway): void {
    const gatewayInfoMessage = {
      type: WS_MESSAGE_TYPES.GATEWAY_INFO,
      info: {
        macAddress: gateway.macAddress,
        model: gateway.model || 'GW-2000',
        wifiFirmwareVersion: gateway.wifiFirmwareVersion || '1.0.0',
        btFirmwareVersion: gateway.btFirmwareVersion || '2.0.0',
        ipAddress: gateway.ipAddress
      },
      timestamp: Date.now()
    };

    this.sendMessage(gatewayInfoMessage);
    console.log('發送網關信息消息');
  }

  /**
   * 發送消息到服務器
   */
  private sendMessage(message: any): void {
    if (this.ws && this.isConnected) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket 未連接，無法發送消息');
    }
  }

  /**
   * 添加自定義設備
   */
  addCustomDevice(deviceConfig: DeviceConfig): void {
    const customDevice: Device = {
      macAddress: deviceConfig.macAddress,
      status: deviceConfig.status || 'online',
      data: {
        size: deviceConfig.size || '2.9"',
        battery: generateBatteryLevel(),
        rssi: generateRSSI(),
        colorType: deviceConfig.colorType || 'BW'
      }
    };

    this.customDevices.push(customDevice);
    
    // 如果提供了 imageCode，存儲到本地映射
    if (deviceConfig.imageCode) {
      this.deviceImageCodes[deviceConfig.macAddress] = deviceConfig.imageCode;
    }

    console.log(`添加自定義設備: ${deviceConfig.macAddress}`);
  }

  /**
   * 移除自定義設備
   */
  removeCustomDevice(index: number): boolean {
    if (index >= 0 && index < this.customDevices.length) {
      const removedDevice = this.customDevices.splice(index, 1)[0];
      delete this.deviceImageCodes[removedDevice.macAddress];
      console.log(`移除設備: ${removedDevice.macAddress}`);
      return true;
    }
    return false;
  }

  /**
   * 獲取設備列表
   */
  getDeviceList(): Device[] {
    const defaultDevices = generateDefaultDevices(3);
    return [...defaultDevices, ...this.customDevices];
  }

  /**
   * 請求設備預覽圖像
   */
  requestDeviceImage(macAddress: string): void {
    const requestImageMessage = {
      type: WS_MESSAGE_TYPES.REQUEST_PREVIEW_IMAGE,
      macAddress: macAddress,
      timestamp: Date.now()
    };

    this.sendMessage(requestImageMessage);
    console.log(`請求設備 ${macAddress} 的預覽圖像`);
  }

  /**
   * 添加消息處理器
   */
  addMessageHandler(messageType: string, handler: (message: WebSocketMessage) => void): void {
    this.messageHandlers.set(messageType, handler);
  }

  /**
   * 移除消息處理器
   */
  removeMessageHandler(messageType: string): void {
    this.messageHandlers.delete(messageType);
  }

  /**
   * 添加狀態變化監聽器
   */
  addStatusChangeHandler(handler: (status: ConnectionStatus) => void): void {
    this.statusChangeHandlers.add(handler);
  }

  /**
   * 移除狀態變化監聽器
   */
  removeStatusChangeHandler(handler: (status: ConnectionStatus) => void): void {
    this.statusChangeHandlers.delete(handler);
  }

  /**
   * 設置連接狀態
   */
  private setConnectionStatus(status: ConnectionStatus): void {
    this.connectionStatus = status;
    this.statusChangeHandlers.forEach(handler => handler(status));
  }

  /**
   * 處理斷線
   */
  private handleDisconnection(): void {
    this.isConnected = false;
    this.setConnectionStatus(CONNECTION_STATUS.DISCONNECTED);
    this.cleanup();
    
    // 自動重連邏輯
    if (this.reconnectAttempts < this.maxReconnectAttempts && this.currentGateway && this.currentStoreId) {
      this.reconnectAttempts++;
      console.log(`嘗試重連 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      this.reconnectTimeout = setTimeout(() => {
        this.connectToGateway(this.currentGateway!, this.currentStoreId!);
      }, WEBSOCKET_CONFIG.RECONNECT_INTERVAL);
    } else {
      console.log('達到最大重連次數，停止重連');
      this.setConnectionStatus(CONNECTION_STATUS.ERROR);
    }
  }

  /**
   * 清理資源
   */
  private cleanup(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
    if (this.deviceStatusInterval) {
      clearInterval(this.deviceStatusInterval);
      this.deviceStatusInterval = null;
    }
    if (this.gatewayInfoInterval) {
      clearInterval(this.gatewayInfoInterval);
      this.gatewayInfoInterval = null;
    }
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }

  /**
   * 斷開連接
   */
  disconnect(): void {
    console.log('主動斷開 WebSocket 連接');
    this.cleanup();
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    this.isConnected = false;
    this.reconnectAttempts = this.maxReconnectAttempts; // 阻止自動重連
    this.setConnectionStatus(CONNECTION_STATUS.DISCONNECTED);
    
    // 清理狀態
    this.currentGateway = null;
    this.currentStoreId = null;
    this.customDevices = [];
    this.deviceImageCodes = {};
  }

  /**
   * 獲取連接狀態
   */
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  /**
   * 是否已連接
   */
  isWebSocketConnected(): boolean {
    return this.isConnected;
  }

  /**
   * 獲取當前網關
   */
  getCurrentGateway(): Gateway | null {
    return this.currentGateway;
  }
}

// 創建單例實例
export const webSocketService = new WebSocketService();
export default webSocketService;
