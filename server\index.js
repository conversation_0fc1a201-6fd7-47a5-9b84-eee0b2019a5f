// server/index.js
const express = require('express');
const http = require('http');
const { MongoClient, GridFSBucket } = require('mongodb');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const { createTemplateValidatorMiddleware } = require('./utils/templateValidation'); // 導入模板驗證中間件創建函數

// 導入所有 API 路由
const { router: dataFieldApiRouter, initDB: initDataFieldApi } = require('./routes/dataFieldApi'); // 導入資料欄位 API 路由及初始化函數
const { router: storeDataApiRouter, initDB: initStoreDataApi } = require('./routes/storeDataApi'); // 導入門店資料 API 路由及初始化函數
const { router: systemSpecificDataApiRouter, initDB: initSystemSpecificDataApi } = require('./routes/systemSpecificDataApi'); // 導入系統專屬數據 API 路由及初始化函數
const { router: storeApiRouter, initDB: initStoreApi } = require('./routes/storeApi'); // 導入門店管理 API 路由及初始化函數
const { router: sysConfigApiRouter, initDB: initSysConfigApi } = require('./routes/sysConfigApi'); // 導入系統配置 API 路由及初始化函數
const { router: fileApiRouter, initDB: initFileApi } = require('./routes/fileApi'); // 導入文件 API 路由及初始化函數
const { router: templateApiRouter, initDB: initTemplateApi } = require('./routes/templateApi'); // 導入模板 API 路由及初始化函數
const { router: deviceApiRouter, initDB: initDeviceApi } = require('./routes/deviceApi'); // 導入設備管理 API 路由及初始化函數
const { router: gatewayApiRouter, initDB: initGatewayApi } = require('./routes/gatewayApi'); // 導入網關管理 API 路由及初始化函數
const templateValidatorApiRouter = require('./routes/templateValidatorApi'); // 導入檢查碼相關 API 路由

// 導入用戶認證和權限管理相關路由
const { router: authApiRouter, initDB: initAuthApi } = require('./routes/authApi'); // 導入認證 API 路由
const { router: userApiRouter, initDB: initUserApi } = require('./routes/userApi'); // 導入用戶管理 API 路由
const { router: roleApiRouter, initDB: initRoleApi } = require('./routes/roleApi'); // 導入角色管理 API 路由
const { router: permissionApiRouter, initDB: initPermissionApi } = require('./routes/permissionApi'); // 導入權限分配 API 路由

// 導入 WebSocket 服務
const {
  initDB: initWebSocketServiceDB,
  initWebSocketServer,
  setupHeartbeatCheck
} = require('./services/websocketService');

// 導入設備狀態服務
const {
  initDB: initDeviceStatusServiceDB,
  startDeviceStatusChecker
} = require('./services/deviceStatusService');

// 導入發送預覽圖到網關服務
const sendPreviewToGateway = require('./services/sendPreviewToGateway');

// 導入網關 API 中的 JWT 密鑰設置函數
const { setJwtSecret } = require('./routes/gatewayApi');

const app = express();
const server = http.createServer(app);  // 創建 HTTP 服務器
const port = 3001;

// JWT 密鑰配置
const jwtSecret = process.env.JWT_SECRET || 'your_jwt_secret_here'; // 應從環境變數獲取

// 自定義 CORS 中間件
app.use((req, res, next) => {
  // 允許來自前端的請求（支持多個端口）
  const allowedOrigins = ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175'];
  const origin = req.headers.origin;

  if (allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');

  // 處理 OPTIONS 請求
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }

  // 記錄請求信息
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);

  next();
});

// 設置較大的請求體限制以支援大尺寸模板的預覽圖
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));
app.use(cookieParser()); // 解析 cookie
app.use(createTemplateValidatorMiddleware()); // 使用模板驗證中間件

// MongoDB 連接
// 使用本地 MongoDB
const uri = 'mongodb://127.0.0.1:27017';
const dbName = 'resourceManagement';
let client = null;
let gridFSBucket = null;

// 連接數據庫並返回客戶端和數據庫物件
async function connectDB() {
  try {
    if (!client) {
      console.log('嘗試連接 MongoDB...');
      client = new MongoClient(uri);
      await client.connect();
      console.log('MongoDB 連接成功');
      const db = client.db(dbName);

      // 檢查數據庫是否存在
      const admin = client.db().admin();
      const dbs = await admin.listDatabases();
      const dbExists = dbs.databases.some(d => d.name === dbName);
      console.log(`數據庫 ${dbName} ${dbExists ? '已存在' : '不存在'}`);

      // 檢查必要的集合是否存在
      const collections = await db.listCollections().toArray();
      const collectionNames = collections.map(c => c.name);
      console.log('現有集合:', collectionNames.join(', '));

      // 確保必要的集合存在
      const requiredCollections = ['users', 'roles', 'permissions'];
      for (const collName of requiredCollections) {
        if (!collectionNames.includes(collName)) {
          console.log(`創建集合: ${collName}`);
          await db.createCollection(collName);
        }
      }

      // 檢查是否有管理員用戶
      const usersCount = await db.collection('users').countDocuments();
      console.log(`用戶數量: ${usersCount}`);

      gridFSBucket = new GridFSBucket(db);
    }
    return { client, db: client.db(dbName), gridFSBucket };
  } catch (error) {
    console.error('MongoDB 連接錯誤:', error);
    throw error;
  }
}

// 添加一個不需要認證的測試路由
app.get('/dbtest', async (req, res) => {
  try {
    console.log('收到數據庫測試請求');

    // 獲取數據庫連接
    const { db } = await connectDB();

    // 檢查數據庫連接
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);

    // 檢查用戶集合
    const usersCount = await db.collection('users').countDocuments();

    console.log('數據庫測試成功:', { collections: collectionNames, usersCount });

    res.json({
      status: 'ok',
      message: '數據庫連接正常',
      collections: collectionNames,
      usersCount
    });
  } catch (error) {
    console.error('數據庫測試錯誤:', error);
    res.status(500).json({ error: '數據庫測試失敗: ' + error.message });
  }
});

// 中間件：將數據庫連接添加到請求對象
app.use(async (req, res, next) => {
  try {
    const { db } = await connectDB();
    req.db = db;
    next();
  } catch (error) {
    console.error('數據庫連接錯誤:', error);
    res.status(500).json({ error: '數據庫連接錯誤' });
  }
});

// 初始化所有 API 的數據庫連接
// 將 connectDB 函數共享給 API 路由
initDataFieldApi(connectDB);
initStoreDataApi(connectDB);
initSystemSpecificDataApi(connectDB);
initStoreApi(connectDB);
initSysConfigApi(connectDB);
initFileApi(connectDB);
initTemplateApi(connectDB);
initDeviceApi(connectDB);
initGatewayApi(connectDB);

// 初始化用戶認證和權限管理相關路由的數據庫連接
initAuthApi(connectDB);
initUserApi(connectDB);
initRoleApi(connectDB);
initPermissionApi(connectDB);

// 初始化 WebSocket 服務的數據庫連接
initWebSocketServiceDB(connectDB);

// 初始化設備狀態服務的數據庫連接
initDeviceStatusServiceDB(connectDB);

// 初始化發送預覽圖到網關服務的數據庫連接
sendPreviewToGateway.initDB(connectDB);

// 設置網關 API 的 JWT 密鑰
setJwtSecret(jwtSecret);

// 添加一個測試路由
app.get('/test', async (req, res) => {
  try {
    const { db } = await connectDB();

    // 檢查數據庫連接
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);

    // 檢查用戶集合
    const usersCount = await db.collection('users').countDocuments();

    res.json({
      status: 'ok',
      message: '數據庫連接正常',
      collections: collectionNames,
      usersCount
    });
  } catch (error) {
    console.error('測試路由錯誤:', error);
    res.status(500).json({ error: '測試失敗: ' + error.message });
  }
});

// 使用 API 路由
app.use('/api', dataFieldApiRouter);
app.use('/api', storeDataApiRouter);
app.use('/api', systemSpecificDataApiRouter);
app.use('/api', storeApiRouter);
app.use('/api', sysConfigApiRouter);
app.use('/api', fileApiRouter);
app.use('/api', templateApiRouter);
app.use('/api', deviceApiRouter);
app.use('/api', gatewayApiRouter);
app.use('/api', templateValidatorApiRouter);

// 使用用戶認證和權限管理相關路由
app.use('/api', authApiRouter);
app.use('/api', userApiRouter);
app.use('/api', roleApiRouter);
app.use('/api', permissionApiRouter);

// 啟動服務器
server.listen(port, () => {
  console.log(`後端服務運行在 http://localhost:${port}`);
  console.log(`測試連接: http://localhost:${port}/dbtest`);

  // 初始化 WebSocket 服務
  const wss = initWebSocketServer(server, jwtSecret);

  // 設置 WebSocket 心跳檢測
  setupHeartbeatCheck();

  // 啟動設備狀態自動檢查服務
  startDeviceStatusChecker();

  console.log(`WebSocket 服務已啟動，連接格式: ws://localhost:${port}/ws/store/{storeId}/gateway/{gatewayId}?token={jwt_token}`);
  console.log(`測試 WebSocket: node tests/test-ws-client.js [gatewayId] [storeId]`);
});
