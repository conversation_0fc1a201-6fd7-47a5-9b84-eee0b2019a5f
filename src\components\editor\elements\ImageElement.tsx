import React, { useState, useRef, useEffect } from 'react';
import { TemplateElement } from '../../../types';
import { ControlHandle } from './ShapeElement';
import { constrainElementToCanvas } from '../canvasUtils';
import { Image as LucideImage } from 'lucide-react';

interface ImageElementProps {
  element: TemplateElement;
  isSelected: boolean;
  onSelect: (id: string, e?: React.MouseEvent) => void;
  onUpdate: (id: string, updates: Partial<TemplateElement>) => void;
  zoom?: number;
  setSelectedTool?: (tool: string | null) => void;
  selectedElementIds?: string[];
  moveSelectedElements?: (dx: number, dy: number) => void;
  isMultiMoving?: boolean;
}

export const ImageElement: React.FC<ImageElementProps> = ({
  element,
  isSelected,
  onSelect,
  onUpdate,
  zoom = 100,
  setSelectedTool,
  selectedElementIds = [],
  moveSelectedElements,
  isMultiMoving = false
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const imgRef = useRef<HTMLImageElement>(null);

  const [isDragging, setIsDragging] = useState(false);
  const [startDragPosition, setStartDragPosition] = useState({ x: 0, y: 0 });
  const [isResizing, setIsResizing] = useState(false);
  const [activeHandle, setActiveHandle] = useState<ControlHandle | null>(null);
  const [isRotating, setIsRotating] = useState(false);
  const [rotationStartAngle, setRotationStartAngle] = useState(0);
  const [imageAspectRatio, setImageAspectRatio] = useState<number | null>(null);

  // 是否為多選狀態
  const isMultiSelected = selectedElementIds.length > 1 && selectedElementIds.includes(element.id);

  // 處理滑鼠按下事件 - 開始拖曳
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!elementRef.current || isResizing || isRotating) return;

    e.stopPropagation();
    setIsDragging(true);
    setStartDragPosition({ x: e.clientX, y: e.clientY });

    if (!isSelected) {
      onSelect(element.id);
    }
  };

  // 處理控制點滑鼠按下事件 - 開始調整大小或旋轉
  const handleControlPointMouseDown = (handle: ControlHandle, e: React.MouseEvent) => {
    e.stopPropagation();

    if (handle === ControlHandle.Rotate) {
      setIsRotating(true);
      const rect = elementRef.current?.getBoundingClientRect();
      if (rect) {
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        const startAngle = Math.atan2(
          e.clientY - centerY,
          e.clientX - centerX
        ) * (180 / Math.PI);
        setRotationStartAngle(startAngle - (element.rotation || 0));
      }
    } else {
      setIsResizing(true);
      setActiveHandle(handle);
    }
  };

  // 處理滑鼠移動事件 - 拖曳、調整大小、旋轉
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        const deltaX = e.clientX - startDragPosition.x;
        const deltaY = e.clientY - startDragPosition.y;

        const scaledDeltaX = deltaX / (zoom / 100);
        const scaledDeltaY = deltaY / (zoom / 100);

        if (isMultiSelected && moveSelectedElements) {
          moveSelectedElements(scaledDeltaX, scaledDeltaY);
        } else {
          const newX = Math.round(element.x + scaledDeltaX);
          const newY = Math.round(element.y + scaledDeltaY);

          const canvasElement = elementRef.current?.closest('[data-canvas-width]');
          const canvasWidth = canvasElement ?
            parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10) : 250;
          const canvasHeight = canvasElement ?
            parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10) : 122;

          const constrainedUpdates = constrainElementToCanvas(
            { x: newX, y: newY, width: Math.round(element.width), height: Math.round(element.height) },
            canvasWidth,
            canvasHeight
          );

          onUpdate(element.id, constrainedUpdates);
        }

        setStartDragPosition({ x: e.clientX, y: e.clientY });
      } else if (isRotating && elementRef.current) {
        const rect = elementRef.current.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        const currentAngle = Math.atan2(
          e.clientY - centerY,
          e.clientX - centerX
        ) * (180 / Math.PI);

        let newRotation = currentAngle - rotationStartAngle;

        if (e.shiftKey) {
          newRotation = Math.round(newRotation / 15) * 15;
        }

        onUpdate(element.id, { rotation: newRotation });
      } else if (isResizing && activeHandle) {
        const rotation = element.rotation || 0;
        const rotationRad = (rotation * Math.PI) / 180;
        const cos = Math.cos(rotationRad);
        const sin = Math.sin(rotationRad);

        let scaledMovementX = e.movementX / (zoom / 100);
        let scaledMovementY = e.movementY / (zoom / 100);

        if (rotation !== 0) {
          const rotatedMovementX = scaledMovementX * cos + scaledMovementY * sin;
          const rotatedMovementY = -scaledMovementX * sin + scaledMovementY * cos;

          scaledMovementX = rotatedMovementX;
          scaledMovementY = rotatedMovementY;
        }

        let newX = Math.round(element.x);
        let newY = Math.round(element.y);
        let newWidth = Math.round(element.width);
        let newHeight = Math.round(element.height);

        // 保持圖片比例的調整大小邏輯
        if (imageAspectRatio !== null) {
          // 根據控制點和移動方向調整大小，同時保持比例
          switch (activeHandle) {
            case ControlHandle.TopLeft:
              // 優先使用寬度變化來計算高度
              newWidth = Math.round(element.width - scaledMovementX);
              newHeight = Math.round(newWidth / imageAspectRatio);
              newX = Math.round(element.x + (element.width - newWidth));
              newY = Math.round(element.y + (element.height - newHeight));
              break;
            case ControlHandle.TopRight:
              // 優先使用寬度變化來計算高度
              newWidth = Math.round(element.width + scaledMovementX);
              newHeight = Math.round(newWidth / imageAspectRatio);
              newY = Math.round(element.y + (element.height - newHeight));
              break;
            case ControlHandle.BottomLeft:
              // 優先使用寬度變化來計算高度
              newWidth = Math.round(element.width - scaledMovementX);
              newHeight = Math.round(newWidth / imageAspectRatio);
              newX = Math.round(element.x + (element.width - newWidth));
              break;
            case ControlHandle.BottomRight:
              // 優先使用寬度變化來計算高度
              newWidth = Math.round(element.width + scaledMovementX);
              newHeight = Math.round(newWidth / imageAspectRatio);
              break;
            case ControlHandle.Top:
              // 優先使用高度變化來計算寬度
              newHeight = Math.round(element.height - scaledMovementY);
              newWidth = Math.round(newHeight * imageAspectRatio);
              newX = Math.round(element.x + (element.width - newWidth) / 2); // 保持水平居中
              newY = Math.round(element.y + scaledMovementY);
              break;
            case ControlHandle.Right:
              // 優先使用寬度變化來計算高度
              newWidth = Math.round(element.width + scaledMovementX);
              newHeight = Math.round(newWidth / imageAspectRatio);
              newY = Math.round(element.y + (element.height - newHeight) / 2); // 保持垂直居中
              break;
            case ControlHandle.Bottom:
              // 優先使用高度變化來計算寬度
              newHeight = Math.round(element.height + scaledMovementY);
              newWidth = Math.round(newHeight * imageAspectRatio);
              newX = Math.round(element.x + (element.width - newWidth) / 2); // 保持水平居中
              break;
            case ControlHandle.Left:
              // 優先使用寬度變化來計算高度
              newWidth = Math.round(element.width - scaledMovementX);
              newHeight = Math.round(newWidth / imageAspectRatio);
              newX = Math.round(element.x + scaledMovementX);
              newY = Math.round(element.y + (element.height - newHeight) / 2); // 保持垂直居中
              break;
          }
        } else {
          // 如果沒有圖片比例信息，使用原來的調整邏輯
          switch (activeHandle) {
            case ControlHandle.TopLeft:
              newX = Math.round(element.x + scaledMovementX);
              newY = Math.round(element.y + scaledMovementY);
              newWidth = Math.round(element.width - scaledMovementX);
              newHeight = Math.round(element.height - scaledMovementY);
              break;
            case ControlHandle.TopRight:
              newY = Math.round(element.y + scaledMovementY);
              newWidth = Math.round(element.width + scaledMovementX);
              newHeight = Math.round(element.height - scaledMovementY);
              break;
            case ControlHandle.BottomLeft:
              newX = Math.round(element.x + scaledMovementX);
              newWidth = Math.round(element.width - scaledMovementX);
              newHeight = Math.round(element.height + scaledMovementY);
              break;
            case ControlHandle.BottomRight:
              newWidth = Math.round(element.width + scaledMovementX);
              newHeight = Math.round(element.height + scaledMovementY);
              break;
            case ControlHandle.Top:
              newY = Math.round(element.y + scaledMovementY);
              newHeight = Math.round(element.height - scaledMovementY);
              break;
            case ControlHandle.Right:
              newWidth = Math.round(element.width + scaledMovementX);
              break;
            case ControlHandle.Bottom:
              newHeight = Math.round(element.height + scaledMovementY);
              break;
            case ControlHandle.Left:
              newX = Math.round(element.x + scaledMovementX);
              newWidth = Math.round(element.width - scaledMovementX);
              break;
          }
        }

        if (newWidth < 5) newWidth = 5;
        if (newHeight < 5) newHeight = 5;

        const canvasElement = elementRef.current?.closest('[data-canvas-width]');
        const canvasWidth = canvasElement ?
          parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10) : 250;
        const canvasHeight = canvasElement ?
          parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10) : 122;

        const constrainedElement = constrainElementToCanvas(
          { x: newX, y: newY, width: newWidth, height: newHeight },
          canvasWidth,
          canvasHeight
        );

        onUpdate(element.id, constrainedElement);
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      setIsResizing(false);
      setIsRotating(false);
      setActiveHandle(null);
    };

    if (isDragging || isResizing || isRotating) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [
    isDragging,
    isResizing,
    isRotating,
    startDragPosition,
    activeHandle,
    rotationStartAngle,
    element,
    onUpdate,
    zoom,
    isMultiSelected,
    moveSelectedElements
  ]);

  // 當圖片加載完成時獲取圖片比例
  const handleImageLoad = () => {
    if (imgRef.current && imgRef.current.naturalWidth && imgRef.current.naturalHeight) {
      const ratio = imgRef.current.naturalWidth / imgRef.current.naturalHeight;
      setImageAspectRatio(ratio);
    }
  };

  // 當圖片URL變更時重置比例
  useEffect(() => {
    if (element.imageUrl) {
      // 創建一個臨時圖片對象來獲取圖片尺寸
      const img = new window.Image();
      img.onload = () => {
        if (img.naturalWidth && img.naturalHeight) {
          const ratio = img.naturalWidth / img.naturalHeight;
          setImageAspectRatio(ratio);
        }
      };
      img.src = element.imageUrl;
    } else {
      setImageAspectRatio(null);
    }
  }, [element.imageUrl]);

  return (
    <div
      ref={elementRef}
      style={{
        position: 'absolute',
        left: element.x,
        top: element.y,
        width: element.width,
        height: element.height,
        cursor: isSelected ? 'move' : 'pointer',
        backgroundColor: 'transparent', // 移除背景色
        transform: element.rotation ? `rotate(${element.rotation}deg)` : undefined,
        transformOrigin: 'center center',
        outline: isSelected ? '1px dashed #3b82f6' : 'none',
        outlineOffset: '2px',
        zIndex: isSelected ? 10 : 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
      onClick={(e) => {
        e.stopPropagation();

        if (isMultiMoving) {
          return;
        }

        if (e.shiftKey && selectedElementIds.length > 0) {
          onSelect(element.id, e);
        } else {
          onSelect(element.id, e);
          if (setSelectedTool) {
            setSelectedTool(null);
          }
        }
      }}
      onMouseDown={handleMouseDown}
      data-element-id={element.id}
    >
      {/* 圖片容器 - 徹底禁用所有事件 */}
      <div style={{
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        pointerEvents: 'none', // 禁用所有事件
        userSelect: 'none', // 防止文字選取
        touchAction: 'none', // 禁用觸控事件
        backgroundColor: 'transparent' // 確保容器也是透明的
      }}>
        {element.imageUrl ? (
          <img
            ref={imgRef}
            src={element.imageUrl}
            alt="圖片元件"
            draggable={false} // 防止圖片被拖曳
            onLoad={handleImageLoad} // 圖片加載完成時獲取比例
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain', // 保持圖片比例
              pointerEvents: 'none', // 禁用所有事件
              userSelect: 'none', // 防止文字選取
              touchAction: 'none', // 禁用觸控事件
              backgroundColor: 'transparent' // 確保圖片背景是透明的
            }}
          />
        ) : (
          <LucideImage
            size={Math.min(element.width, element.height) * 0.5}
            color="#999"
            style={{
              pointerEvents: 'none', // 禁用所有事件
              userSelect: 'none' // 防止文字選取
            }}
            strokeWidth={1.5}
          />
        )}
      </div>

      {/* 控制點 - 只在單選狀態下顯示 */}
      {isSelected && !isMultiSelected && (
        <>
          {/* 角落控制點 */}
          <div
            style={{
              position: 'absolute',
              left: -4,
              top: -4,
              width: 8,
              height: 8,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'nwse-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.TopLeft, e)}
          />
          <div
            style={{
              position: 'absolute',
              right: -4,
              top: -4,
              width: 8,
              height: 8,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'nesw-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.TopRight, e)}
          />
          <div
            style={{
              position: 'absolute',
              left: -4,
              bottom: -4,
              width: 8,
              height: 8,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'nesw-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.BottomLeft, e)}
          />
          <div
            style={{
              position: 'absolute',
              right: -4,
              bottom: -4,
              width: 8,
              height: 8,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'nwse-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.BottomRight, e)}
          />

          {/* 邊緣控制點 */}
          <div
            style={{
              position: 'absolute',
              left: '50%',
              top: -4,
              width: 8,
              height: 8,
              marginLeft: -4,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'ns-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Top, e)}
          />
          <div
            style={{
              position: 'absolute',
              right: -4,
              top: '50%',
              width: 8,
              height: 8,
              marginTop: -4,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'ew-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Right, e)}
          />
          <div
            style={{
              position: 'absolute',
              left: '50%',
              bottom: -4,
              width: 8,
              height: 8,
              marginLeft: -4,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'ns-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Bottom, e)}
          />
          <div
            style={{
              position: 'absolute',
              left: -4,
              top: '50%',
              width: 8,
              height: 8,
              marginTop: -4,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'ew-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Left, e)}
          />

          {/* 旋轉控制點 */}
          <div
            style={{
              position: 'absolute',
              left: '50%',
              top: -25,
              width: 8,
              height: 8,
              marginLeft: -4,
              backgroundColor: 'transparent',
              border: '0.5px dashed #3b82f6',
              borderRadius: '50%',
              cursor: 'url("data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22%3E%3Cpath fill=%22%23000000%22 d=%22M7.11 8.53L5.7 7.11C4.8 8.27 4.24 9.61 4.07 11h2.02c.14-.87.49-1.72 1.02-2.47zM6.09 13H4.07c.17 1.39.72 2.73 1.62 3.89l1.41-1.42c-.52-.75-.87-1.59-1.01-2.47zm1.01 5.32c1.16.9 2.51 1.44 3.9 1.61V17.9c-.87-.15-1.71-.49-2.46-1.03L7.1 18.32zM13 4.07V1L8.45 5.55 13 10V6.09c2.84.48 5 2.94 5 5.91s-2.16 5.43-5 5.91v2.02c3.95-.49 7-3.85 7-7.93s-3.05-7.44-7-7.93z%22/%3E%3C/svg%3E") 12 12, auto',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Rotate, e)}
          />

          {/* 旋轉控制點連接線 */}
          <div
            style={{
              position: 'absolute',
              left: '50%',
              top: -15,
              width: 1,
              height: 15,
              marginLeft: -0.5,
              backgroundColor: '#3b82f6',
              zIndex: 99
            }}
          />
        </>
      )}
    </div>
  );
};