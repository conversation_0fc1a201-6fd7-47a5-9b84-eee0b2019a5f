import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Search, RefreshCw, Plus, Trash2, Edit, AlertCircle, Wifi, Bluetooth, Grid, Trash, MoreHorizontal, Power, Key } from 'lucide-react';
import { Gateway, GatewayStatus } from '../types/gateway';
import { Store } from '../types/store';
import { getAllGateways, deleteGateway, syncGateways, restartGateway, getGateway } from '../utils/api/gatewayApi';
import { GatewayStatusBadge } from './ui/GatewayStatusBadge';
import { AddGatewayModal } from './AddGatewayModal';
import { EditGatewayModal } from './EditGatewayModal';
import { UpgradeWifiFirmwareModal } from './UpgradeWifiFirmwareModal';
import { UpgradeBtFirmwareModal } from './UpgradeBtFirmwareModal';

interface GatewaysPageProps {
  store: Store;
}

export function GatewaysPage({ store }: GatewaysPageProps) {
  const { t } = useTranslation();
  const [gateways, setGateways] = useState<Gateway[]>([]);
  const [filteredGateways, setFilteredGateways] = useState<Gateway[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<GatewayStatus | ''>('');
  const [modelFilter, setModelFilter] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [notification, setNotification] = useState<{ message: string, type: 'success' | 'error' } | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showWifiUpgradeModal, setShowWifiUpgradeModal] = useState(false);
  const [showBtUpgradeModal, setShowBtUpgradeModal] = useState(false);
  const [selectedGateway, setSelectedGateway] = useState<Gateway | null>(null);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [syncingGateways, setSyncingGateways] = useState(false);
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const moreMenuRef = useRef<HTMLDivElement>(null);
  const [visibleFields, setVisibleFields] = useState<Record<string, boolean>>({
    macAddress: true,
    status: true,
    lastSeen: true,
    model: true,
    wifiFirmwareVersion: true,
    btFirmwareVersion: true,
    ipAddress: true,
  });
  const [showFieldManager, setShowFieldManager] = useState(false);
  const fieldManagerRef = useRef<HTMLDivElement>(null);
  const fieldManagerButtonRef = useRef<HTMLButtonElement>(null);

  // 獲取網關列表
  const fetchGateways = async () => {
    try {
      setLoading(true);
      setError(null);

      // 使用門店ID獲取網關列表
      const gatewayList = await getAllGateways(store.id);

      // 將日期字符串轉換為 Date 對象
      const formattedGateways = gatewayList.map(gateway => ({
        ...gateway,
        lastSeen: gateway.lastSeen ? new Date(gateway.lastSeen) : null,
        createdAt: gateway.createdAt ? new Date(gateway.createdAt) : null,
        updatedAt: gateway.updatedAt ? new Date(gateway.updatedAt) : null,
      }));

      setGateways(formattedGateways);
      applyFilters(formattedGateways, searchTerm, statusFilter, modelFilter);
    } catch (err: any) {
      console.error('獲取網關列表失敗:', err);
      setError(err.message || '獲取網關列表失敗');
    } finally {
      setLoading(false);
    }
  };

  // 初始加載和門店變化時重新獲取網關列表
  useEffect(() => {
    if (store?.id) {
      console.log(`門店變更為 ${store.id}，重新獲取網關列表`);
      fetchGateways();
    }
  }, [store?.id]);

  // 應用篩選器
  const applyFilters = (
    gatewayList: Gateway[],
    search: string,
    status: GatewayStatus | '',
    model: string
  ) => {
    let filtered = [...gatewayList];

    // 搜索過濾
    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(
        gateway =>
          gateway.name.toLowerCase().includes(searchLower) ||
          gateway.macAddress.toLowerCase().includes(searchLower) ||
          gateway.ipAddress.toLowerCase().includes(searchLower)
      );
    }

    // 狀態過濾
    if (status) {
      filtered = filtered.filter(gateway => gateway.status === status);
    }

    // 型號過濾
    if (model) {
      filtered = filtered.filter(gateway => gateway.model === model);
    }

    setFilteredGateways(filtered);
    // 重置到第一頁
    setCurrentPage(1);
  };

  // 處理搜索
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    applyFilters(gateways, value, statusFilter, modelFilter);
  };

  // 處理狀態過濾
  const handleStatusFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value as GatewayStatus | '';
    setStatusFilter(value);
    applyFilters(gateways, searchTerm, value, modelFilter);
  };

  // 處理型號過濾
  const handleModelFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setModelFilter(value);
    applyFilters(gateways, searchTerm, statusFilter, value);
  };

  // 處理編輯
  const handleEdit = (gateway: Gateway) => {
    setSelectedGateway(gateway);
    setShowEditModal(true);
  };

  // 處理刪除
  const handleDelete = async (id: string) => {
    if (!window.confirm(t('gateways.confirmDelete'))) {
      return;
    }

    try {
      // 傳遞門店ID
      await deleteGateway(id, store.id);
      fetchGateways();
      showNotification(t('gateways.deleteSuccess'), 'success');
    } catch (err: any) {
      console.error('刪除網關失敗:', err);
      showNotification(err.message || t('gateways.deleteFailed'), 'error');
    }
  };

  // 處理批量刪除
  const handleBatchDelete = async () => {
    if (selectedItems.length === 0) {
      showNotification(t('gateways.noItemSelected'), 'error');
      return;
    }

    if (!window.confirm(t('gateways.confirmBatchDelete', { count: selectedItems.length }))) {
      return;
    }

    try {
      // 依序刪除所選網關，傳遞門店ID
      for (const id of selectedItems) {
        await deleteGateway(id, store.id);
      }
      fetchGateways();
      setSelectedItems([]);
      setSelectAll(false);
      showNotification(t('gateways.batchDeleteSuccess'), 'success');
    } catch (err: any) {
      console.error('批量刪除網關失敗:', err);
      showNotification(err.message || t('gateways.batchDeleteFailed'), 'error');
    }
  };
  // 處理同步 - 獲取最新網關狀態
  const handleSync = async () => {
    try {
      setSyncingGateways(true);
      // 傳遞門店ID
      const result = await syncGateways(store.id);

      if (result.success && result.gateways) {
        // 將日期字符串轉換為 Date 對象
        const formattedGateways = result.gateways.map(gateway => ({
          ...gateway,
          lastSeen: gateway.lastSeen ? new Date(gateway.lastSeen) : null,
          createdAt: gateway.createdAt ? new Date(gateway.createdAt) : null,
          updatedAt: gateway.updatedAt ? new Date(gateway.updatedAt) : null,
        }));

        setGateways(formattedGateways);
        applyFilters(formattedGateways, searchTerm, statusFilter, modelFilter);
      } else {
        // 如果沒有獲取到網關數據，則重新獲取
        fetchGateways();
      }

      showNotification(t('gateways.syncSuccess'), 'success');
    } catch (err: any) {
      console.error('同步網關狀態失敗:', err);
      showNotification(err.message || t('gateways.syncFailed'), 'error');
    } finally {
      setSyncingGateways(false);
    }
  };

  // 處理重啟網關
  const handleRestart = async (id: string) => {
    if (!window.confirm(t('gateways.confirmRestart'))) {
      return;
    }

    try {
      // 傳遞門店ID
      await restartGateway(id, store.id);
      showNotification(t('gateways.restartSuccess'), 'success');
    } catch (err: any) {
      console.error('重啟網關失敗:', err);
      showNotification(err.message || t('gateways.restartFailed'), 'error');
    }
  };

  // 複製WebSocket登入資訊
  const handleCopyWsInfo = async (gateway: Gateway) => {
    try {
      // 獲取單個網關詳情，確保有最新的WebSocket資訊
      const gatewayDetails = await getGateway(gateway._id || '', store.id);

      if (!gatewayDetails.websocket) {
        showNotification(t('gateways.noWsInfo'), 'error');
        return;
      }

      // 直接使用後端提供的WebSocket資訊
      const wsInfo = {
        url: gatewayDetails.websocket.url,
        path: gatewayDetails.websocket.path,
        token: gatewayDetails.websocket.token,
        protocol: gatewayDetails.websocket.protocol
      };

      // 複製到剪貼板 (不使用格式化，避免分行)
      await navigator.clipboard.writeText(JSON.stringify(wsInfo));
      showNotification(t('gateways.wsInfoCopied'), 'success');
    } catch (err: any) {
      console.error('複製WebSocket登入資訊失敗:', err);
      showNotification(err.message || t('gateways.copyWsInfoFailed'), 'error');
    }
  };

  // 顯示通知
  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 3000);
  };

  // 獲取當前頁的項目
  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredGateways.slice(startIndex, endIndex);
  };

  // 處理頁面變更
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 計算總頁數
  const totalPages = Math.ceil(filteredGateways.length / itemsPerPage);

  // 獲取唯一的型號列表
  const uniqueModels = Array.from(new Set(gateways.map(gateway => gateway.model))).filter(Boolean);

  // 點擊外部關閉更多選單
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (moreMenuRef.current && !moreMenuRef.current.contains(event.target as Node)) {
        setShowMoreMenu(false);
      }
      if (
        fieldManagerRef.current &&
        !fieldManagerRef.current.contains(event.target as Node) &&
        fieldManagerButtonRef.current &&
        !fieldManagerButtonRef.current.contains(event.target as Node)
      ) {
        setShowFieldManager(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="p-8 lg:px-4 py-2">
      <div className="max-w-7xl mx-auto">
        {/* 錯誤提示 */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700 flex items-center">
            <AlertCircle className="mr-2" size={20} />
            <span>{error}</span>
          </div>
        )}

        {/* 通知提示 */}
        {notification && (
          <div
            className={`mb-4 p-3 ${notification.type === 'success' ? 'bg-green-100 border-green-500 text-green-700' : 'bg-red-100 border-red-500 text-red-700'
              } border-l-4 flex items-center`}
          >
            {notification.type === 'success' ? (
              <div className="mr-2">✓</div>
            ) : (
              <AlertCircle className="mr-2" size={20} />
            )}
            <span>{notification.message}</span>
          </div>
        )}

        {/* 工具列 */}
        <div className="mb-6 flex flex-wrap items-center gap-4">
          {/* 顯示當前門店名稱 */}
          <div className="px-4 py-2 bg-blue-100 text-blue-800 rounded-md border border-blue-300">
            <span className="font-medium">{store?.name || '未選擇門店'}</span>
            <span className="ml-2 text-blue-600">({store?.id || ''})</span>
          </div>

          <div className="relative flex-1">
            <input
              type="text"
              placeholder={t('gateways.searchPlaceholder')}
              value={searchTerm}
              onChange={handleSearch}
              className="w-full pl-4 pr-10 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Search className="absolute right-3 top-2.5 text-gray-400 w-5 h-5" />
          </div>

          {/* 狀態篩選 */}
          <select
            value={statusFilter}
            onChange={handleStatusFilter}
            className="px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">{t('gateways.allStatus')}</option>
            <option value="online">{t('gateways.online')}</option>
            <option value="offline">{t('gateways.offline')}</option>
          </select>

          {/* 型號篩選 */}
          <select
            value={modelFilter}
            onChange={handleModelFilter}
            className="px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">{t('gateways.allModels')}</option>
            {uniqueModels.map(model => (
              <option key={model} value={model}>
                {model}
              </option>
            ))}
          </select>

          {/* 同步按鈕 */}
          <button
            onClick={handleSync}
            disabled={syncingGateways}
            className={`flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md ${syncingGateways ? 'opacity-75 cursor-not-allowed' : 'hover:bg-blue-600'
              }`}
          >
            <RefreshCw className={`w-5 h-5 ${syncingGateways ? 'animate-spin' : ''}`} />
            {syncingGateways ? t('gateways.syncing') : t('gateways.syncGateways')}
          </button>

          {/* 新增網關按鈕 */}
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-violet-500 text-white rounded-md hover:bg-violet-600"
          >
            <Plus className="w-5 h-5" />
            {t('gateways.addGateway')}
          </button>

          {/* 批量刪除按鈕 */}
          <button
            onClick={handleBatchDelete}
            className="flex items-center gap-2 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
          >
            <Trash2 className="w-5 h-5" />
            {t('common.delete')}
          </button>

          {/* 更多操作按鈕 */}
          <div className="relative" ref={moreMenuRef}>
            <button
              onClick={() => setShowMoreMenu(!showMoreMenu)}
              className="flex items-center gap-2 px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
            >
              <MoreHorizontal className="w-5 h-5" />
              {t('common.more')}
            </button>
            {showMoreMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                <div className="py-1">
                  <button
                    onClick={() => {
                      setShowWifiUpgradeModal(true);
                      setShowMoreMenu(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Wifi className="w-4 h-4 mr-2" />
                    {t('gateways.upgradeWifiFirmware')}
                  </button>
                  <button
                    onClick={() => {
                      setShowBtUpgradeModal(true);
                      setShowMoreMenu(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Bluetooth className="w-4 h-4 mr-2" />
                    {t('gateways.upgradeBtFirmware')}
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* 欄位管理器 */}
          <div className="relative" ref={fieldManagerRef}>
            <button
              className="flex items-center gap-2 px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600"
              onClick={() => setShowFieldManager(!showFieldManager)}
              ref={fieldManagerButtonRef}
            >
              <Grid className="w-5 h-5" />
              {t('gateways.fieldManagement')}
            </button>
            {showFieldManager && (
              <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg z-10">
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-2">{t('gateways.manageFields')}</h3>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.macAddress}
                        onChange={() => setVisibleFields({ ...visibleFields, macAddress: !visibleFields.macAddress })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.macAddress')}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.status}
                        onChange={() => setVisibleFields({ ...visibleFields, status: !visibleFields.status })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.status')}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.lastSeen}
                        onChange={() => setVisibleFields({ ...visibleFields, lastSeen: !visibleFields.lastSeen })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.lastSeen')}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.model}
                        onChange={() => setVisibleFields({ ...visibleFields, model: !visibleFields.model })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.model')}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.wifiFirmwareVersion}
                        onChange={() => setVisibleFields({ ...visibleFields, wifiFirmwareVersion: !visibleFields.wifiFirmwareVersion })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.wifiFirmwareVersion')}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.btFirmwareVersion}
                        onChange={() => setVisibleFields({ ...visibleFields, btFirmwareVersion: !visibleFields.btFirmwareVersion })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.btFirmwareVersion')}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.ipAddress}
                        onChange={() => setVisibleFields({ ...visibleFields, ipAddress: !visibleFields.ipAddress })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.ipAddress')}
                    </label>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 網關列表 */}
        <div className="bg-white rounded-lg shadow relative">
          {/* 使用三個固定位置的表格實現左右固定，中間滾動的效果 */}
          <div className="relative overflow-hidden" style={{ maxWidth: "100%" }}>
            <div className="flex relative">
              {/* 左側固定欄位（勾選框和序號） */}
              <div className="sticky left-0 z-20 bg-white shadow-sm" style={{ width: "100px" }}>
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 w-12 border-b border-gray-200 text-white">
                        <input
                          type="checkbox"
                          className="rounded"
                          checked={selectAll}
                          onChange={(e) => {
                            setSelectAll(e.target.checked);
                            if (e.target.checked) {
                              setSelectedItems(getCurrentPageItems().map(gateway => gateway._id || ''));
                            } else {
                              setSelectedItems([]);
                            }
                          }}
                        />
                      </th>
                      <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 text-white">
                        {t('database.serialNumber')}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={2} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8">
                            <RefreshCw className="w-5 h-5 animate-spin mx-auto mb-2" />
                          </div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td colSpan={2} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8">{t('database.noData')}</div>
                        </td>
                      </tr>
                    ) : (
                      getCurrentPageItems().map((gateway, index) => (
                        <tr key={`left-${gateway._id}`} className={`h-[53px] ${index % 2 === 0 ? 'bg-amber-50' : 'bg-blue-50'}`}>
                          <td className={`px-4 py-3 border-b border-gray-200 align-middle`}>
                            <input
                              type="checkbox"
                              className="rounded"
                              checked={selectedItems.includes(gateway._id || '')}
                              onChange={(e) => {
                                if (e.target.checked && gateway._id) {
                                  setSelectedItems([...selectedItems, gateway._id]);
                                } else {
                                  setSelectedItems(selectedItems.filter(id => id !== gateway._id));
                                  setSelectAll(false);
                                }
                              }}
                            />
                          </td>
                          <td className={`px-4 py-3 border-b border-gray-200 whitespace-nowrap`}>
                            {index + 1 + (currentPage - 1) * itemsPerPage}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* 中間可滾動區域（動態欄位） */}
              <div className="flex-1 overflow-x-auto">
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                        {t('gateways.name')}
                      </th>
                      {visibleFields.macAddress && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('gateways.macAddress')}
                        </th>
                      )}
                      {visibleFields.status && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[100px] text-white">
                          {t('gateways.status')}
                        </th>
                      )}
                      {visibleFields.lastSeen && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[180px] text-white">
                          {t('gateways.lastSeen')}
                        </th>
                      )}
                      {visibleFields.model && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[120px] text-white">
                          {t('gateways.model')}
                        </th>
                      )}
                      {visibleFields.wifiFirmwareVersion && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('gateways.wifiFirmwareVersion')}
                        </th>
                      )}
                      {visibleFields.btFirmwareVersion && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('gateways.btFirmwareVersion')}
                        </th>
                      )}
                      {visibleFields.ipAddress && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('gateways.ipAddress')}
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={Object.values(visibleFields).filter(Boolean).length + 1} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td colSpan={Object.values(visibleFields).filter(Boolean).length + 1} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : (
                      getCurrentPageItems().map((gateway, index) => (
                        <tr key={`mid-${gateway._id}`} className={`h-[53px] ${index % 2 === 0 ? 'bg-amber-50' : 'bg-blue-50'}`}>
                          <td className="px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                            {gateway.name}
                          </td>
                          {visibleFields.macAddress && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {gateway.macAddress}
                            </td>
                          )}
                          {visibleFields.status && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[100px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              <GatewayStatusBadge status={gateway.status} />
                            </td>
                          )}
                          {visibleFields.lastSeen && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[180px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {gateway.lastSeen
                                ? new Date(gateway.lastSeen).toLocaleString()
                                : t('common.never')}
                            </td>
                          )}
                          {visibleFields.model && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[120px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {gateway.model}
                            </td>
                          )}
                          {visibleFields.wifiFirmwareVersion && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {gateway.wifiFirmwareVersion}
                            </td>
                          )}
                          {visibleFields.btFirmwareVersion && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {gateway.btFirmwareVersion}
                            </td>
                          )}
                          {visibleFields.ipAddress && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {gateway.ipAddress}
                            </td>
                          )}
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* 右側固定區域（操作欄） */}
              <div className="sticky right-0 z-20 bg-white shadow-sm" style={{ width: "150px" }}>
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap text-white">
                        {t('gateways.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : (
                      getCurrentPageItems().map((gateway, index) => (
                        <tr key={`right-${gateway._id}`} className={`h-[53px] align-middle ${index % 2 === 0 ? 'bg-amber-50' : 'bg-blue-50'}`}>
                          <td className="px-4 py-3 border-b border-gray-200 whitespace-nowrap overflow-hidden text-ellipsis">
                            <div className="flex items-center gap-2">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (gateway._id) handleRestart(gateway._id);
                                }}
                                className="text-gray-500 hover:text-blue-600"
                                title={t('gateways.restart')}
                              >
                                <Power className="w-5 h-5" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEdit(gateway);
                                }}
                                className="text-gray-500 hover:text-blue-600"
                                title={t('common.edit')}
                              >
                                <Edit className="w-5 h-5" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (gateway._id) handleDelete(gateway._id);
                                }}
                                className="text-gray-500 hover:text-red-600"
                                title={t('common.delete')}
                              >
                                <Trash className="w-5 h-5" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleCopyWsInfo(gateway);
                                }}
                                className="text-gray-500 hover:text-green-600"
                                title={t('gateways.copyWsInfo')}
                              >
                                <Key className="w-5 h-5" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* 分頁 */}
          {!loading && filteredGateways.length > 0 && (
            <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200">
              <div>
                <p className="text-sm text-gray-700">
                  {t('common.showing')}
                  <span className="font-medium mx-1">
                    {filteredGateways.length === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1}
                  </span>
                  {t('common.to')}
                  <span className="font-medium mx-1">
                    {Math.min(currentPage * itemsPerPage, filteredGateways.length)}
                  </span>
                  {t('common.of')}
                  <span className="font-medium mx-1">{filteredGateways.length}</span>
                  {t('common.entries')}
                </p>
              </div>

              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  {/* 上一頁按鈕 */}
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">{t('common.previousPage')}</span>
                    &laquo;
                  </button>

                  {/* 頁碼按鈕 */}
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(pageNum => (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium
                        ${currentPage === pageNum
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                    >
                      {pageNum}
                    </button>
                  ))}

                  {/* 下一頁按鈕 */}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">{t('common.nextPage')}</span>
                    &raquo;
                  </button>
                </nav>
              </div>
            </div>
          )}
        </div>

      </div>


      {/* 新增網關模態窗口 */}
      <AddGatewayModal
        isOpen={showAddModal}
        storeId={store.id} // 傳遞門店ID
        onClose={() => setShowAddModal(false)}
        onSuccess={() => {
          fetchGateways();
          showNotification('網關新增成功', 'success');
        }}
      />

      {/* 編輯網關模態窗口 */}
      <EditGatewayModal
        isOpen={showEditModal}
        gateway={selectedGateway}
        storeId={store.id} // 傳遞門店ID
        onClose={() => {
          setShowEditModal(false);
          setSelectedGateway(null);
        }}
        onSuccess={() => {
          fetchGateways();
          showNotification('網關更新成功', 'success');
        }}
      />

      {/* WiFi 固件升級模態窗口 */}
      <UpgradeWifiFirmwareModal
        isOpen={showWifiUpgradeModal}
        selectedGateways={selectedItems.length > 0 ? selectedItems : []}
        storeId={store.id} // 傳遞門店ID
        onClose={() => setShowWifiUpgradeModal(false)}
        onSuccess={() => {
          fetchGateways();
          showNotification('WiFi 固件升級成功', 'success');
        }}
      />

      {/* 藍芽固件升級模態窗口 */}
      <UpgradeBtFirmwareModal
        isOpen={showBtUpgradeModal}
        selectedGateways={selectedItems.length > 0 ? selectedItems : []}
        storeId={store.id} // 傳遞門店ID
        onClose={() => setShowBtUpgradeModal(false)}
        onSuccess={() => {
          fetchGateways();
          showNotification('藍芽固件升級成功', 'success');
        }}
      />
    </div>
  );
}
