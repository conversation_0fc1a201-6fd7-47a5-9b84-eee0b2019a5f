// EPD Manager App - 設備管理頁面

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDevices } from '../stores/deviceStore';
import { COLORS, SIZES } from '../utils/constants';

interface AddDeviceModalProps {
  visible: boolean;
  onClose: () => void;
  onAdd: (deviceConfig: any) => void;
}

const AddDeviceModal: React.FC<AddDeviceModalProps> = ({ visible, onClose, onAdd }) => {
  const [macAddress, setMacAddress] = useState('');
  const [deviceSize, setDeviceSize] = useState('2.9"');
  const [colorType, setColorType] = useState('BW');

  const generateRandomMac = () => {
    const mac = Array.from({ length: 6 }, () => 
      Math.floor(Math.random() * 256).toString(16).padStart(2, '0').toUpperCase()
    ).join(':');
    setMacAddress(mac);
  };

  const handleAdd = () => {
    if (!macAddress) {
      Alert.alert('錯誤', '請輸入 MAC 地址');
      return;
    }

    onAdd({
      macAddress,
      size: deviceSize,
      colorType,
      status: 'online'
    });

    // 重置表單
    setMacAddress('');
    setDeviceSize('2.9"');
    setColorType('BW');
    onClose();
  };

  useEffect(() => {
    if (visible && !macAddress) {
      generateRandomMac();
    }
  }, [visible]);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>添加模擬設備</Text>
            <TouchableOpacity onPress={onClose}>
              <Text style={styles.closeButton}>✕</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>MAC 地址</Text>
            <View style={styles.macInputContainer}>
              <TextInput
                style={styles.textInput}
                value={macAddress}
                onChangeText={setMacAddress}
                placeholder="XX:XX:XX:XX:XX:XX"
                autoCapitalize="characters"
              />
              <TouchableOpacity
                style={styles.generateButton}
                onPress={generateRandomMac}
              >
                <Text style={styles.generateButtonText}>生成</Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>螢幕尺寸</Text>
            <View style={styles.optionContainer}>
              {['2.9"', '4.2"', '7.5"', '13.3"'].map((size) => (
                <TouchableOpacity
                  key={size}
                  style={[
                    styles.optionButton,
                    deviceSize === size && styles.optionButtonSelected
                  ]}
                  onPress={() => setDeviceSize(size)}
                >
                  <Text style={[
                    styles.optionText,
                    deviceSize === size && styles.optionTextSelected
                  ]}>
                    {size}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>顏色類型</Text>
            <View style={styles.optionContainer}>
              {['BW', 'BWR', 'BWY'].map((color) => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.optionButton,
                    colorType === color && styles.optionButtonSelected
                  ]}
                  onPress={() => setColorType(color)}
                >
                  <Text style={[
                    styles.optionText,
                    colorType === color && styles.optionTextSelected
                  ]}>
                    {color}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.modalActions}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>取消</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={handleAdd}
            >
              <Text style={styles.confirmButtonText}>添加設備</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SIZES.SPACING_MD,
    backgroundColor: COLORS.SURFACE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.TEXT_DISABLED,
  },
  title: {
    fontSize: SIZES.FONT_SIZE_XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  addButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_MD,
  },
  addButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '600',
  },
  errorContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.ERROR,
    padding: SIZES.SPACING_SM,
    margin: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  errorText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    flex: 1,
  },
  errorDismiss: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: 'bold',
    padding: SIZES.SPACING_XS,
  },
  scrollView: {
    flex: 1,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: SIZES.SPACING_MD,
    backgroundColor: COLORS.SURFACE,
    margin: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_MD,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: SIZES.FONT_SIZE_XL,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
  },
  statLabel: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SIZES.SPACING_XS,
  },
  sectionTitle: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginHorizontal: SIZES.SPACING_MD,
    marginBottom: SIZES.SPACING_SM,
  },
  deviceCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SIZES.SPACING_MD,
    marginTop: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    position: 'relative',
  },
  deviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SIZES.SPACING_SM,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceMac: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    fontFamily: 'monospace',
    marginBottom: SIZES.SPACING_XS,
  },
  deviceDetails: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
  },
  statusBadge: {
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  statusOnline: {
    backgroundColor: COLORS.SUCCESS,
  },
  statusOffline: {
    backgroundColor: COLORS.TEXT_DISABLED,
  },
  statusText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  deviceActions: {
    flexDirection: 'row',
    gap: SIZES.SPACING_SM,
  },
  actionButton: {
    backgroundColor: COLORS.INFO,
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  actionButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '600',
  },
  deleteButton: {
    backgroundColor: COLORS.ERROR,
  },
  deleteButtonText: {
    color: COLORS.SURFACE,
  },
  customBadge: {
    position: 'absolute',
    top: SIZES.SPACING_SM,
    right: SIZES.SPACING_SM,
    backgroundColor: COLORS.WARNING,
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  customBadgeText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: SIZES.SPACING_XL,
  },
  emptyText: {
    fontSize: SIZES.FONT_SIZE_LG,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SIZES.SPACING_SM,
  },
  emptySubtext: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_DISABLED,
  },
  // Modal 樣式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_LG,
    padding: SIZES.SPACING_LG,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_LG,
  },
  modalTitle: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  closeButton: {
    fontSize: SIZES.FONT_SIZE_LG,
    color: COLORS.TEXT_SECONDARY,
    padding: SIZES.SPACING_SM,
  },
  formGroup: {
    marginBottom: SIZES.SPACING_LG,
  },
  label: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_SM,
  },
  macInputContainer: {
    flexDirection: 'row',
    gap: SIZES.SPACING_SM,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    padding: SIZES.SPACING_MD,
    fontSize: SIZES.FONT_SIZE_MD,
    fontFamily: 'monospace',
  },
  generateButton: {
    backgroundColor: COLORS.INFO,
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    justifyContent: 'center',
  },
  generateButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '600',
  },
  optionContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SIZES.SPACING_SM,
  },
  optionButton: {
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
  },
  optionButtonSelected: {
    borderColor: COLORS.PRIMARY,
    backgroundColor: COLORS.PRIMARY,
  },
  optionText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_PRIMARY,
  },
  optionTextSelected: {
    color: COLORS.SURFACE,
    fontWeight: '600',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: SIZES.SPACING_MD,
    marginTop: SIZES.SPACING_LG,
  },
  cancelButton: {
    flex: 1,
    padding: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: COLORS.TEXT_SECONDARY,
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
  },
  confirmButton: {
    flex: 1,
    padding: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    backgroundColor: COLORS.PRIMARY,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: 'bold',
  },
});

export const DeviceManagementScreen: React.FC = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const {
    devices,
    customDevices,
    loading,
    error,
    addCustomDevice,
    removeCustomDevice,
    requestDeviceImage,
    refreshDeviceList,
    clearError
  } = useDevices();

  useEffect(() => {
    refreshDeviceList();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    refreshDeviceList();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const handleAddDevice = async (deviceConfig: any) => {
    const success = await addCustomDevice(deviceConfig);
    if (success) {
      Alert.alert('成功', '設備添加成功');
    } else {
      Alert.alert('錯誤', error || '添加設備失敗');
    }
  };

  const handleRemoveDevice = (index: number, device: any) => {
    Alert.alert(
      '確認刪除',
      `確定要刪除設備 ${device.macAddress} 嗎？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '刪除',
          style: 'destructive',
          onPress: () => {
            const success = removeCustomDevice(index);
            if (success) {
              Alert.alert('成功', '設備已刪除');
            }
          }
        }
      ]
    );
  };

  const handleRequestImage = (macAddress: string) => {
    requestDeviceImage(macAddress);
    Alert.alert('提示', `已請求設備 ${macAddress} 的預覽圖像`);
  };

  const allDevices = [...devices, ...customDevices];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>設備管理</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddModal(true)}
        >
          <Text style={styles.addButtonText}>+ 添加設備</Text>
        </TouchableOpacity>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={clearError}>
            <Text style={styles.errorDismiss}>✕</Text>
          </TouchableOpacity>
        </View>
      )}

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.PRIMARY]}
          />
        }
      >
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{devices.length}</Text>
            <Text style={styles.statLabel}>預設設備</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{customDevices.length}</Text>
            <Text style={styles.statLabel}>自定義設備</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{allDevices.length}</Text>
            <Text style={styles.statLabel}>總設備數</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>設備列表</Text>

        {allDevices.map((device, index) => {
          const isCustomDevice = index >= devices.length;
          const customIndex = isCustomDevice ? index - devices.length : -1;

          return (
            <View key={device.macAddress} style={styles.deviceCard}>
              <View style={styles.deviceHeader}>
                <View style={styles.deviceInfo}>
                  <Text style={styles.deviceMac}>{device.macAddress}</Text>
                  <Text style={styles.deviceDetails}>
                    {device.data.size} | {device.data.colorType || 'BW'} | 
                    電量: {device.data.battery}% | 
                    信號: {device.data.rssi}dBm
                  </Text>
                </View>
                <View style={[
                  styles.statusBadge,
                  device.status === 'online' ? styles.statusOnline : styles.statusOffline
                ]}>
                  <Text style={styles.statusText}>
                    {device.status === 'online' ? '在線' : '離線'}
                  </Text>
                </View>
              </View>

              <View style={styles.deviceActions}>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handleRequestImage(device.macAddress)}
                >
                  <Text style={styles.actionButtonText}>請求圖像</Text>
                </TouchableOpacity>

                {isCustomDevice && (
                  <TouchableOpacity
                    style={[styles.actionButton, styles.deleteButton]}
                    onPress={() => handleRemoveDevice(customIndex, device)}
                  >
                    <Text style={[styles.actionButtonText, styles.deleteButtonText]}>
                      刪除
                    </Text>
                  </TouchableOpacity>
                )}
              </View>

              {isCustomDevice && (
                <View style={styles.customBadge}>
                  <Text style={styles.customBadgeText}>自定義</Text>
                </View>
              )}
            </View>
          );
        })}

        {allDevices.length === 0 && (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>暫無設備</Text>
            <Text style={styles.emptySubtext}>點擊右上角按鈕添加設備</Text>
          </View>
        )}
      </ScrollView>

      <AddDeviceModal
        visible={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAdd={handleAddDevice}
      />
    </SafeAreaView>
  );
};
