// EPD Manager App - 一鍵配對按鈕組件

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Modal,
  FlatList,
  Pressable,
} from 'react-native';
import { useGateways } from '../stores/gatewayStore';
import { useStores } from '../stores/storeStore';
import { BluetoothDevice } from '../types';
import { COLORS, SIZES, SUCCESS_MESSAGES, ERROR_MESSAGES } from '../utils/constants';

interface AutoPairingButtonProps {
  onSuccess?: (gateway: any) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  style?: any;
}

export const AutoPairingButton: React.FC<AutoPairingButtonProps> = ({
  onSuccess,
  onError,
  disabled = false,
  style
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [showBluetoothModal, setShowBluetoothModal] = useState(false);
  const [bluetoothDevices, setBluetoothDevices] = useState<BluetoothDevice[]>([]);
  const { selectedStore } = useStores();
  const { autoPairGateway, loading, error } = useGateways();

  // 生成假的藍芽裝置列表
  const generateFakeBluetoothDevices = (): BluetoothDevice[] => {
    const devices: BluetoothDevice[] = [];
    for (let i = 1; i <= 5; i++) {
      const randomMac = Array.from({ length: 6 }, () =>
        Math.floor(Math.random() * 256).toString(16).padStart(2, '0').toUpperCase()
      ).join(':');

      devices.push({
        id: `device_${i}`,
        name: `EPD-GATEWAY-${String(i).padStart(3, '0')}`,
        macAddress: randomMac,
        selected: false,
      });
    }
    return devices;
  };

  const handleAutoPairing = async () => {
    // 檢查是否選擇了門店
    if (!selectedStore) {
      Alert.alert('錯誤', '請先選擇門店');
      return;
    }

    if (isProcessing) {
      return;
    }

    // 生成假的藍芽裝置並顯示選單
    const devices = generateFakeBluetoothDevices();
    setBluetoothDevices(devices);
    setShowBluetoothModal(true);
  };

  const handleDeviceSelect = (deviceId: string) => {
    setBluetoothDevices(prev =>
      prev.map(device => ({
        ...device,
        selected: device.id === deviceId ? !device.selected : device.selected
      }))
    );
  };

  const handleConfirmPairing = async () => {
    const selectedDevices = bluetoothDevices.filter(device => device.selected);

    if (selectedDevices.length === 0) {
      Alert.alert('提示', '請至少選擇一個網關設備');
      return;
    }

    setShowBluetoothModal(false);
    setIsProcessing(true);

    try {
      console.log('開始配對選中的網關設備...');

      const storeId = selectedStore._id || selectedStore.id;

      // 為每個選中的設備執行配對流程
      for (const device of selectedDevices) {
        console.log(`配對設備: ${device.name} (${device.macAddress})`);

        // 使用現有的註冊方式
        const success = await autoPairGateway(storeId, device.name, device.macAddress);

        if (success) {
          console.log(`設備 ${device.name} 配對成功`);

          // TODO: 這裡預留藍芽發送 WebSocket 資訊的步驟
          console.log(`預留: 透過藍芽發送 WebSocket 資訊給 ${device.name}`);

        } else {
          throw new Error(`設備 ${device.name} 配對失敗`);
        }
      }

      Alert.alert(
        '配對成功',
        `已成功配對 ${selectedDevices.length} 個網關設備`,
        [
          {
            text: '確定',
            onPress: () => {
              if (onSuccess) {
                onSuccess(selectedDevices);
              }
            },
          },
        ]
      );
    } catch (err: any) {
      const errorMessage = err.message || ERROR_MESSAGES.AUTO_PAIRING_FAILED;
      Alert.alert('配對失敗', errorMessage);

      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setIsProcessing(false);
    }
  };



  const isButtonDisabled = disabled || isProcessing || loading || !selectedStore;

  return (
    <>
      <View style={[styles.container, style]}>
        <TouchableOpacity
          style={[
            styles.button,
            isButtonDisabled && styles.buttonDisabled
          ]}
          onPress={handleAutoPairing}
          disabled={isButtonDisabled}
          activeOpacity={0.8}
        >
          {(isProcessing || loading) ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator
                size="small"
                color={COLORS.SURFACE}
                style={styles.loadingIndicator}
              />
              <Text style={styles.buttonTextLoading}>
                配對中...
              </Text>
            </View>
          ) : (
            <Text style={[
              styles.buttonText,
              isButtonDisabled && styles.buttonTextDisabled
            ]}>
              🚀 一鍵配對新網關
            </Text>
          )}
        </TouchableOpacity>

        {!selectedStore && (
          <Text style={styles.hintText}>
            請先選擇門店
          </Text>
        )}

        {selectedStore && (
          <Text style={styles.infoText}>
            將掃描藍芽裝置並配對到門店「{selectedStore.name}」
          </Text>
        )}
      </View>

      {/* 藍芽裝置選擇 Modal */}
      <Modal
        visible={showBluetoothModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowBluetoothModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>選擇藍芽網關設備</Text>
              <Pressable
                style={styles.closeButton}
                onPress={() => setShowBluetoothModal(false)}
              >
                <Text style={styles.closeButtonText}>✕</Text>
              </Pressable>
            </View>

            <Text style={styles.modalSubtitle}>
              發現 {bluetoothDevices.length} 個 EPD 網關設備
            </Text>

            <FlatList
              data={bluetoothDevices}
              keyExtractor={(item) => item.id}
              style={styles.deviceList}
              renderItem={({ item }) => (
                <Pressable
                  style={[
                    styles.deviceItem,
                    item.selected && styles.deviceItemSelected
                  ]}
                  onPress={() => handleDeviceSelect(item.id)}
                >
                  <View style={styles.deviceInfo}>
                    <Text style={styles.deviceName}>{item.name}</Text>
                    <Text style={styles.deviceMac}>MAC: {item.macAddress}</Text>
                  </View>
                  <View style={[
                    styles.checkbox,
                    item.selected && styles.checkboxSelected
                  ]}>
                    {item.selected && <Text style={styles.checkmark}>✓</Text>}
                  </View>
                </Pressable>
              )}
            />

            <View style={styles.modalActions}>
              <Pressable
                style={styles.cancelButton}
                onPress={() => setShowBluetoothModal(false)}
              >
                <Text style={styles.cancelButtonText}>取消</Text>
              </Pressable>
              <Pressable
                style={styles.confirmButton}
                onPress={handleConfirmPairing}
              >
                <Text style={styles.confirmButtonText}>
                  配對選中設備 ({bluetoothDevices.filter(d => d.selected).length})
                </Text>
              </Pressable>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginVertical: SIZES.SPACING_MD,
  },
  button: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SIZES.SPACING_XL,
    paddingVertical: SIZES.SPACING_LG,
    borderRadius: SIZES.BORDER_RADIUS_LG,
    minWidth: 200,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: COLORS.PRIMARY,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  buttonDisabled: {
    backgroundColor: COLORS.TEXT_DISABLED,
    elevation: 0,
    shadowOpacity: 0,
  },
  buttonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  buttonTextDisabled: {
    color: COLORS.TEXT_SECONDARY,
  },
  buttonTextLoading: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingIndicator: {
    marginRight: SIZES.SPACING_SM,
  },
  hintText: {
    color: COLORS.WARNING,
    fontSize: SIZES.FONT_SIZE_SM,
    marginTop: SIZES.SPACING_SM,
    textAlign: 'center',
  },
  infoText: {
    color: COLORS.TEXT_SECONDARY,
    fontSize: SIZES.FONT_SIZE_SM,
    marginTop: SIZES.SPACING_SM,
    textAlign: 'center',
    paddingHorizontal: SIZES.SPACING_MD,
  },
  // Modal 樣式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_LG,
    padding: SIZES.SPACING_LG,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_MD,
  },
  modalTitle: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  modalSubtitle: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SIZES.SPACING_MD,
  },
  closeButton: {
    padding: SIZES.SPACING_SM,
  },
  closeButtonText: {
    fontSize: SIZES.FONT_SIZE_LG,
    color: COLORS.TEXT_SECONDARY,
  },
  deviceList: {
    maxHeight: 300,
    marginBottom: SIZES.SPACING_LG,
  },
  deviceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
    marginBottom: SIZES.SPACING_SM,
  },
  deviceItemSelected: {
    borderColor: COLORS.PRIMARY,
    backgroundColor: `${COLORS.PRIMARY}10`,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceName: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 2,
  },
  deviceMac: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    fontFamily: 'monospace',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: COLORS.TEXT_DISABLED,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxSelected: {
    borderColor: COLORS.PRIMARY,
    backgroundColor: COLORS.PRIMARY,
  },
  checkmark: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: 'bold',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: SIZES.SPACING_MD,
  },
  cancelButton: {
    flex: 1,
    padding: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: COLORS.TEXT_SECONDARY,
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
  },
  confirmButton: {
    flex: 2,
    padding: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    backgroundColor: COLORS.PRIMARY,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: 'bold',
  },
});

export default AutoPairingButton;
