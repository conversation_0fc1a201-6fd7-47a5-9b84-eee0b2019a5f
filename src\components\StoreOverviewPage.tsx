import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import { Doughn<PERSON> } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Store } from '../types/store';
import { StoreData, DataField } from '../types';
import { Gateway } from '../types/gateway';
import { Device } from '../types/device';
import { getAllStoreData } from '../utils/api/storeDataApi';
import { getAllDataFields } from '../utils/api/dataFieldApi';
import { getAllGateways } from '../utils/api/gatewayApi';
import { getAllDevices } from '../utils/api/deviceApi';
import { getUser } from '../utils/api/userApi';  // 添加 getUser 函數引入
import { AddStoreDataModal } from './AddStoreDataModal';
import { Plus, AlertCircle, RefreshCw } from 'lucide-react';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface StoreOverviewPageProps {
  store?: Store;
}

export function StoreOverviewPage({ store }: StoreOverviewPageProps) {
  const [storeData, setStoreData] = useState<StoreData[]>([]);
  const [dataFields, setDataFields] = useState<DataField[]>([]);
  const [gateways, setGateways] = useState<Gateway[]>([]);
  const [devices, setDevices] = useState<Device[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingFields, setIsLoadingFields] = useState(false);
  const [isLoadingStats, setIsLoadingStats] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  // 添加管理員狀態
  const [manager, setManager] = useState<{ name: string; username: string } | null>(null);
  const [isLoadingManager, setIsLoadingManager] = useState(false);
  const [managerError, setManagerError] = useState<string | null>(null);

  // 獲取門店資料
  useEffect(() => {
    const fetchStoreData = async () => {
      if (!store) return;

      try {
        setIsLoading(true);
        setError(null);

        // 使用門店ID獲取對應的門店資料
        console.log(`正在獲取門店 ${store.id} 的資料...`);
        const data = await getAllStoreData(store.id);
        console.log(`獲取到門店 ${store.id} 的資料:`, data);

        if (data.length === 0) {
          console.log(`門店 ${store.id} 沒有對應的資料，可能需要創建`);
        } else {
          console.log(`門店 ${store.id} 的資料 storeId:`, data.map(item => item.storeId));
        }

        setStoreData(data);
      } catch (err) {
        console.error('獲取門店資料失敗:', err);
        setError('獲取門店資料失敗');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStoreData();
  }, [store]);

  // 獲取資料欄位定義
  useEffect(() => {
    const fetchDataFields = async () => {
      try {
        setIsLoadingFields(true);
        const fields = await getAllDataFields();
        setDataFields(fields);
      } catch (err) {
        console.error('獲取資料欄位失敗:', err);
      } finally {
        setIsLoadingFields(false);
      }
    };

    fetchDataFields();
  }, []);

  // 獲取管理員詳細信息
  useEffect(() => {
    const fetchManager = async () => {
      if (!store?.managerId) return;

      try {
        setIsLoadingManager(true);
        setManagerError(null);
        const userData = await getUser(store.managerId);
        setManager({
          name: userData.name || userData.username || '未知',
          username: userData.username || '未知'
        });
      } catch (err) {
        console.error('獲取管理員資訊失敗:', err);
        setManagerError('獲取管理員資訊失敗');
        setManager(null);
      } finally {
        setIsLoadingManager(false);
      }
    };

    fetchManager();
  }, [store?.managerId]);

  // 獲取Gateway和Device數據
  useEffect(() => {
    const fetchStatsData = async () => {
      if (!store?.id) return;

      try {
        setIsLoadingStats(true);

        // 並行獲取gateway和device數據
        const [gatewayData, deviceData] = await Promise.all([
          getAllGateways(store.id),
          getAllDevices(store.id)
        ]);

        console.log(`門店 ${store.id} - Gateway數量: ${gatewayData.length}, Device數量: ${deviceData.length}`);

        setGateways(gatewayData);
        setDevices(deviceData);
      } catch (err) {
        console.error('獲取統計數據失敗:', err);
      } finally {
        setIsLoadingStats(false);
      }
    };

    fetchStatsData();
  }, [store?.id]);

  // 計算統計數據
  const calculateStats = () => {
    // Gateway統計
    const gatewayOnline = gateways.filter(g => g.status === 'online').length;
    const gatewayOffline = gateways.filter(g => g.status === 'offline').length;

    // Device統計
    const deviceOnline = devices.filter(d => d.status === 'online').length;
    const deviceOffline = devices.filter(d => d.status === 'offline').length;

    // 刷新狀態統計（基於imageUpdateStatus）
    const refreshSucceeded = devices.filter(d => d.imageUpdateStatus === '已更新').length;
    const refreshFailed = devices.filter(d => d.imageUpdateStatus === '未更新').length;

    // 電池狀態統計（基於battery值，低於20%視為低電量）
    const batteryNormal = devices.filter(d => {
      const battery = d.data?.battery;
      return battery !== undefined && battery >= 20;
    }).length;
    const batteryLow = devices.filter(d => {
      const battery = d.data?.battery;
      return battery !== undefined && battery < 20;
    }).length;

    return {
      gateway: { online: gatewayOnline, offline: gatewayOffline },
      device: { online: deviceOnline, offline: deviceOffline },
      refresh: { succeeded: refreshSucceeded, failed: refreshFailed },
      battery: { normal: batteryNormal, low: batteryLow }
    };
  };

  const stats = calculateStats();

  const statusCards = [
    {
      title: 'Gateway',
      gradient: 'from-purple-400 to-blue-500',
      metrics: [
        { label: 'Online', value: stats.gateway.online },
        { label: 'Offline', value: stats.gateway.offline },
      ],
    },
    {
      title: 'ESL',
      gradient: 'from-blue-400 to-cyan-500',
      metrics: [
        { label: 'Online', value: stats.device.online },
        { label: 'Offline', value: stats.device.offline },
      ],
    },
    {
      title: 'Refresh',
      gradient: 'from-green-400 to-emerald-500',
      metrics: [
        { label: 'Succeeded', value: stats.refresh.succeeded },
        { label: 'Failed', value: stats.refresh.failed },
      ],
    },
    {
      title: 'Battery level',
      gradient: 'from-orange-400 to-amber-500',
      metrics: [
        { label: 'Normal', value: stats.battery.normal },
        { label: 'Low battery', value: stats.battery.low },
      ],
    },
  ];

  // 計算成功率
  const calculateSuccessRate = () => {
    const total = stats.refresh.succeeded + stats.refresh.failed;
    if (total === 0) return 0;
    return Math.round((stats.refresh.succeeded / total) * 100);
  };

  const successRate = calculateSuccessRate();

  const lineChartData = {
    labels: ['03-26', '03-27', '03-28', '03-29', '03-30', 'Yesterday', 'Today'],
    datasets: [
      {
        label: 'Succeeded/times',
        data: [0, 0, 0, 0, 0, 0, stats.refresh.succeeded],
        borderColor: 'rgb(99, 132, 255)',
        backgroundColor: 'rgba(99, 132, 255, 0.5)',
      },
      {
        label: 'Refresh/times',
        data: [0, 0, 0, 0, 0, 0, stats.refresh.succeeded + stats.refresh.failed],
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
      },
    ],
  };

  const doughnutChartData = {
    labels: ['Succeeded', 'Failed'],
    datasets: [
      {
        data: [stats.refresh.succeeded, stats.refresh.failed],
        backgroundColor: [
          'rgba(99, 132, 255, 0.8)',
          'rgba(255, 99, 132, 0.8)',
        ],
        borderColor: [
          'rgba(99, 132, 255, 1)',
          'rgba(255, 99, 132, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // 刷新統計數據
  const handleRefreshStats = async () => {
    if (!store?.id) return;

    try {
      setIsLoadingStats(true);

      const [gatewayData, deviceData] = await Promise.all([
        getAllGateways(store.id),
        getAllDevices(store.id)
      ]);

      setGateways(gatewayData);
      setDevices(deviceData);
    } catch (err) {
      console.error('刷新統計數據失敗:', err);
    } finally {
      setIsLoadingStats(false);
    }
  };

  return (
    <>
      <div className="p-8 lg:px-4 py-2">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* 門店信息 */}
          {store && (
            <div className="bg-white rounded-xl p-6 shadow-lg mb-6">
              <h2 className="text-xl font-bold text-gray-800 mb-2">{store.name || '未命名門店'}</h2>
              <div className="text-gray-600">
                <p><span className="font-medium">ID:</span> {store.id}</p>
                <p><span className="font-medium">地址:</span> {store.address || '無地址'}</p>
                {store.phone && <p><span className="font-medium">電話:</span> {store.phone}</p>}
                {isLoadingManager ? (
                  <p><span className="font-medium">管理員:</span> 加載中...</p>
                ) : manager ? (
                  <p><span className="font-medium">管理員:</span> {manager.name}</p>
                ) : store.managerId ? (
                  <p><span className="font-medium">管理員ID:</span> {store.managerId} {managerError && <span className="text-red-500 text-xs">({managerError})</span>}</p>
                ) : null}
              </div>
            </div>
          )}

          {/* 載入狀態 */}
          {isLoading && (
            <div className="text-center py-4">
              <p className="text-gray-600">載入門店資料中...</p>
            </div>
          )}

          {/* 錯誤信息 */}
          {error && (
            <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
              <p className="text-red-700">{error}</p>
            </div>
          )}

          {!isLoading && storeData.length === 0 && !error && (
            <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4 mb-6">
              <div className="flex justify-between items-center">
                <p className="text-yellow-700">此門店尚未設置資料，請先添加門店資料。</p>
                <button
                  onClick={() => setShowAddModal(true)}
                  className="flex items-center bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 text-sm"
                  disabled={isLoadingFields}
                >
                  <Plus size={16} className="mr-1" />
                  添加門店資料
                </button>
              </div>
            </div>
          )}

          {/* 統計數據標題和刷新按鈕 */}
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-gray-800">門店概況</h2>
            <button
              onClick={handleRefreshStats}
              disabled={isLoadingStats}
              className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <RefreshCw size={16} className={`mr-2 ${isLoadingStats ? 'animate-spin' : ''}`} />
              {isLoadingStats ? '刷新中...' : '刷新統計'}
            </button>
          </div>

          {/* Status Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {statusCards.map((card) => (
              <div
                key={card.title}
                className={`rounded-xl p-6 bg-gradient-to-r ${card.gradient} text-white shadow-lg`}
              >
                <h3 className="text-xl font-semibold mb-4">{card.title}</h3>
                <div className="flex justify-between">
                  {card.metrics.map((metric) => (
                    <div key={metric.label}>
                      <span className="block text-sm opacity-90">{metric.label}</span>
                      <span className="text-2xl font-bold">{metric.value}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Line Chart */}
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                ESL status in the last week
              </h3>
              <Line
                data={lineChartData}
                options={{
                  responsive: true,
                  scales: {
                    y: {
                      beginAtZero: true,
                    },
                  },
                }}
              />
            </div>

            {/* Doughnut Chart */}
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                Success rate
              </h3>
              <div className="relative" style={{ height: '250px' }}>
                <Doughnut
                  data={doughnutChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '60%',
                    plugins: {
                      legend: {
                        position: 'top' as const,
                        labels: {
                          padding: 15,
                          font: {
                            size: 12
                          }
                        }
                      },
                    },
                  }}
                />
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                  <div className="text-2xl font-bold text-gray-800">{successRate}%</div>
                  <div className="text-2xl mt-1">
                    {successRate >= 80 ? '😊' : successRate >= 50 ? '😐' : '😕'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 添加門店資料模態窗口 */}
      {store && (
        <AddStoreDataModal
          isOpen={showAddModal}
          dataFields={dataFields}
          storeId={store.id}
          onClose={() => setShowAddModal(false)}
          onSuccess={() => {
            // 重新獲取門店資料
            if (store) {
              getAllStoreData(store.id).then(data => {
                setStoreData(data);
                setShowAddModal(false);
              }).catch(err => {
                console.error('重新獲取門店資料失敗:', err);
              });
            }
          }}
        />
      )}
    </>
  );
}