const express = require('express');
const router = express.Router();
const { ObjectId } = require('mongodb');
const { authenticate, checkPermission } = require('../middleware/auth');

// MongoDB 連接信息
const collectionName = 'stores';

// 使用共享的 MongoDB 連接
let getDbConnection = null;

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

// 獲取數據集合
const getCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();

  // 檢查 stores 集合是否存在，若不存在則創建
  const collections = await db.listCollections({ name: collectionName }).toArray();
  if (collections.length === 0) {
    console.log(`創建 ${collectionName} 集合`);
    await db.createCollection(collectionName);
  }

  const collection = db.collection(collectionName);
  return { collection, client };
};

// 獲取所有門店
router.get('/stores', authenticate, checkPermission(['store:view', 'store-data:view', 'store-template:view', 'gateway:view', 'device:view', 'store-settings:view', 'analytics:view']), async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const { user, db } = req;

    const { collection } = await getCollection();

    // 獲取用戶的權限分配
    const Permission = require('../models/Permission');
    const userPermissions = await Permission.findByUserId(db, user._id.toString());

    // 獲取用戶的所有角色
    const roleIds = userPermissions.map(p => p.roleId);
    const Role = require('../models/Role');
    const roles = await Promise.all(roleIds.map(roleId => Role.findById(db, roleId.toString())));
    const validRoles = roles.filter(role => role !== null);

    // 檢查是否有系統管理員權限
    const hasSystemRole = validRoles.some(role =>
      role.type === 'system' &&
      (role.permissions.includes('all') || role.permissions.includes('store:view'))
    );

    // 構建查詢條件
    const query = {};
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { id: { $regex: search, $options: 'i' } },
        { address: { $regex: search, $options: 'i' } }
      ];
    }

    // 如果不是系統管理員，只能看到分配給他的門店
    if (!hasSystemRole) {
      // 獲取用戶有權限的門店ID列表
      const storeScopes = userPermissions
        .filter(p => p.scopeType === 'store')
        .map(p => p.scope);

      console.log('用戶有權限的門店ID:', storeScopes);

      if (storeScopes.length === 0) {
        // 如果用戶沒有任何門店權限，返回空列表
        return res.json({
          stores: [],
          pagination: {
            page: pageNum,
            limit: limitNum,
            total: 0
          }
        });
      }

      // 添加門店ID過濾條件
      if (query.$or) {
        // 如果已有搜索條件，使用 $and 組合
        query.$and = [
          { $or: query.$or },
          { id: { $in: storeScopes } }
        ];
        delete query.$or;
      } else {
        // 直接添加門店ID過濾
        query.id = { $in: storeScopes };
      }
    }

    console.log('門店查詢條件:', JSON.stringify(query));

    // 獲取總數
    const total = await collection.countDocuments(query);

    // 獲取分頁數據
    const stores = await collection.find(query)
      .sort({ createdAt: -1 })
      .skip((pageNum - 1) * limitNum)
      .limit(limitNum)
      .toArray();

    res.json({
      stores,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total
      }
    });
  } catch (error) {
    console.error('獲取門店失敗:', error);
    res.status(500).json({ error: '獲取門店失敗' });
  }
});

// 獲取單個門店
router.get('/stores/:id', authenticate, checkPermission(['store:view', 'store-data:view', 'store-template:view', 'gateway:view', 'device:view', 'store-settings:view', 'analytics:view']), async (req, res) => {
  try {
    const { id } = req.params;
    const { user, db } = req;
    const { collection } = await getCollection();

    // 嘗試通過 _id 查找
    let store = null;
    if (ObjectId.isValid(id)) {
      store = await collection.findOne({ _id: new ObjectId(id) });
    }

    // 如果沒找到，嘗試通過 id 字段查找
    if (!store) {
      store = await collection.findOne({ id });
    }

    if (!store) {
      return res.status(404).json({ error: '門店不存在' });
    }

    // 獲取用戶的權限分配
    const Permission = require('../models/Permission');
    const userPermissions = await Permission.findByUserId(db, user._id.toString());

    // 獲取用戶的所有角色
    const roleIds = userPermissions.map(p => p.roleId);
    const Role = require('../models/Role');
    const roles = await Promise.all(roleIds.map(roleId => Role.findById(db, roleId.toString())));
    const validRoles = roles.filter(role => role !== null);

    // 檢查是否有系統管理員權限
    const hasSystemRole = validRoles.some(role =>
      role.type === 'system' &&
      (role.permissions.includes('all') || role.permissions.includes('store:view'))
    );

    // 如果不是系統管理員，檢查是否有權限訪問該門店
    if (!hasSystemRole) {
      // 獲取用戶有權限的門店ID列表
      const storeScopes = userPermissions
        .filter(p => p.scopeType === 'store')
        .map(p => p.scope);

      // 檢查當前門店是否在用戶有權限的門店列表中
      if (!storeScopes.includes(store.id)) {
        return res.status(403).json({ error: '沒有權限訪問該門店' });
      }
    }

    res.json(store);
  } catch (error) {
    console.error('獲取門店失敗:', error);
    res.status(500).json({ error: '獲取門店失敗' });
  }
});

// 創建門店
router.post('/stores', authenticate, checkPermission('store:create'), async (req, res) => {
  try {
    const storeData = req.body;
    // 從查詢參數中獲取是否導入系統數據的選項，預設為 true
    const importSystemData = req.query.importSystemData !== 'false';

    console.log(`創建門店，是否導入系統數據: ${importSystemData}`);

    // 驗證必填字段
    if (!storeData.id || !storeData.name) {
      return res.status(400).json({ error: '門店 ID 和名稱為必填項' });
    }

    const { collection } = await getCollection();

    // 檢查 ID 是否重複
    const existingStore = await collection.findOne({ id: storeData.id });
    if (existingStore) {
      return res.status(400).json({
        error: '門店 ID 已存在',
        message: `門店 ID "${storeData.id}" 已被使用，請使用其他唯一識別碼`,
        code: 'DUPLICATE_STORE_ID',
        field: 'id'
      });
    }

    // 初始化 storeSpecificData 陣列
    let storeSpecificData = [];

    // 如果使用者選擇導入系統數據，則獲取並複製系統專屬數據
    if (importSystemData) {
      // 獲取系統專屬數據
      const { client, db } = await getDbConnection();
      const systemSpecificDataCollection = db.collection('systemSpecificData');
      const systemSpecificDataItems = await systemSpecificDataCollection.find().toArray();

      // 複製系統專屬數據到新門店的 storeSpecificData
      storeSpecificData = systemSpecificDataItems.map(item => {
        // 創建新的 _id 和 uid，但保留其他所有欄位
        const { _id, uid, ...rest } = item;
        return {
          _id: new ObjectId(),
          uid: new ObjectId().toString(),
          ...rest
        };
      });

      console.log(`複製 ${storeSpecificData.length} 筆系統專屬數據到新門店`);
    } else {
      console.log('使用者選擇不導入系統數據');
    }    // 添加創建時間和更新時間
    const now = new Date().toISOString();
    
    // 獲取當前用戶作為默認管理員
    const userId = req.user._id;
    console.log(`設置門店管理員為當前用戶: ${userId}`);
    
    // 確保 id 字段存在且為字符串
    if (typeof storeData.id !== 'string') {
      return res.status(400).json({ error: '門店ID必須為字符串格式' });
    }
      const newStore = {
      ...storeData,
      status: storeData.status || 'active',
      // 確保 managerId 始終是 ObjectId 類型
      managerId: storeData.managerId ? (ObjectId.isValid(storeData.managerId) ? new ObjectId(storeData.managerId) : userId) : userId,
      createdAt: now,
      updatedAt: now,
      // 初始化新字段，並添加系統專屬數據副本（如果有的話）
      storeSpecificData: storeSpecificData,
      gatewayManagement: {},
      deviceManagement: {},
      storeSettings: {}
    };
    
    console.log(`創建新門店 ID: ${newStore.id}, 管理員ID: ${newStore.managerId}`);
    

    const result = await collection.insertOne(newStore);

    // 返回創建的門店數據
    const createdStore = await collection.findOne({ _id: result.insertedId });
    res.status(201).json(createdStore);
  } catch (error) {
    console.error('創建門店失敗:', error);
    res.status(500).json({ error: '創建門店失敗' });
  }
});

// 更新門店
router.put('/stores/:id', authenticate, checkPermission('store:update'), async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const { collection } = await getCollection();

    // 查找門店
    let store = null;
    if (ObjectId.isValid(id)) {
      store = await collection.findOne({ _id: new ObjectId(id) });
    }

    if (!store) {
      store = await collection.findOne({ id });
    }

    if (!store) {
      return res.status(404).json({ error: '門店不存在' });
    }

    // 如果更新 ID，檢查是否重複
    if (updateData.id && updateData.id !== store.id) {
      const existingStore = await collection.findOne({ id: updateData.id });
      if (existingStore) {
        return res.status(400).json({
          error: '門店 ID 已存在',
          message: `門店 ID "${updateData.id}" 已被使用，請使用其他唯一識別碼`,
          code: 'DUPLICATE_STORE_ID',
          field: 'id'
        });
      }
    }    // 更新數據
    const updatedStore = {
      ...updateData,
      updatedAt: new Date().toISOString()
    };
      // 如果沒有指定管理員且原本也沒有管理員，則設置為當前用戶
    if (!updatedStore.managerId && !store.managerId) {
      updatedStore.managerId = req.user._id;
      console.log(`門店更新時設置管理員為當前用戶: ${updatedStore.managerId}`);
    } else if (updatedStore.managerId && typeof updatedStore.managerId === 'string' && ObjectId.isValid(updatedStore.managerId)) {
      // 確保 managerId 是 ObjectId 類型
      updatedStore.managerId = new ObjectId(updatedStore.managerId);
      console.log(`門店更新時設置管理員ID: ${updatedStore.managerId}`);
    }

    await collection.updateOne(
      { _id: store._id },
      { $set: updatedStore }
    );

    // 返回更新後的門店數據
    const result = await collection.findOne({ _id: store._id });
    res.json(result);
  } catch (error) {
    console.error('更新門店失敗:', error);
    res.status(500).json({ error: '更新門店失敗' });
  }
});

// 刪除門店
router.delete('/stores/:id', authenticate, checkPermission('store:delete'), async (req, res) => {
  try {
    const { id } = req.params;

    const { collection } = await getCollection();

    // 查找門店
    let store = null;
    if (ObjectId.isValid(id)) {
      store = await collection.findOne({ _id: new ObjectId(id) });
    }

    if (!store) {
      store = await collection.findOne({ id });
    }

    if (!store) {
      return res.status(404).json({ error: '門店不存在' });
    }

    // 刪除門店
    await collection.deleteOne({ _id: store._id });

    res.status(204).send();
  } catch (error) {
    console.error('刪除門店失敗:', error);
    res.status(500).json({ error: '刪除門店失敗' });
  }
});

// 導出路由器和初始化函數
module.exports = { router, initDB };
