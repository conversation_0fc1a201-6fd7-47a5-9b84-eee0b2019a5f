// EPD Manager App - API 服務

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_CONFIG, STORAGE_KEYS, ERROR_MESSAGES } from '../utils/constants';
import { ApiResponse, User, Store, Gateway, GatewayConfig } from '../types';

class ApiService {
  private axiosInstance: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = `${API_CONFIG.DEFAULT_PROTOCOL}://${API_CONFIG.DEFAULT_HOST}:${API_CONFIG.DEFAULT_PORT}`;
    this.axiosInstance = this.createAxiosInstance();
  }

  /**
   * 創建 Axios 實例
   */
  private createAxiosInstance(): AxiosInstance {
    const instance = axios.create({
      baseURL: this.baseURL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 請求攔截器 - 添加認證 token
    instance.interceptors.request.use(
      async (config) => {
        const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          // 同時設置 Cookie（與主專案保持一致）
          config.headers.Cookie = `token=${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 回應攔截器 - 處理錯誤
    instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error) => {
        if (error.response?.status === 401) {
          // Token 過期，清除本地存儲
          await this.clearAuthData();
          throw new Error(ERROR_MESSAGES.TOKEN_EXPIRED);
        }
        
        if (error.code === 'NETWORK_ERROR' || !error.response) {
          throw new Error(ERROR_MESSAGES.NETWORK_ERROR);
        }
        
        const errorMessage = error.response?.data?.error || ERROR_MESSAGES.SERVER_ERROR;
        throw new Error(errorMessage);
      }
    );

    return instance;
  }

  /**
   * 設置基礎 URL
   */
  setBaseURL(url: string): void {
    this.baseURL = url;
    this.axiosInstance.defaults.baseURL = url;
  }

  /**
   * 清除認證數據
   */
  private async clearAuthData(): Promise<void> {
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.AUTH_TOKEN,
      STORAGE_KEYS.USER_INFO,
    ]);
  }

  // ==================== 認證相關 API ====================

  /**
   * 用戶登入
   */
  async login(username: string, password: string, rememberMe: boolean = false): Promise<ApiResponse<{ user: User; token: string }>> {
    try {
      const response = await this.axiosInstance.post('/api/auth/login', {
        username,
        password,
        rememberMe,
      });

      const { user, token } = response.data;

      // 保存認證信息
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
      await AsyncStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(user));
      
      if (rememberMe) {
        await AsyncStorage.setItem(STORAGE_KEYS.REMEMBER_LOGIN, 'true');
      }

      return {
        success: true,
        data: { user, token },
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 用戶登出
   */
  async logout(): Promise<ApiResponse> {
    try {
      await this.axiosInstance.post('/api/auth/logout');
      await this.clearAuthData();
      
      return {
        success: true,
      };
    } catch (error: any) {
      // 即使服務器端登出失敗，也要清除本地數據
      await this.clearAuthData();
      return {
        success: true,
      };
    }
  }

  /**
   * 檢查認證狀態
   */
  async checkAuth(): Promise<ApiResponse<User>> {
    try {
      const response = await this.axiosInstance.get('/api/auth/me');
      return {
        success: true,
        data: response.data.user,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // ==================== 門店相關 API ====================

  /**
   * 獲取所有門店
   */
  async getStores(): Promise<ApiResponse<Store[]>> {
    try {
      const response = await this.axiosInstance.get('/api/stores');
      
      // 處理不同的回應格式
      let stores: Store[] = [];
      if (Array.isArray(response.data)) {
        stores = response.data;
      } else if (response.data.stores && Array.isArray(response.data.stores)) {
        stores = response.data.stores;
      } else if (response.data.data && Array.isArray(response.data.data)) {
        stores = response.data.data;
      }

      return {
        success: true,
        data: stores,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // ==================== 網關相關 API ====================

  /**
   * 獲取門店的網關列表
   */
  async getGateways(storeId: string): Promise<ApiResponse<Gateway[]>> {
    try {
      const response = await this.axiosInstance.get(`/api/gateways?storeId=${storeId}`);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 創建新網關
   */
  async createGateway(gatewayConfig: GatewayConfig): Promise<ApiResponse<Gateway>> {
    try {
      const response = await this.axiosInstance.post('/api/gateways', gatewayConfig);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 獲取單個網關詳情
   */
  async getGateway(gatewayId: string, storeId?: string): Promise<ApiResponse<Gateway>> {
    try {
      let url = `/api/gateways/${gatewayId}`;
      if (storeId) {
        url += `?storeId=${storeId}`;
      }
      
      const response = await this.axiosInstance.get(url);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 更新網關信息
   */
  async updateGateway(gatewayId: string, updates: Partial<Gateway>): Promise<ApiResponse<Gateway>> {
    try {
      const response = await this.axiosInstance.put(`/api/gateways/${gatewayId}`, updates);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 刪除網關
   */
  async deleteGateway(gatewayId: string): Promise<ApiResponse> {
    try {
      await this.axiosInstance.delete(`/api/gateways/${gatewayId}`);
      return {
        success: true,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 測試服務器連接
   */
  async testConnection(): Promise<ApiResponse> {
    try {
      const response = await this.axiosInstance.get('/api/health');
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 獲取當前基礎 URL
   */
  getBaseURL(): string {
    return this.baseURL;
  }
}

// 創建單例實例
export const apiService = new ApiService();
export default apiService;
