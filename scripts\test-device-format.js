// 測試新的設備數據格式
const sampleDevice = {
  macAddress: '11:22:33:44:55:66',
  status: 'online',
  dataId: 'test001',
  storeId: 'store1',
  
  // 新增與網關綁定相關欄位
  initialized: true,                  // 是否已初始化
  primaryGatewayId: '60d2f4a45d432a1f345678ab', // 主要網關ID
  otherGateways: ['60d2f4a45d432a1f345678cd', '60d2f4a45d432a1f345678ef'], // 其他發現此設備的網關ID列表
  
  // 新增用戶綁定欄位
  userId: '609f1c9b5f8c443e9f7654ab',  // 綁定的用戶ID
  
  lastSeen: new Date(),                 // 使用 Date 對象而非字符串
  note: '測試設備',
  
  // 設備本地相關數據
  data: {
    size: '2.9"',
    rssi: -75,
    battery: 85,
    imgcode: 'IMG12345'                // 新增 imgcode 欄位取代之前的 code
  }
};

console.log('修改後的設備數據格式:');
console.log(JSON.stringify(sampleDevice, null, 2));
