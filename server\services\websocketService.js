// WebSocket 服務實現
const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const { URL } = require('url');
const { ObjectId } = require('mongodb');

// 定義簡短的顏色類型代碼轉換函數
const shortCodeToDisplayColorType = (code) => {
  if (!code) return 'UNKNOWN';

  switch (code.toUpperCase()) {
    case 'BW':
      return 'Gray16';
    case 'BWR':
      return 'Black & White & Red';
    case 'BWRY':
      return 'Black & White & Red & Yellow';
    case 'ALL':
      return 'All colors';
    default:
      console.warn(`未知的顏色類型代碼: ${code}，將顯示為 UNKNOWN`);
      return 'UNKNOWN';
  }
};

// 共享的數據庫連接
let getDbConnection = null;

// WebSocket 伺服器
let wss = null;

// 用於存儲連接的網關
const connectedGateways = new Map();

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

// 獲取網關集合
const getGatewayCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('gateways');
  return { collection, client };
};

// 獲取設備集合
const getDeviceCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('devices');
  return { collection, client };
};

// 初始化 WebSocket 服務
const initWebSocketServer = (server, jwtSecret) => {
  wss = new WebSocket.Server({
    server,
    // 不使用固定路徑，而是在 verifyClient 中解析路徑
    verifyClient: async (info, callback) => {
      try {
        // 獲取請求URL
        const url = new URL(info.req.url, `http://${info.req.headers.host}`);
        const token = url.searchParams.get('token');

        if (!token) {
          console.error('WebSocket 連接缺少 token');
          return callback(false, 401, 'Unauthorized');
        }

        // 驗證 token
        const decoded = jwt.verify(token, jwtSecret);

        // 檢查是否為網關類型的 token
        if (decoded.type !== 'gateway') {
          console.error('WebSocket token 類型不匹配');
          return callback(false, 401, 'Invalid token type');
        }

        // 解析路徑 (例如 /ws/store/{storeId}/gateway/{gatewayId})
        const pathParts = url.pathname.split('/');
        const storeIdFromPath = pathParts[pathParts.indexOf('store') + 1];
        const gatewayIdFromPath = pathParts[pathParts.indexOf('gateway') + 1];        // 驗證 token 中的網關 ID 與路徑中的網關 ID 是否匹配
        if (gatewayIdFromPath && gatewayIdFromPath !== decoded.gatewayId) {
          console.error('WebSocket 路徑中的網關 ID 與 token 不匹配');
          return callback(false, 403, 'Gateway ID mismatch');
        }

        // 驗證 token 中的商店 ID 與路徑中的商店 ID 是否匹配
        if (storeIdFromPath && storeIdFromPath !== decoded.storeId) {
          console.error('WebSocket 路徑中的商店 ID 與 token 不匹配');
          return callback(false, 403, 'Store ID mismatch');
        }

        // 檢查 token 中是否包含 MAC 地址
        if (!decoded.macAddress) {
          console.error('WebSocket token 缺少 MAC 地址');
          return callback(false, 403, 'MAC address missing in token');
        }

        // 驗證通過，將 token 數據附加到 req 對象
        info.req.gatewayId = decoded.gatewayId;
        info.req.storeId = decoded.storeId;
        info.req.macAddress = decoded.macAddress; // 添加 MAC 地址到 req 對象
        info.req.token = decoded;

        callback(true);
      } catch (error) {
        console.error('WebSocket 認證錯誤:', error);
        callback(false, 401, 'Authentication failed');
      }
    }
  });

  // 監聽連接事件
  wss.on('connection', handleConnection);

  console.log('WebSocket 服務已啟動');
  return wss;
};

// 處理新的 WebSocket 連接
const handleConnection = async (ws, req) => {
  try {
    const { gatewayId, storeId, macAddress } = req;
    console.log(`網關 ${gatewayId} (門店: ${storeId}, MAC: ${macAddress}) 已連接`);

    // 將 gatewayId、storeId 和 macAddress 附加到 WebSocket 實例
    ws.gatewayId = gatewayId;
    ws.storeId = storeId;
    ws.macAddress = macAddress; // 添加MAC地址到WebSocket實例
    ws.isAlive = true;
    ws.connectionTime = Date.now();
    ws.lastActivityTime = Date.now(); // 記錄上次活動時間

    // 存儲連接
    connectedGateways.set(gatewayId, ws);

    // 更新網關狀態為在線
    await updateGatewayStatus(gatewayId, 'online');

    // 發送歡迎消息
    ws.send(JSON.stringify({
      type: 'welcome',
      message: 'WebSocket 連接成功',
      timestamp: Date.now(),
      gatewayInfo: {
        gatewayId: gatewayId,
        storeId: storeId,
        macAddress: macAddress // 在歡迎消息中包含MAC地址
      },
      serverInfo: {
        time: new Date().toISOString(),
        version: '1.0.0'
      }
    }));

    // 設置 ping/pong 心跳檢測
    ws.on('pong', () => {
      ws.isAlive = true;
      ws.lastActivityTime = Date.now(); // 更新最後活動時間
    });

    // 處理接收到的消息
    ws.on('message', (message) => {
      ws.lastActivityTime = Date.now(); // 更新最後活動時間
      handleMessage(ws, message);
    });

    // 處理關閉連接
    ws.on('close', async (code, reason) => {
      console.log(`網關 ${gatewayId} 已斷開連接，代碼: ${code}, 原因: ${reason || '未提供'}`);
      connectedGateways.delete(gatewayId);
      await updateGatewayStatus(gatewayId, 'offline');
      // 記錄網關離線事件
      logGatewayEvent(gatewayId, 'disconnect', { code, reason });
    });

    // 處理錯誤
    ws.on('error', async (error) => {
      console.error(`網關 ${gatewayId} WebSocket 錯誤:`, error);
      // 記錄網關錯誤事件
      logGatewayEvent(gatewayId, 'error', { message: error.message, stack: error.stack });
    });

    // 記錄網關連接事件
    logGatewayEvent(gatewayId, 'connect', { storeId });
  } catch (error) {
    console.error('處理 WebSocket 連接時發生錯誤:', error);
    ws.close(1011, 'Server error');
  }
};

// 更新網關狀態 (online/offline)
const updateGatewayStatus = async (gatewayId, status) => {
  try {
    const { collection } = await getGatewayCollection();

    await collection.updateOne(
      { _id: new ObjectId(gatewayId) },
      {
        $set: {
          status: status,
          lastSeen: new Date(),
          updatedAt: new Date()
        }
      }
    );

    console.log(`已更新網關 ${gatewayId} 狀態為 ${status}`);
  } catch (error) {
    console.error(`更新網關 ${gatewayId} 狀態失敗:`, error);
  }
};

// 處理 WebSocket 消息
const handleMessage = async (ws, message) => {
  try {
    const data = JSON.parse(message);
    const { type } = data;
    const gatewayId = ws.gatewayId;
    const storeId = ws.storeId;

    console.log(`收到來自網關 ${gatewayId} 的消息類型: ${type}`);

    switch (type) {
      case 'ping':
        // 處理心跳消息
        handlePingMessage(ws, data);
        break;

      case 'deviceStatus':
        // 處理設備狀態更新消息
        await handleDeviceStatusMessage(ws, data);
        break;

      case 'gatewayInfo':
        // 處理網關信息更新
        await handleGatewayInfoMessage(ws, data);
        break;

      default:
        console.warn(`收到未知類型的消息: ${type}`);
        ws.send(JSON.stringify({
          type: 'error',
          message: '未知的消息類型',
          originalType: type,
          timestamp: Date.now()
        }));
    }
  } catch (error) {
    console.error('處理 WebSocket 消息時發生錯誤:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: '處理消息時發生錯誤',
      error: error.message,
      timestamp: Date.now()
    }));
  }
};

// 處理 ping 消息
const handlePingMessage = (ws, data) => {
  const response = {
    type: 'pong',
    timestamp: data.timestamp,
    serverTime: Date.now()
  };
  ws.send(JSON.stringify(response));
};

// 處理設備狀態消息
const handleDeviceStatusMessage = async (ws, data) => {
  try {
    const gatewayId = ws.gatewayId;
    const storeId = ws.storeId;
    const devices = data.devices || [];

    if (!Array.isArray(devices) || devices.length === 0) {
      throw new Error('設備列表為空或無效');
    }

    console.log(`處理來自網關 ${gatewayId} 的 ${devices.length} 個設備狀態更新`);

    // 處理每個設備的狀態更新
    const processedDevices = await updateDeviceStatus(gatewayId, devices, storeId);

    // 發送確認回應
    ws.send(JSON.stringify({
      type: 'deviceStatusAck',
      timestamp: Date.now(),
      success: true,
      message: '設備狀態更新成功',
      detailStatus: processedDevices.map(device => ({
        macAddress: device.macAddress,
        status: 'success',
        deviceId: device._id.toString()
      }))
    }));
  } catch (error) {
    console.error('處理設備狀態消息失敗:', error);
    ws.send(JSON.stringify({
      type: 'deviceStatusAck',
      timestamp: Date.now(),
      success: false,
      message: '處理設備狀態失敗: ' + error.message
    }));
  }
};

// 處理網關信息更新消息
const handleGatewayInfoMessage = async (ws, data) => {
  try {
    const gatewayId = ws.gatewayId;
    const tokenMacAddress = ws.macAddress; // 從WebSocket連接中獲取token中的MAC地址
    const info = data.info || {};

    if (!info || typeof info !== 'object') {
      throw new Error('網關信息無效');
    }

    console.log(`更新網關 ${gatewayId} 的信息`);

    // 更新網關信息
    const { collection } = await getGatewayCollection();

    // 確保網關存在
    const gateway = await collection.findOne({ _id: new ObjectId(gatewayId) });
    if (!gateway) {
      throw new Error('找不到指定的網關');
    }

    // 驗證網關發送的MAC地址與token中的MAC地址是否匹配
    if (info.macAddress && info.macAddress !== tokenMacAddress) {
      console.error(`安全警告: 網關 ${gatewayId} 發送的MAC地址 (${info.macAddress}) 與token中的MAC地址 (${tokenMacAddress}) 不匹配，強制中斷連線`);

      // 記錄安全事件
      await logGatewayEvent(gatewayId, 'security-violation', {
        type: 'mac_address_mismatch',
        reportedMac: info.macAddress,
        tokenMac: tokenMacAddress,
        clientIP: ws.clientIP || 'unknown',
        timestamp: new Date().toISOString()
      });

      // 發送錯誤回應後立即關閉連接
      try {
        ws.send(JSON.stringify({
          type: 'gatewayInfoAck',
          timestamp: Date.now(),
          success: false,
          message: 'MAC地址不匹配，連線已中斷',
          fatal: true
        }));
      } catch (sendError) {
        console.error('發送錯誤回應失敗:', sendError);
      }

      // 強制關閉連接
      setTimeout(() => {
        ws.terminate();
      }, 100); // 給一點時間讓錯誤消息發送出去

      return; // 直接返回，不繼續處理
    }

    // 更新網關信息
    await collection.updateOne(
      { _id: new ObjectId(gatewayId) },
      {
        $set: {
          name: info.name || gateway.name,
          model: info.model || gateway.model,
          wifiFirmwareVersion: info.wifiFirmwareVersion || gateway.wifiFirmwareVersion,
          btFirmwareVersion: info.btFirmwareVersion || gateway.btFirmwareVersion,
          ipAddress: info.ipAddress || gateway.ipAddress,
          lastSeen: new Date(),
          updatedAt: new Date()
        }
      }
    );

    // 發送確認回應
    ws.send(JSON.stringify({
      type: 'gatewayInfoAck',
      timestamp: Date.now(),
      success: true,
      message: '網關信息更新成功'
    }));
  } catch (error) {
    console.error('處理網關信息消息失敗:', error);
    ws.send(JSON.stringify({
      type: 'gatewayInfoAck',
      timestamp: Date.now(),
      success: false,
      message: '處理網關信息失敗: ' + error.message
    }));
  }
};



// 更新設備狀態
const updateDeviceStatus = async (gatewayId, devices, storeId) => {
  // 獲取數據庫連接
  const { collection: deviceCollection } = await getDeviceCollection();
  const { collection: gatewayCollection } = await getGatewayCollection();

  const processedDevices = [];
  const gatewayObjectId = new ObjectId(gatewayId);

  // 查詢網關信息
  const gateway = await gatewayCollection.findOne({ _id: gatewayObjectId });
  if (!gateway) {
    throw new Error(`網關 ${gatewayId} 不存在`);
  }

  for (const device of devices) {
    try {
      // 確保設備有有效的 MAC 地址
      if (!device.macAddress || !/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(device.macAddress)) {
        console.warn(`來自網關 ${gatewayId} 的無效設備 MAC 地址: ${device.macAddress}`);
        continue;
      }

      // 查找是否已存在相同MAC地址的設備
      const existingDevice = await deviceCollection.findOne({ macAddress: device.macAddress });      if (!existingDevice) {
        // 如果設備不存在，創建新設備並設置為已初始化狀態
        // 因為是通過WebSocket發現的設備，所以直接標記為已初始化

        // 處理 colorType 轉換
        let deviceData = { ...(device.data || {}) };

        // 從裝置回報的數據中移除 imageCode，避免將其寫入資料庫
        if (deviceData.imageCode !== undefined) {
          delete deviceData.imageCode;
          console.log(`已從新設備 ${device.macAddress} 回報的數據中移除 imageCode，防止寫入資料庫`);
        }

        if (deviceData.colorType !== undefined) {
          const convertedColorType = shortCodeToDisplayColorType(deviceData.colorType);
          deviceData.colorType = convertedColorType;
          console.log(`新設備 ${device.macAddress} 的顏色類型從 ${device.data?.colorType} 轉換為 ${convertedColorType}`);
        }

        const newDevice = {
          macAddress: device.macAddress,
          status: 'online',
          dataId: device.dataId || '',
          primaryGatewayId: gatewayObjectId,  // 將當前網關設置為主要網關
          initialized: true,  // 設為已初始化，因為是通過網關發現
          storeId: storeId,
          otherGateways: [],
          lastSeen: new Date(),
          note: device.note || '',
          data: {
            size: device.data?.size || '10.3',
            battery: device.data?.battery || 100,
            rssi: device.data?.rssi || -50,
            ...deviceData
          },
          createdAt: new Date(),
          updatedAt: new Date()
        };
          // 獲取網關所在門店的預設管理員資訊，用於自動綁定設備
        try {          const { db } = await getDbConnection();
          const storeCollection = db.collection('stores');
          const gatewayCollection = db.collection('gateways');

          // 獲取網關記錄，以得到正確的門店ID
          const gateway = await gatewayCollection.findOne({ _id: new ObjectId(gatewayId) });

          if (!gateway) {
            throw new Error(`找不到ID為 "${gatewayId}" 的網關`);
          }

          const gatewayStoreId = gateway.storeId;
          console.log(`網關 ${gatewayId} 關聯的門店ID: ${gatewayStoreId}`);

          // 首先嘗試使用 id 字段查詢
          let store = await storeCollection.findOne({ id: gatewayStoreId });

          // 如果沒找到，嘗試使用 _id 字段查詢（如果 storeId 是有效的 ObjectId）
          if (!store && ObjectId.isValid(gatewayStoreId)) {
            store = await storeCollection.findOne({ _id: new ObjectId(gatewayStoreId) });
          }

          // 如果還是找不到，嘗試查詢所有門店，這是一個最後的手段
          if (!store) {
            console.warn(`通過網關的 storeId "${gatewayStoreId}" 找不到門店，嘗試使用參數中的 storeId "${storeId}"`);
            store = await storeCollection.findOne({ id: storeId });

            if (!store && ObjectId.isValid(storeId)) {
              store = await storeCollection.findOne({ _id: new ObjectId(storeId) });
            }
          }

          // 如果找到門店且有管理員，自動綁定給管理員
          console.log(`===== 設備自動綁定日誌 =====`);
          console.log(`門店資訊: ${JSON.stringify(store)}`);
          console.log(`門店ID: ${storeId}, 管理員ID存在檢查: ${Boolean(store && store.managerId)}`);

          if (store && store.managerId) {
            console.log(`正在將設備 ${device.macAddress} 自動綁定到管理員 ${store.managerId}`);
            newDevice.userId = new ObjectId(store.managerId);
            console.log(`綁定成功，設備 userId 已設置為: ${newDevice.userId}`);
          } else {
            console.warn(`無法綁定設備: 門店 ${storeId} 沒有指定管理員`);
          }
        } catch (storeError) {
          console.warn(`查詢網關所屬門店資訊失敗，設備暫不綁定用戶: ${storeError.message}`);
          // 繼續創建設備，但不綁定用戶
        }

        const result = await deviceCollection.insertOne(newDevice);
        const insertedDevice = { ...newDevice, _id: result.insertedId };
        processedDevices.push(insertedDevice);

        // 將設備ID添加到網關的設備列表中
        await gatewayCollection.updateOne(
          { _id: gatewayObjectId },
          { $addToSet: { devices: result.insertedId } }
        );

        // 記錄新設備發現事件
        await logDeviceEvent(result.insertedId, 'discovered', {
          gatewayId: gatewayId,
          storeId: storeId,
          initialStatus: 'online',
          macAddress: device.macAddress
        });
          // 如果自動綁定了用戶，記錄綁定事件
        if (newDevice.userId) {
          try {
            const { db } = await getDbConnection();
            const userCollection = db.collection('users');
            const user = await userCollection.findOne({ _id: newDevice.userId });

            await logDeviceEvent(result.insertedId, 'user_binding', {
              action: 'auto_bind',
              userId: newDevice.userId.toString(),
              userName: user ? (user.name || user.username || '未知') : '未知',
              userEmail: user ? (user.email || '未知') : '未知',
              note: '設備被網關發現時自動綁定到門店管理員'
            });
          } catch (eventError) {
            console.warn('記錄自動綁定事件失敗:', eventError);
            // 不中斷流程
          }
        }

        console.log(`網關 ${gatewayId} 發現新設備 ${device.macAddress}`);
        continue;
      }

      // 更新設備狀態和最後活動時間
      const updateData = {
        status: 'online',
        lastSeen: new Date(),
        updatedAt: new Date()
      };      // 更新設備數據
      if (device.data) {
        const existingData = existingDevice.data || {};
        const deviceData = { ...device.data };

        // 從裝置回報的數據中移除 imageCode，避免覆蓋資料庫中的值
        if (deviceData.imageCode !== undefined) {
          delete deviceData.imageCode;
          console.log(`已從裝置 ${device.macAddress} 回報的數據中移除 imageCode，防止覆蓋資料庫值`);
        }

        // 更新設備數據中的特定欄位，但不包含 imageCode
        updateData.data = {
          ...existingData,
          ...deviceData
        };

        // 處理 colorType 轉換
        if (device.data.colorType !== undefined) {
          const convertedColorType = shortCodeToDisplayColorType(device.data.colorType);
          updateData.data.colorType = convertedColorType;
          console.log(`設備 ${device.macAddress} 的顏色類型從 ${device.data.colorType} 轉換為 ${convertedColorType}`);
        }

        // 確保必要的欄位存在
        if (device.data.battery !== undefined) updateData.data.battery = device.data.battery;
        if (device.data.rssi !== undefined) updateData.data.rssi = device.data.rssi;
        if (device.data.size !== undefined) updateData.data.size = device.data.size;

        // 檢查裝置回報的 imageCode 與資料庫中儲存的 imageCode
        if (device.data.imageCode !== undefined) {
          // 檢查裝置是否有 imageCode 欄位
          // 注意：imageCode 存儲在 data.imageCode 中
          const deviceImageCode = existingDevice.data?.imageCode || '';
          // 比對回報的 imageCode 與資料庫中的 imageCode
          if (deviceImageCode && device.data.imageCode === deviceImageCode) {
            // 相同代表已更新
            updateData.imageUpdateStatus = '已更新';
            console.log(`設備 ${device.macAddress} 圖片已更新成功，imageCode: ${device.data.imageCode}`);
          } else if (deviceImageCode) {
            // 不同代表未更新
            updateData.imageUpdateStatus = '未更新';
            console.log(`設備 ${device.macAddress} 圖片未更新，期望: ${deviceImageCode}, 實際: ${device.data.imageCode}`);
          }
          console.log(`設備 ${device.macAddress} 的圖片更新狀態更新為: ${updateData.imageUpdateStatus || '未設置'}`);
        }
      }

        // 處理設備初始化和網關綁定邏輯
      if (!existingDevice.initialized) {
        // 設備首次被發現，將當前網關設為主要網關
        updateData.primaryGatewayId = gatewayObjectId;
        updateData.initialized = true;
        updateData.gatewaySelectionMode = 'manual'; // 默認為固定模式
        console.log(`設備 ${existingDevice._id} 已被網關 ${gatewayId} 初始化`);

        // 將設備ID添加到網關的設備列表中
        await gatewayCollection.updateOne(
          { _id: gatewayObjectId },
          { $addToSet: { devices: existingDevice._id } }
        );

        // 如果設備由API創建時綁定了用戶，則保留這個綁定
        // API創建的設備已經有用戶ID，我們只需更新初始化狀態和主要網關
      } else if (existingDevice.primaryGatewayId) {        // 如果設備處於自動網關選擇模式，進行主要網關的自動選擇
        if (existingDevice.gatewaySelectionMode === 'auto') {
          const oldPrimaryGatewayId = existingDevice.primaryGatewayId;
          let needSwitchGateway = false;
          let switchReason = '';

          // 檢查當前主要網關是否離線
          if (!isGatewayOnline(oldPrimaryGatewayId.toString())) {
            needSwitchGateway = true;
            switchReason = '主要網關離線';
          }
          // 檢查信號強度是否更好（只在主要網關在線時比較）
          else if (device.data && device.data.rssi !== undefined &&
                  existingDevice.data && existingDevice.data.rssi !== undefined &&
                  device.data.rssi > existingDevice.data.rssi + 15) {
            needSwitchGateway = true;
            switchReason = `信號強度更好 (${device.data.rssi} > ${existingDevice.data.rssi})`;
          }

          // 如果需要切換網關
          if (needSwitchGateway) {
            // 記錄主要網關切換事件
            await logDeviceEvent(existingDevice._id, 'gateway_changed', {
              action: 'auto_switch_gateway',
              oldPrimaryGatewayId: oldPrimaryGatewayId.toString(),
              newPrimaryGatewayId: gatewayId,
              oldRssi: existingDevice.data?.rssi,
              newRssi: device.data?.rssi,
              gatewayName: gateway.name || '未知',
              reason: switchReason
            });

            // 將舊主要網關添加到其他網關列表
            await deviceCollection.updateOne(
              { _id: existingDevice._id },
              { $addToSet: { otherGateways: oldPrimaryGatewayId } }
            );

            // 設置新的主要網關
            updateData.primaryGatewayId = gatewayObjectId;

            // 從其他網關列表中移除新主要網關
            await deviceCollection.updateOne(
              { _id: existingDevice._id },
              { $pull: { otherGateways: gatewayObjectId } }
            );

            console.log(`自動模式：設備 ${existingDevice._id} 的主要網關已從 ${oldPrimaryGatewayId} 切換到 ${gatewayId} (原因: ${switchReason})`);
          }
        }

        // 檢查當前網關是否是主要網關
        if (!existingDevice.primaryGatewayId.equals(gatewayObjectId)) {
          // 當前網關不是主要網關，應添加到其他網關列表
          // 確保 otherGateways 是一個數組
          const otherGateways = Array.isArray(existingDevice.otherGateways)
            ? existingDevice.otherGateways
            : [];

          // 檢查當前網關是否已在其他網關列表中
          // 使用更安全的方式來比較 ObjectId
          const isGatewayInList = otherGateways.some(id =>
            (id && id.equals && id.equals(gatewayObjectId)) ||
            (id && id.toString() === gatewayId)
          );          if (!isGatewayInList) {
            // 將設備ID添加到網關的設備列表中
            await gatewayCollection.updateOne(
              { _id: gatewayObjectId },
              { $addToSet: { devices: existingDevice._id } }
            );

            // 準備更新操作，添加當前網關到其他網關列表
            // 這裡需要分開使用 $set 和 $addToSet 操作符
            await deviceCollection.updateOne(
              { _id: existingDevice._id },
              {
                $set: updateData,
                $addToSet: { otherGateways: gatewayObjectId }
              }
            );

            console.log(`設備 ${existingDevice._id} 添加了新的發現網關 ${gatewayId}`);

            // 獲取更新後的設備，用於返回處理結果
            const updatedDevice = await deviceCollection.findOne({ _id: existingDevice._id });
            processedDevices.push(updatedDevice);
            continue;  // 繼續處理下一個設備
          }
        }
      } else {
        // 如果沒有主要網關（這種情況不應該發生，但為了穩健性處理一下）
        updateData.primaryGatewayId = gatewayObjectId;
        console.log(`設備 ${existingDevice._id} 設置主要網關為 ${gatewayId}`);
      }
        // 只執行標準更新 ($set 操作)，沒有添加到 otherGateways
      await deviceCollection.updateOne(
        { _id: existingDevice._id },
        { $set: updateData }
      );

      // 獲取更新後的設備，用於返回處理結果
      const updatedDevice = await deviceCollection.findOne({ _id: existingDevice._id });
      processedDevices.push(updatedDevice);
    } catch (deviceError) {
      console.error(`處理設備 ${device.macAddress} 狀態時發生錯誤:`, deviceError);
      // 繼續處理下一個設備，而不是中斷整個流程
    }
  }

  return processedDevices;
};

// 記錄設備事件 (如狀態變更、數據更新等)
const logDeviceEvent = async (deviceId, eventType, eventData = {}) => {
  try {
    if (!getDbConnection) {
      console.warn(`無法記錄設備事件: 數據庫連接未初始化`);
      return;
    }

    // 驗證 deviceId 是否有效
    if (!deviceId || !(deviceId instanceof ObjectId || typeof deviceId === 'string')) {
      console.error('嘗試記錄設備事件時提供了無效的設備ID');
      return;
    }

    // 確保 deviceId 是 ObjectId 類型
    const deviceObjectId = deviceId instanceof ObjectId ? deviceId : new ObjectId(deviceId);

    const { db } = await getDbConnection();

    // 確保 deviceEvents 集合存在
    const collections = await db.listCollections({ name: 'deviceEvents' }).toArray();
    if (collections.length === 0) {
      await db.createCollection('deviceEvents');
      // 創建索引以提高查詢效率
      await db.collection('deviceEvents').createIndex({ deviceId: 1 });
      await db.collection('deviceEvents').createIndex({ timestamp: -1 });
      await db.collection('deviceEvents').createIndex({ eventType: 1 });
    }

    const eventCollection = db.collection('deviceEvents');

    // 獲取設備詳情以豐富事件數據
    const deviceCollection = db.collection('devices');
    const device = await deviceCollection.findOne({ _id: deviceObjectId });

    if (!device) {
      console.warn(`找不到ID為 ${deviceId} 的設備，但仍將記錄事件`);
    }

    // 創建標準化的事件對象
    const event = {
      deviceId: deviceObjectId,
      eventType, // 'online', 'offline', 'data_update', 'binding_changed', 'gateway_changed' 等
      eventData,
      timestamp: new Date(),
      deviceMac: device?.macAddress || 'unknown',
      storeId: device?.storeId || null
    };

    // 寫入事件紀錄
    await eventCollection.insertOne(event);

    console.log(`已記錄設備 ${deviceId} 的 ${eventType} 事件`);

    // 定期清理舊事件（保留90天的設備事件紀錄）
    if (Math.random() < 0.005) { // 0.5% 的概率執行清理，避免每次都執行
      const ninetyDaysAgo = new Date();
      ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

      eventCollection.deleteMany({ timestamp: { $lt: ninetyDaysAgo } })
        .then(result => {
          if (result.deletedCount > 0) {
            console.log(`已清理 ${result.deletedCount} 條過期的設備事件紀錄`);
          }
        })
        .catch(cleanupError => {
          console.error('清理舊設備事件紀錄失敗:', cleanupError);
        });
    }

    return event;
  } catch (error) {
    console.error(`記錄設備 ${deviceId} 事件失敗:`, error);
    return null;
  }
};

// 定期檢查連接狀態
const setupHeartbeatCheck = () => {
  const pingInterval = 30000; // 30 秒
  const terminateThreshold = 60000; // 60 秒沒有回應則斷開連接

  const interval = setInterval(() => {
    const now = Date.now();

    wss.clients.forEach((ws) => {
      // 檢查心跳超時
      if (ws.isAlive === false) {
        console.log(`網關 ${ws.gatewayId} 心跳檢測失敗，關閉連接`);
        // 記錄心跳檢測失敗事件
        logGatewayEvent(ws.gatewayId, 'heartbeat-fail', {
          lastActivityTime: new Date(ws.lastActivityTime).toISOString(),
          inactiveForMs: now - ws.lastActivityTime
        });
        return ws.terminate();
      }

      // 檢查長時間無活動
      if (now - ws.lastActivityTime > terminateThreshold) {
        console.log(`網關 ${ws.gatewayId} 長時間無活動 (${(now - ws.lastActivityTime) / 1000}秒)，關閉連接`);
        // 記錄無活動事件
        logGatewayEvent(ws.gatewayId, 'inactivity-timeout', {
          lastActivityTime: new Date(ws.lastActivityTime).toISOString(),
          inactiveForMs: now - ws.lastActivityTime
        });
        return ws.terminate();
      }

      // 重置心跳標誌並發送 ping
      ws.isAlive = false;
      try {
        ws.ping();
      } catch (error) {
        console.error(`向網關 ${ws.gatewayId} 發送 ping 失敗:`, error);
      }
    });
  }, pingInterval);

  // 當 WebSocket 服務關閉時，清除定時器
  wss.on('close', () => {
    clearInterval(interval);
  });

  console.log(`心跳檢測已啟動，間隔: ${pingInterval}ms，超時閾值: ${terminateThreshold}ms`);
};

// 記錄網關事件
const logGatewayEvent = async (gatewayId, eventType, eventData = {}) => {
  try {
    if (!getDbConnection) {
      console.warn(`無法記錄網關事件: 數據庫連接未初始化`);
      return;
    }

    // 驗證 gatewayId 是否有效
    if (!gatewayId || typeof gatewayId !== 'string') {
      console.error('嘗試記錄網關事件時提供了無效的網關ID');
      return;
    }

    const { db } = await getDbConnection();

    // 確保 gatewayEvents 集合存在
    const collections = await db.listCollections({ name: 'gatewayEvents' }).toArray();
    if (collections.length === 0) {
      await db.createCollection('gatewayEvents');
      // 創建索引以提高查詢效率
      await db.collection('gatewayEvents').createIndex({ gatewayId: 1 });
      await db.collection('gatewayEvents').createIndex({ timestamp: -1 });
      await db.collection('gatewayEvents').createIndex({ eventType: 1 });
    }

    const eventCollection = db.collection('gatewayEvents');

    // 創建標準化的事件對象
    const event = {
      gatewayId: new ObjectId(gatewayId),
      eventType, // 'connect', 'disconnect', 'error', 'heartbeat-fail', 'inactivity-timeout' 等
      eventData,
      timestamp: new Date(),
      storeId: null, // 稍後將被更新
      gatewayName: null // 稍後將被更新
    };

    // 嘗試獲取網關相關信息以豐富事件數據
    try {
      const gateway = await db.collection('gateways').findOne({ _id: new ObjectId(gatewayId) });
      if (gateway) {
        event.storeId = gateway.storeId;
        event.gatewayName = gateway.name;
      }
    } catch (gatewayLookupError) {
      console.warn(`獲取網關 ${gatewayId} 的額外信息失敗:`, gatewayLookupError.message);
    }

    // 寫入事件紀錄
    await eventCollection.insertOne(event);

    // 對於嚴重事件，增加額外的日誌輸出
    if (['error', 'heartbeat-fail', 'inactivity-timeout', 'security-violation'].includes(eventType)) {
      console.warn(`網關 ${gatewayId} ${event.gatewayName ? `(${event.gatewayName})` : ''} 發生 ${eventType} 事件: `,
                  JSON.stringify(eventData));
    } else {
      console.log(`已記錄網關 ${gatewayId} 的 ${eventType} 事件`);
    }

    // 定期清理舊事件（保留30天的事件紀錄）
    if (Math.random() < 0.01) { // 1% 的概率執行清理，避免每次都執行
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      eventCollection.deleteMany({ timestamp: { $lt: thirtyDaysAgo } })
        .then(result => {
          if (result.deletedCount > 0) {
            console.log(`已清理 ${result.deletedCount} 條過期的網關事件紀錄`);
          }
        })
        .catch(cleanupError => {
          console.error('清理舊網關事件紀錄失敗:', cleanupError);
        });
    }
  } catch (error) {
    console.error(`記錄網關 ${gatewayId} 事件失敗:`, error);
  }
};

// 獲取網關連接統計信息
const getGatewayConnectionStats = async () => {
  try {
    const stats = {
      totalConnected: connectedGateways.size,
      connectedGateways: [],
      lastUpdated: new Date()
    };

    // 獲取每個網關的連接詳情
    if (connectedGateways.size > 0) {
      const { collection } = await getGatewayCollection();

      for (const [gatewayId, ws] of connectedGateways.entries()) {
        try {
          const gateway = await collection.findOne({ _id: new ObjectId(gatewayId) });

          if (gateway) {
            stats.connectedGateways.push({
              id: gatewayId,
              name: gateway.name || 'Unknown',
              storeId: gateway.storeId,
              ipAddress: gateway.ipAddress,
              connectionTime: new Date(ws.connectionTime),
              lastActivity: new Date(ws.lastActivityTime),
              inactiveForMs: Date.now() - ws.lastActivityTime
            });
          } else {
            stats.connectedGateways.push({
              id: gatewayId,
              name: 'Unknown',
              connectionTime: new Date(ws.connectionTime),
              lastActivity: new Date(ws.lastActivityTime),
              inactiveForMs: Date.now() - ws.lastActivityTime
            });
          }
        } catch (error) {
          console.error(`獲取網關 ${gatewayId} 的連接統計信息失敗:`, error);
        }
      }
    }

    return stats;
  } catch (error) {
    console.error('獲取網關連接統計信息失敗:', error);
    return { error: error.message, totalConnected: 0, connectedGateways: [] };
  }
};

// 檢查特定網關是否在線
const isGatewayOnline = (gatewayId) => {
  return connectedGateways.has(gatewayId);
};

// 向特定網關發送命令
const sendCommandToGateway = async (gatewayId, command) => {
  try {
    const ws = connectedGateways.get(gatewayId);
    if (!ws) {
      throw new Error(`網關 ${gatewayId} 不在線`);
    }

    let message;

    // 檢查命令是否為對象類型，如果是，則直接使用該對象
    if (typeof command === 'object' && command !== null) {
      // 如果命令對象已經有type屬性，則直接使用
      if (!command.type) {
        // 否則，設置為默認的command類型
        command.type = 'command';
      }

      // 確保有時間戳
      if (!command.timestamp) {
        command.timestamp = Date.now();
      }

      message = command;
    } else {
      // 如果命令不是對象，則使用舊的格式
      message = {
        type: 'command',
        command,
        timestamp: Date.now()
      };
    }

    ws.send(JSON.stringify(message));
    console.log(`已發送命令到網關 ${gatewayId}: ${JSON.stringify(message).substring(0, 100)}...`);

    return { success: true, message: `命令已發送到網關 ${gatewayId}` };
  } catch (error) {
    console.error(`發送命令到網關 ${gatewayId} 失敗:`, error);
    return { success: false, error: error.message };
  }
};

// 公開的 API
module.exports = {
  initDB,
  initWebSocketServer,
  updateGatewayStatus,
  updateDeviceStatus,
  setupHeartbeatCheck,
  getConnectedGateways: () => connectedGateways,
  getGatewayConnectionStats,
  isGatewayOnline,
  sendCommandToGateway,
  logGatewayEvent,
  logDeviceEvent
};
