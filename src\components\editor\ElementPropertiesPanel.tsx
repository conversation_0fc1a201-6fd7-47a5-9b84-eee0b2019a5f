import React, { useState, useEffect, useRef } from 'react';
import { TemplateElement, DisplayColorType } from '../../types';
import { CommonProperties } from './properties/CommonProperties';
import { TextProperties } from './properties/TextProperties';
import { LineProperties } from './properties/LineProperties';
import { IconProperties } from './properties/IconProperties'; // 引入圖標屬性面板
import { RectangleProperties } from './properties/RectangleProperties'; // 引入矩形屬性面板
import { CircleProperties } from './properties/CircleProperties'; // 引入圓形屬性面板
import { DefaultProperties } from './properties/DefaultProperties';
import { ImageProperties } from './properties/ImageProperties';
import { PropertySeparator } from './properties/FormComponents';
import {
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignCenterVertical as AlignVerticalCenter,
  AlignStartVertical as AlignTop,
  AlignEndVertical as AlignBottom,
  StretchHorizontal,
  StretchVertical
} from 'lucide-react';

// 新增對齊面板組件
interface MultiSelectionPanelProps {
  selectedElementIds: string[];
  alignSelectedElements: (alignType: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom') => void;
  distributeSelectedElements: (distributeType: 'horizontal' | 'vertical') => void;
}

const MultiSelectionPanel: React.FC<MultiSelectionPanelProps> = ({
  selectedElementIds,
  alignSelectedElements,
  distributeSelectedElements
}) => {
  return (
    <div className="p-4 bg-gray-800 text-white">
      <h3 className="text-sm font-medium mb-4">多選工具 ({selectedElementIds.length} 個已選擇)</h3>

      {/* 水平對齊工具 */}
      <div className="mb-4">
        <p className="text-xs text-gray-400 mb-2">水平對齊</p>
        <div className="flex gap-2">
          <button
            onClick={() => alignSelectedElements('left')}
            className="p-2 bg-gray-700 rounded hover:bg-gray-600"
            title="左對齊"
          >
            <AlignLeft size={16} />
          </button>
          <button
            onClick={() => alignSelectedElements('center')}
            className="p-2 bg-gray-700 rounded hover:bg-gray-600"
            title="水平居中"
          >
            <AlignCenter size={16} />
          </button>
          <button
            onClick={() => alignSelectedElements('right')}
            className="p-2 bg-gray-700 rounded hover:bg-gray-600"
            title="右對齊"
          >
            <AlignRight size={16} />
          </button>
          {selectedElementIds.length > 2 && (
            <button
              onClick={() => distributeSelectedElements('horizontal')}
              className="p-2 bg-gray-700 rounded hover:bg-gray-600"
              title="水平分散"
            >
              <StretchHorizontal size={16} />
            </button>
          )}
        </div>
      </div>

      {/* 垂直對齊工具 */}
      <div>
        <p className="text-xs text-gray-400 mb-2">垂直對齊</p>
        <div className="flex gap-2">
          <button
            onClick={() => alignSelectedElements('top')}
            className="p-2 bg-gray-700 rounded hover:bg-gray-600"
            title="頂部對齊"
          >
            <AlignTop size={16} />
          </button>
          <button
            onClick={() => alignSelectedElements('middle')}
            className="p-2 bg-gray-700 rounded hover:bg-gray-600"
            title="垂直居中"
          >
            <AlignVerticalCenter size={16} />
          </button>
          <button
            onClick={() => alignSelectedElements('bottom')}
            className="p-2 bg-gray-700 rounded hover:bg-gray-600"
            title="底部對齊"
          >
            <AlignBottom size={16} />
          </button>
          {selectedElementIds.length > 2 && (
            <button
              onClick={() => distributeSelectedElements('vertical')}
              className="p-2 bg-gray-700 rounded hover:bg-gray-600"
              title="垂直分散"
            >
              <StretchVertical size={16} />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

interface ElementPropertiesPanelProps {
  selectedElement: TemplateElement | null;
  elementProperties: {
    text: string;
    fontSize: number;
    fontFamily: string;
    lineWidth: number;
    lineColor: string;
  };
  setElementProperties: (properties: {
    text: string;
    fontSize: number;
    fontFamily: string;
    lineWidth: number;
    lineColor: string;
  }) => void;
  updateSelectedElement: (updates: Partial<TemplateElement>) => void;
  // 新增多選相關屬性
  selectedElementIds: string[];
  alignSelectedElements: (alignType: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom') => void;
  distributeSelectedElements: (distributeType: 'horizontal' | 'vertical') => void;
  // 新增：模板的顏色類型
  colorType?: string | DisplayColorType;
}

export const ElementPropertiesPanel: React.FC<ElementPropertiesPanelProps> = ({
  selectedElement,
  elementProperties,
  setElementProperties,
  updateSelectedElement,
  selectedElementIds,
  alignSelectedElements,
  distributeSelectedElements,
  colorType
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [maxHeight, setMaxHeight] = useState<string>('100%');

  // 添加狀態日誌，幫助追蹤面板的渲染過程
  useEffect(() => {
    console.log('ElementPropertiesPanel 渲染，選中元素數:', selectedElementIds.length);
  }, [selectedElementIds]);

  useEffect(() => {
    const updateMaxHeight = () => {
      if (containerRef.current) {
        // 獲取容器元素相對於視窗的位置
        const containerRect = containerRef.current.getBoundingClientRect();
        // 計算從容器頂部到視窗底部的高度，也就是剩餘的可用高度
        // 再額外減去 20px 作為底部間距
        const availableHeight = window.innerHeight - containerRect.top;

        setMaxHeight(`${availableHeight}px`);
      }
    };

    // 初始計算
    // 等待DOM渲染完成後再計算
    setTimeout(updateMaxHeight, 0);

    // 監聽視窗大小變化
    window.addEventListener('resize', updateMaxHeight);

    // 清理監聽器
    return () => {
      window.removeEventListener('resize', updateMaxHeight);
    };
  }, [selectedElement]); // 當選中元素變化時也重新計算高度

  // 多選模式 - 顯示對齊工具
  if (selectedElementIds.length > 1) {
    console.log('顯示多選對齊面板，選中元素數量:', selectedElementIds.length, '元素IDs:', selectedElementIds);
    return (
      <div
        ref={containerRef}
        className="h-full overflow-y-auto"
        style={{
          maxHeight: maxHeight,
          overflowY: 'auto',
          scrollbarWidth: 'thin',
          scrollbarColor: '#4B5563 #1F2937',
          paddingBottom: '40px'
        }}
      >
        <MultiSelectionPanel
          selectedElementIds={selectedElementIds}
          alignSelectedElements={alignSelectedElements}
          distributeSelectedElements={distributeSelectedElements}
        />
      </div>
    );
  }

  // 沒有選中元素時顯示提示
  if (!selectedElement) {
    return (
      <div className="p-4 bg-gray-800 text-white">
        <p className="text-sm text-gray-400">請選擇一個元素來編輯其屬性</p>
      </div>
    );
  }

  // 元素類型顯示文字
  const getElementTypeDisplay = (type: string) => {
    switch (type) {
      case 'line': return '線段';
      case 'rectangle': return '矩形';
      case 'square': return '正方形';
      case 'circle': return '圓形';
      case 'ellipse': return '橢圓';
      case 'text': return '文字';
      case 'multiline-text': return '多行文字';
      case 'image': return '圖片';
      case 'icon': return '圖標';
      default: return type;
    }
  };

  const elementTypeDisplay = getElementTypeDisplay(selectedElement.type);
  const isTextElement = selectedElement.type === 'text' || selectedElement.type === 'multiline-text';
  const isLineElement = selectedElement.type === 'line';
  const isIconElement = selectedElement.type === 'icon';
  const isImageElement = selectedElement.type === 'image';
  const isRectangleElement = selectedElement.type === 'rectangle' || selectedElement.type === 'square';
  const isCircleElement = selectedElement.type === 'circle' || selectedElement.type === 'ellipse';

  return (
    <div
      ref={containerRef}
      className="p-4 bg-gray-800 text-white h-full overflow-y-auto"
      style={{
        maxHeight: maxHeight,
        scrollbarWidth: 'thin',
        scrollbarColor: '#4B5563 #1F2937'
      }}
    >
      <h3 className="text-sm font-medium mb-4 px-3 py-2 rounded-md bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-400/30 backdrop-blur-sm">
        {elementTypeDisplay} 屬性
      </h3>

      {/* 通用屬性 */}
      <CommonProperties
        element={selectedElement}
        updateElement={updateSelectedElement}
      />

      {/* 根據元素類型顯示特定屬性 */}
      {isTextElement && (
        <>
          <PropertySeparator />
          <TextProperties
            element={selectedElement}
            updateElement={updateSelectedElement}
            colorType={colorType}
          />
        </>
      )}

      {isLineElement && (
        <>
          <PropertySeparator />
          <LineProperties
            element={selectedElement}
            updateElement={updateSelectedElement}
            colorType={colorType}
          />
        </>
      )}

      {/* 圖標屬性面板 */}
      {isIconElement && (
        <>
          <PropertySeparator />
          <IconProperties
            element={selectedElement}
            updateElement={updateSelectedElement}
            colorType={colorType}
          />
        </>
      )}

      {/* 矩形屬性面板 */}
      {isRectangleElement && (
        <>
          <PropertySeparator />
          <RectangleProperties
            element={selectedElement}
            updateElement={updateSelectedElement}
            colorType={colorType}
          />
        </>
      )}

      {/* 圓形屬性面板 */}
      {isCircleElement && (
        <>
          <PropertySeparator />
          <CircleProperties
            element={selectedElement}
            updateElement={updateSelectedElement}
            colorType={colorType}
          />
        </>
      )}

      {/* 圖片屬性面板 */}
      {isImageElement && (
        <>
          <PropertySeparator />
          <ImageProperties
            element={selectedElement}
            updateElement={updateSelectedElement}
          />
        </>
      )}

      {/* 預設屬性設置 */}
      <PropertySeparator />
      <DefaultProperties
        elementType={selectedElement.type}
        properties={elementProperties}
        setProperties={setElementProperties}
        colorType={colorType}
      />
    </div>
  );
};