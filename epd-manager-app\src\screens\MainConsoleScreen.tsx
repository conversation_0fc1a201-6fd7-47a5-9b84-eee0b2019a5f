// EPD Manager App - 主控制台頁面

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../stores/authStore';
import { useStores } from '../stores/storeStore';
import { useGateways } from '../stores/gatewayStore';
import { AutoPairingButton } from '../components/AutoPairingButton';
import { ConnectionStatus } from '../components/ConnectionStatus';
import { DeviceManagementScreen } from './DeviceManagementScreen';
import { ConnectionMonitorScreen } from './ConnectionMonitorScreen';
import { COLORS, SIZES, SUCCESS_MESSAGES } from '../utils/constants';

export const MainConsoleScreen: React.FC = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [showDeviceManagement, setShowDeviceManagement] = useState(false);
  const [showConnectionMonitor, setShowConnectionMonitor] = useState(false);
  const { user, logout } = useAuth();
  const { selectedStore, clearSelectedStore } = useStores();
  const {
    gateways,
    selectedGateway,
    fetchGateways,
    disconnectGateway,
    isConnected,
    connectionStatus
  } = useGateways();

  useEffect(() => {
    // 頁面加載時獲取網關列表
    if (selectedStore) {
      loadGateways();
    }
  }, [selectedStore]);

  const loadGateways = async () => {
    if (!selectedStore) return;
    
    try {
      await fetchGateways(selectedStore._id || selectedStore.id);
    } catch (error) {
      console.error('載入網關列表失敗:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadGateways();
    setRefreshing(false);
  };

  const handleLogout = () => {
    Alert.alert(
      '確認登出',
      '確定要登出嗎？',
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '登出',
          onPress: async () => {
            await logout();
          },
        },
      ]
    );
  };

  const handleDisconnect = () => {
    Alert.alert(
      '確認斷開',
      '確定要斷開當前網關連接嗎？',
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '斷開',
          onPress: () => {
            disconnectGateway();
            Alert.alert('提示', '網關連接已斷開');
          },
        },
      ]
    );
  };

  const handleChangeStore = () => {
    Alert.alert(
      '切換門店',
      '切換門店將斷開當前網關連接，確定要繼續嗎？',
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '確定',
          onPress: () => {
            if (isConnected) {
              disconnectGateway();
            }
            clearSelectedStore();
            // 這裡應該導航回門店選擇頁面
          },
        },
      ]
    );
  };

  const handleAutoPairingSuccess = () => {
    // 配對成功後刷新網關列表
    loadGateways();
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.PRIMARY]}
          />
        }
      >
        {/* 頭部信息 */}
        <View style={styles.header}>
          <View style={styles.userInfo}>
            <Text style={styles.welcomeText}>歡迎，{user?.username}</Text>
            <Text style={styles.storeText}>
              當前門店：{selectedStore?.name || '未選擇'}
            </Text>
          </View>
          
          <TouchableOpacity
            style={styles.logoutButton}
            onPress={handleLogout}
          >
            <Text style={styles.logoutButtonText}>登出</Text>
          </TouchableOpacity>
        </View>

        {/* 門店信息卡片 */}
        {selectedStore && (
          <View style={styles.storeCard}>
            <View style={styles.storeCardHeader}>
              <Text style={styles.storeCardTitle}>門店信息</Text>
              <TouchableOpacity
                style={styles.changeStoreButton}
                onPress={handleChangeStore}
              >
                <Text style={styles.changeStoreButtonText}>切換</Text>
              </TouchableOpacity>
            </View>
            
            <Text style={styles.storeName}>{selectedStore.name}</Text>
            <Text style={styles.storeAddress}>{selectedStore.address}</Text>
            
            <View style={styles.storeStats}>
              <Text style={styles.storeStatsText}>
                網關數量: {gateways.length}
              </Text>
            </View>
          </View>
        )}

        {/* 連接狀態 */}
        <ConnectionStatus 
          showDetails={true}
          style={styles.connectionStatus}
        />

        {/* 一鍵配對按鈕 */}
        <AutoPairingButton
          onSuccess={handleAutoPairingSuccess}
          style={styles.autoPairingButton}
        />

        {/* 快速操作 */}
        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>快速操作</Text>
          <Text style={styles.noteText}>
            💡 設備管理和連接監控功能可獨立使用，無需先進行配對
          </Text>
          
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => setShowDeviceManagement(true)}
            >
              <Text style={styles.actionButtonIcon}>📱</Text>
              <Text style={styles.actionButtonText}>
                設備管理
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => setShowConnectionMonitor(true)}
            >
              <Text style={styles.actionButtonIcon}>📊</Text>
              <Text style={styles.actionButtonText}>
                連接監控
              </Text>
            </TouchableOpacity>

            {isConnected && (
              <TouchableOpacity
                style={[styles.actionButton, styles.disconnectButton]}
                onPress={handleDisconnect}
              >
                <Text style={styles.actionButtonIcon}>🔌</Text>
                <Text style={[styles.actionButtonText, styles.disconnectButtonText]}>
                  斷開連接
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* 網關列表 */}
        {gateways.length > 0 && (
          <View style={styles.gatewayList}>
            <Text style={styles.sectionTitle}>網關列表</Text>
            
            {gateways.map((gateway, index) => (
              <View key={gateway._id || index} style={styles.gatewayCard}>
                <View style={styles.gatewayCardHeader}>
                  <Text style={styles.gatewayName}>{gateway.name}</Text>
                  <View style={[
                    styles.gatewayStatusBadge,
                    gateway.status === 'online' 
                      ? styles.gatewayStatusOnline 
                      : styles.gatewayStatusOffline
                  ]}>
                    <Text style={styles.gatewayStatusText}>
                      {gateway.status === 'online' ? '在線' : '離線'}
                    </Text>
                  </View>
                </View>
                
                <Text style={styles.gatewayMac}>MAC: {gateway.macAddress}</Text>
                <Text style={styles.gatewayModel}>型號: {gateway.model}</Text>
                
                {selectedGateway?._id === gateway._id && (
                  <View style={styles.currentGatewayBadge}>
                    <Text style={styles.currentGatewayText}>當前連接</Text>
                  </View>
                )}
              </View>
            ))}
          </View>
        )}

        {/* 底部間距 */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* 設備管理 Modal */}
      <Modal
        visible={showDeviceManagement}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => setShowDeviceManagement(false)}
      >
        <View style={styles.modalHeader}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => setShowDeviceManagement(false)}
          >
            <Text style={styles.backButtonText}>← 返回</Text>
          </TouchableOpacity>
        </View>
        <DeviceManagementScreen />
      </Modal>

      {/* 連接監控 Modal */}
      <Modal
        visible={showConnectionMonitor}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={() => setShowConnectionMonitor(false)}
      >
        <View style={styles.modalHeader}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => setShowConnectionMonitor(false)}
          >
            <Text style={styles.backButtonText}>← 返回</Text>
          </TouchableOpacity>
        </View>
        <ConnectionMonitorScreen />
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SIZES.SPACING_MD,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_LG,
  },
  userInfo: {
    flex: 1,
  },
  welcomeText: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  storeText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: 2,
  },
  logoutButton: {
    backgroundColor: COLORS.ERROR,
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  logoutButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '600',
  },
  storeCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    marginBottom: SIZES.SPACING_MD,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
  },
  storeCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_SM,
  },
  storeCardTitle: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  changeStoreButton: {
    backgroundColor: COLORS.INFO,
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  changeStoreButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  storeName: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_XS,
  },
  storeAddress: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SIZES.SPACING_SM,
  },
  storeStats: {
    paddingTop: SIZES.SPACING_SM,
    borderTopWidth: 1,
    borderTopColor: COLORS.TEXT_DISABLED,
  },
  storeStatsText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
  },
  connectionStatus: {
    marginBottom: SIZES.SPACING_MD,
  },
  autoPairingButton: {
    marginBottom: SIZES.SPACING_LG,
  },
  quickActions: {
    marginBottom: SIZES.SPACING_LG,
  },
  sectionTitle: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_MD,
  },
  noteText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.INFO,
    marginBottom: SIZES.SPACING_MD,
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_SM,
    backgroundColor: `${COLORS.INFO}15`,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.INFO,
  },
  actionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    alignItems: 'center',
    width: '48%',
    marginBottom: SIZES.SPACING_SM,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY,
  },
  actionButtonDisabled: {
    backgroundColor: COLORS.TEXT_DISABLED,
    borderColor: COLORS.TEXT_DISABLED,
  },
  disconnectButton: {
    borderColor: COLORS.ERROR,
    width: '100%',
  },
  actionButtonIcon: {
    fontSize: SIZES.FONT_SIZE_XL,
    marginBottom: SIZES.SPACING_XS,
  },
  actionButtonText: {
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '600',
    color: COLORS.PRIMARY,
    textAlign: 'center',
  },
  actionButtonTextDisabled: {
    color: COLORS.TEXT_SECONDARY,
  },
  disconnectButtonText: {
    color: COLORS.ERROR,
  },
  gatewayList: {
    marginBottom: SIZES.SPACING_LG,
  },
  gatewayCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    marginBottom: SIZES.SPACING_SM,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
  },
  gatewayCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_SM,
  },
  gatewayName: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    flex: 1,
  },
  gatewayStatusBadge: {
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  gatewayStatusOnline: {
    backgroundColor: COLORS.SUCCESS,
  },
  gatewayStatusOffline: {
    backgroundColor: COLORS.TEXT_DISABLED,
  },
  gatewayStatusText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  gatewayMac: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    fontFamily: 'monospace',
    marginBottom: 2,
  },
  gatewayModel: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
  },
  currentGatewayBadge: {
    backgroundColor: COLORS.INFO,
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    alignSelf: 'flex-start',
    marginTop: SIZES.SPACING_SM,
  },
  currentGatewayText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  bottomSpacing: {
    height: SIZES.SPACING_XL,
  },
  modalHeader: {
    backgroundColor: COLORS.SURFACE,
    paddingTop: 50, // 為狀態欄留出空間
    paddingHorizontal: SIZES.SPACING_MD,
    paddingBottom: SIZES.SPACING_SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.TEXT_DISABLED,
  },
  backButton: {
    paddingVertical: SIZES.SPACING_SM,
  },
  backButtonText: {
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.PRIMARY,
    fontWeight: '600',
  },
});

export default MainConsoleScreen;
