import React, { useEffect, useState } from 'react';
import { Template, TemplateElement } from '../../types';
import { ElementRenderer } from './elements/ElementRenderer';
import { ZoomIn, ZoomOut, Download, Save, Eye } from 'lucide-react';
import { calculateElementBounds, downloadCanvasAsImage, renderCanvasToImage } from './canvasUtils';
import { getScreenConfigFromDimensions, parseScreenSizeString } from '../../utils/screenUtils';
import { PreviewModal } from './PreviewModal';
import { saveTemplateToServer } from '../../utils/api/templateApi';
import { processTextBindings, restoreTextBindings } from '../../utils/previewUtils';
// import { v4 as uuidv4 } from 'uuid'; // 不再需要，使用從 props 傳入的函數

interface CanvasProps {
  canvasRef: React.RefObject<HTMLDivElement>;
  selectedTemplate: Template;
  zoom: number;
  setZoom: (zoom: number) => void;
  templateName: string;
  setTemplateName: (name: string) => void;
  localElements: TemplateElement[];
  setLocalElements: (elements: TemplateElement[]) => void;
  selectedElementId: string | null;
  setSelectedElementId: (id: string | null) => void;
  selectedElementIds: string[];
  setSelectedElementIds: (ids: string[]) => void;
  isMultiSelectMode: boolean;
  setIsMultiSelectMode: (isMultiSelectMode: boolean) => void;
  selectionBox: { startX: number; startY: number; endX: number; endY: number } | null;
  setSelectionBox: (box: { startX: number; startY: number; endX: number; endY: number } | null) => void;
  selectElementsByBox: (box: { startX: number; startY: number; endX: number; endY: number }) => void;
  toggleElementSelection: (id: string) => void;
  clearSelection: () => void;
  moveSelectedElements: (dx: number, dy: number) => void;
  isMultiMoving: boolean;
  isDrawing: boolean;
  setIsDrawing: (isDrawing: boolean) => void;
  startPoint: { x: number; y: number };
  setStartPoint: (point: { x: number; y: number }) => void;
  selectedTool: string | null;
  setSelectedTool: (tool: string | null) => void;
  handleCanvasMouseDown: (e: React.MouseEvent<HTMLDivElement>) => void;
  handleCanvasMouseMove: (e: React.MouseEvent<HTMLDivElement>) => void;
  handleCanvasMouseUp: (e: React.MouseEvent<HTMLDivElement>) => void;
  updateTemplate: (template: Template) => void;
  updateElement: (id: string, updates: Partial<TemplateElement>) => void;
  tempElement: TemplateElement | null;
  handleCopy: () => void;
  handlePaste: () => void;
}

export const Canvas: React.FC<CanvasProps> = ({
  canvasRef,
  selectedTemplate,
  zoom,
  setZoom,
  templateName,
  setTemplateName,
  localElements,
  setLocalElements,
  selectedElementId,
  setSelectedElementId,
  selectedElementIds,
  setSelectedElementIds,
  isMultiSelectMode,
  setIsMultiSelectMode,
  selectionBox,
  setSelectionBox,
  selectElementsByBox,
  toggleElementSelection,
  clearSelection,
  moveSelectedElements,
  isMultiMoving,
  isDrawing,
  setIsDrawing,
  startPoint,
  setStartPoint,
  selectedTool,
  setSelectedTool,
  handleCanvasMouseDown,
  handleCanvasMouseMove,
  handleCanvasMouseUp,
  updateTemplate,
  updateElement,
  tempElement,
  handleCopy,
  handlePaste
}) => {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [previewCanvas, setPreviewCanvas] = useState<HTMLCanvasElement | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [lastSavedState, setLastSavedState] = useState<Template | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  // 不再需要本地複製貼上狀態，使用從 props 傳入的函數

  useEffect(() => {
    const canvasElement = canvasRef.current;
    if (!canvasElement) return;

    const handleWheel = (e: WheelEvent) => {
      if (e.ctrlKey) {
        e.preventDefault();
        const newZoom = e.deltaY < 0 ? Math.min(500, zoom + 10) : Math.max(50, zoom - 10);
        setZoom(newZoom);
      }
    };

    canvasElement.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      canvasElement.removeEventListener('wheel', handleWheel);
    };
  }, [zoom, setZoom, canvasRef]);
  useEffect(() => {
    if (selectedTemplate) {
      setTemplateName(selectedTemplate.name);
      setLocalElements(selectedTemplate.elements || []);
      setLastSavedState(selectedTemplate);
      setHasUnsavedChanges(false);
    }
  }, [selectedTemplate]);

  // 使用從 props 傳入的複製貼上函數

  // 添加鍵盤事件監聽器處理複製/貼上操作
  useEffect(() => {
    console.log('Canvas: 設置鍵盤事件監聽器');

    // 檢查 handleCopy 和 handlePaste 是否存在
    console.log('Canvas: handleCopy 是否存在:', typeof handleCopy === 'function');
    console.log('Canvas: handlePaste 是否存在:', typeof handlePaste === 'function');

    const handleKeyDown = (e: KeyboardEvent) => {
      // 複製操作 (Ctrl+C)
      // 使用 e.key.toLowerCase() 來忽略 Caps Lock 狀態
      if (e.ctrlKey && (e.key.toLowerCase() === 'c')) {
        console.log('Canvas: 檢測到 Ctrl+C 按下，執行複製操作');
        console.log('Canvas: 按鍵代碼:', e.keyCode, '按鍵名稱:', e.key);
        e.preventDefault(); // 防止預設行為
        if (typeof handleCopy === 'function') {
          handleCopy();
        } else {
          console.error('Canvas: handleCopy 不是一個函數!');
        }
      }
      // 貼上操作 (Ctrl+V)
      else if (e.ctrlKey && (e.key.toLowerCase() === 'v')) {
        console.log('Canvas: 檢測到 Ctrl+V 按下，執行貼上操作');
        console.log('Canvas: 按鍵代碼:', e.keyCode, '按鍵名稱:', e.key);
        e.preventDefault(); // 防止預設行為
        if (typeof handlePaste === 'function') {
          handlePaste();
        } else {
          console.error('Canvas: handlePaste 不是一個函數!');
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      console.log('Canvas: 移除鍵盤事件監聽器');
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleCopy, handlePaste]);

  useEffect(() => {
    if (selectedTemplate && selectedTemplate.elements) {
      setHasUnsavedChanges(true);
    }
  }, [localElements, selectedTemplate]);

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      if (window.confirm('您有未保存的更改，確定要取消嗎？')) {
        restoreOriginalTemplate();
      }
    } else {
      if (updateTemplate) {
        const emptyTemplate = {
          ...selectedTemplate
        };
        updateTemplate(emptyTemplate);
      }
    }
  };

  const restoreOriginalTemplate = () => {
    if (lastSavedState) {
      setTemplateName(lastSavedState.name);
      setLocalElements(lastSavedState.elements || []);
    } else {
      setTemplateName(selectedTemplate.name);
      setLocalElements(selectedTemplate.elements || []);
    }
    setSelectedElementId(null);
    setSelectedElementIds([]);
    setIsDrawing(false);
    setSelectedTool(null);
    setHasUnsavedChanges(false);
  };

  const handleZoomIn = () => {
    const newZoom = Math.min(zoom + 10, 500);
    setZoom(newZoom);
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(zoom - 10, 50);
    setZoom(newZoom);
  };

  // 重置縮放函數
  const handleResetZoom = () => {
    setZoom(100);
  };

  const getScreenDimensions = () => {
    const parsedSize = parseScreenSizeString(selectedTemplate.screenSize);

    if (parsedSize.includes('x')) {
      const [width, height] = parsedSize.split('x').map(Number);
      return { width, height };
    }

    const screenConfig = getScreenConfigFromDimensions(selectedTemplate.screenSize);
    if (screenConfig) {
      return { width: screenConfig.width, height: screenConfig.height };
    }

    return { width: 250, height: 122 };
  };

  const { width: screenWidth, height: screenHeight } = getScreenDimensions();

  // 保存模板函數 - 目前未使用，使用 handleSaveToServer 代替
  const handleSaveTemplate = () => {
    if (selectedTemplate) {
      updateTemplate({
        ...selectedTemplate,
        name: templateName,
        elements: localElements
      });
      setHasUnsavedChanges(false);
    }
  };

  const handleSaveToServer = async () => {
    try {
      if (!selectedTemplate) return;

      let templateToSave = {
        ...selectedTemplate,
        name: templateName,
        elements: localElements
      };

      console.log('準備保存模板，原始ID:', templateToSave.id);

      if (!templateToSave.id || templateToSave.id === '') {
        templateToSave.id = 'template_' + new Date().getTime();
        console.log('生成新ID:', templateToSave.id);
      }

      console.log('開始保存模板到服務器，模板數據:', JSON.stringify({
        id: templateToSave.id,
        name: templateToSave.name,
        elementsCount: templateToSave.elements.length
      }));

      const currentSelectedElementId = selectedElementId;
      const currentSelectedElementIds = [...selectedElementIds];

      setSelectedElementId(null);
      setSelectedElementIds([]);

      setTimeout(async () => {
        const result = await saveTemplateToServer(templateToSave);
        console.log('保存模板API返回結果:', result);

        if (result.success) {
          const updatedTemplate = result.result?.template || templateToSave;

          if (updatedTemplate.previewImage) {
            console.log('模板包含預覽圖，更新全局模板狀態');

            updateTemplate(updatedTemplate);

            setLocalElements(updatedTemplate.elements || []);
          } else {
            updateTemplate(templateToSave);
          }

          setLastSavedState(updatedTemplate);
          alert('模板已成功保存到服務器');
          setHasUnsavedChanges(false);
        } else {
          throw result.error;
        }

        setSelectedElementId(currentSelectedElementId);
        setSelectedElementIds(currentSelectedElementIds);
      }, 10);
    } catch (error) {
      console.error('保存模板到服務器失敗:', error);
      alert('保存模板到服務器失敗');
    }
  };

  const handleCanvasClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === canvasRef.current) {
      setSelectedElementId(null);
      clearSelection();
    }
  };

  const handleMultiSelectMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    const clickedOnElement = (e.target as HTMLElement).closest('[data-element-id]');

    if (selectedTool) {
      return;
    }

    if (e.shiftKey && clickedOnElement) {
      const elementId = clickedOnElement.getAttribute('data-element-id');
      if (elementId) {
        e.stopPropagation();
        toggleElementSelection(elementId);
        return;
      }
    }

    if (clickedOnElement && !e.shiftKey) {
      return;
    }

    if (!canvasRef.current) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const x = (e.clientX - rect.left) / (zoom / 100);
    const y = (e.clientY - rect.top) / (zoom / 100);

    setIsDrawing(true);
    setIsMultiSelectMode(true);
    setStartPoint({ x, y });
    setSelectionBox({
      startX: x,
      startY: y,
      endX: x,
      endY: y
    });
  };

  const handleMultiSelectMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDrawing || !isMultiSelectMode || !selectionBox || !canvasRef.current) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const x = (e.clientX - rect.left) / (zoom / 100);
    const y = (e.clientY - rect.top) / (zoom / 100);

    setSelectionBox({
      startX: selectionBox.startX,
      startY: selectionBox.startY,
      endX: x,
      endY: y
    });
  };

  const handleMultiSelectMouseUp = (_e: React.MouseEvent<HTMLDivElement>) => {
    if (!isMultiSelectMode || !isDrawing || !selectionBox || !canvasRef.current) return;

    setIsDrawing(false);

    if (selectionBox.startX !== selectionBox.endX || selectionBox.startY !== selectionBox.endY) {
      selectElementsByBox(selectionBox);

      setTimeout(() => {
        console.log('選擇後的元素IDs (Canvas):', selectedElementIds);
      }, 10);
    }

    setSelectionBox(null);
  };

  const handlePreview = async () => {
    if (!canvasRef.current) return;

    try {
      const currentSelectedElementId = selectedElementId;
      const currentSelectedElementIds = [...selectedElementIds];

      // 先清除選中狀態
      setSelectedElementId(null);
      setSelectedElementIds([]);

      setTimeout(async () => {
        // 處理綁定數據的文字元件
        console.log('開始處理綁定文字元素...');
        const originalStyles = await processTextBindings();

        // 確保所有元素都有正確的 data-element-type 屬性
        if (canvasRef.current) {
          const elements = canvasRef.current.querySelectorAll('[data-element-id]');
          elements.forEach((el) => {
            const elementId = el.getAttribute('data-element-id');
            if (elementId) {
              const element = localElements.find(e => e.id === elementId);
              if (element) {
                el.setAttribute('data-element-type', element.type);
              }
            }
          });
        }

        // 等待一下，確保 DOM 更新
        setTimeout(async () => {
          // 渲染畫布為圖片
          const renderedCanvas = await renderCanvasToImage(canvasRef, screenWidth, screenHeight);
          setPreviewCanvas(renderedCanvas);

          // 顯示預覽模態框
          setIsPreviewOpen(true);

          // 恢復選中狀態
          setSelectedElementId(currentSelectedElementId);
          setSelectedElementIds(currentSelectedElementIds);

          // 重新渲染畫面，恢復綁定標記
          setTimeout(() => {
            // 恢復綁定標記的原始樣式
            restoreTextBindings(originalStyles);

            // 強制重新渲染
            const canvasElement = canvasRef.current;
            if (canvasElement) {
              const display = canvasElement.style.display;
              canvasElement.style.display = 'none';
              setTimeout(() => {
                if (canvasElement) {
                  canvasElement.style.display = display;
                }
              }, 0);
            }
          }, 100);
        }, 50);
      }, 10);
    } catch (error) {
      console.error('預覽圖片生成失敗:', error);
      alert('預覽圖片生成失敗');
    }
  };

  return (
    <div className="flex-1 bg-gray-100">
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center gap-4">
          <div className="flex-1 flex items-center gap-4">
            <input
              type="text"
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
              className="border border-gray-300 rounded px-3 py-1.5 w-48"
              placeholder="模板名稱"
            />
            <div className="flex items-center gap-2 text-gray-600">
              <button className="p-1 hover:bg-gray-100 rounded" onClick={handleZoomOut} title="縮小">
                <ZoomOut size={20} />
              </button>
              <span className="w-16 text-center">{zoom}%</span>
              <button className="p-1 hover:bg-gray-100 rounded" onClick={handleZoomIn} title="放大">
                <ZoomIn size={20} />
              </button>
            </div>
          </div>
          <div className="flex gap-2">
            <button
              className="px-6 py-1.5 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              onClick={handleCancel}
            >
              取消
            </button>
            <button
              className="px-6 py-1.5 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              onClick={handlePreview}
              title="預覽"
            >
              <Eye size={18} className="inline-block mr-1" />
              預覽
            </button>
            <button
              className="px-6 py-1.5 bg-purple-500 text-white rounded-md hover:bg-purple-600"
              onClick={handleSaveToServer}
              title="保存"
            >
              <Save size={18} className="inline-block mr-1" />
              保存
            </button>
          </div>
        </div>
      </div>

      <div className="flex justify-center items-start h-[calc(100vh-6rem)] overflow-hide px-[2px] pt-[2px] pb-[50px]">
        <div
          className="bg-gray-300 shadow-lg rounded p-8 overflow-auto w-full"
          style={{
            minHeight: 'calc(100% - 0rem)',
            marginBottom: '0rem',
            display: 'flex',
            justifyContent: 'flex-start',
            alignItems: 'flex-start'
          }}
        >
          <div
            ref={canvasRef}
            style={{
              transform: `scale(${zoom / 100})`,
              transformOrigin: 'top left',
              width: `${screenWidth}px`,
              height: `${screenHeight}px`,
              position: 'relative',
              background: selectedTemplate.color === 'bwr' ? 'white' : '#f0f0f0',
              border: '0px solid #ddd',
              boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
              transition: 'transform 0.2s ease',
              cursor: selectedTool ? 'crosshair' : 'default',
              margin: '1rem'
            }}
            onClick={handleCanvasClick}
            onMouseDown={selectedTool ? handleCanvasMouseDown : handleMultiSelectMouseDown}
            onMouseMove={selectedTool ? handleCanvasMouseMove : handleMultiSelectMouseMove}
            onMouseUp={selectedTool ? handleCanvasMouseUp : handleMultiSelectMouseUp}
            data-canvas-width={screenWidth}
            data-canvas-height={screenHeight}
          >
            {localElements.map((element) => (
              <ElementRenderer
                key={element.id}
                element={element}
                isSelected={selectedElementId === element.id || selectedElementIds.includes(element.id)}
                onSelect={(id, e) => {
                  if (e && e.shiftKey && selectedElementIds.length > 0) {
                    toggleElementSelection(id);
                  } else {
                    setSelectedElementId(id);
                    setSelectedElementIds([id]);
                  }
                }}
                onUpdate={updateElement}
                zoom={zoom}
                setSelectedTool={setSelectedTool}
                selectedElementIds={selectedElementIds}
                moveSelectedElements={moveSelectedElements}
                isMultiMoving={isMultiMoving}
              />
            ))}

            {selectionBox && (
              <div
                style={{
                  position: 'absolute',
                  left: Math.min(selectionBox.startX, selectionBox.endX),
                  top: Math.min(selectionBox.startY, selectionBox.endY),
                  width: Math.abs(selectionBox.endX - selectionBox.startX),
                  height: Math.abs(selectionBox.endY - selectionBox.startY),
                  border: '1px dashed #3b82f6',
                  backgroundColor: 'rgba(59, 130, 246, 0.1)',
                  pointerEvents: 'none'
                }}
              />
            )}

            {selectedElementIds.length > 1 && (
              <div
                style={{
                  position: 'absolute',
                  left: (() => {
                    const selectedElements = localElements.filter((el) => selectedElementIds.includes(el.id));
                    if (selectedElements.length === 0) return 0;

                    let minX = Infinity;
                    for (const el of selectedElements) {
                      const bounds = calculateElementBounds(el);
                      minX = Math.min(minX, bounds.minX);
                    }
                    return minX - 5;
                  })(),
                  top: (() => {
                    const selectedElements = localElements.filter((el) => selectedElementIds.includes(el.id));
                    if (selectedElements.length === 0) return 0;

                    let minY = Infinity;
                    for (const el of selectedElements) {
                      const bounds = calculateElementBounds(el);
                      minY = Math.min(minY, bounds.minY);
                    }
                    return minY - 5;
                  })(),
                  width: (() => {
                    const selectedElements = localElements.filter((el) => selectedElementIds.includes(el.id));
                    if (selectedElements.length === 0) return 0;

                    let minX = Infinity,
                      maxX = -Infinity;
                    for (const el of selectedElements) {
                      const bounds = calculateElementBounds(el);
                      minX = Math.min(minX, bounds.minX);
                      maxX = Math.max(maxX, bounds.maxX);
                    }
                    return maxX - minX + 10;
                  })(),
                  height: (() => {
                    const selectedElements = localElements.filter((el) => selectedElementIds.includes(el.id));
                    if (selectedElements.length === 0) return 0;

                    let minY = Infinity,
                      maxY = -Infinity;
                    for (const el of selectedElements) {
                      const bounds = calculateElementBounds(el);
                      minY = Math.min(minY, bounds.minY);
                      maxY = Math.max(maxY, bounds.maxY);
                    }
                    return maxY - minY + 10;
                  })(),
                  border: '2px dashed #3b82f6',
                  backgroundColor: 'rgba(59, 130, 246, 0.1)',
                  cursor: 'move',
                  zIndex: 900
                }}
                onMouseDown={(e) => {
                  e.stopPropagation();
                  if (selectedElementIds.length > 1) {
                    setIsDragging(true);
                    setStartPoint({
                      x: e.clientX / (zoom / 100),
                      y: e.clientY / (zoom / 100)
                    });
                  }
                }}
                onMouseMove={(e) => {
                  if (isDragging && selectedElementIds.length > 1) {
                    const currentX = e.clientX / (zoom / 100);
                    const currentY = e.clientY / (zoom / 100);
                    const deltaX = currentX - startPoint.x;
                    const deltaY = currentY - startPoint.y;

                    moveSelectedElements(deltaX, deltaY);
                    setStartPoint({ x: currentX, y: currentY });
                  }
                }}
                onMouseUp={() => {
                  if (isDragging) {
                    setIsDragging(false);
                  }
                }}
              />
            )}            {tempElement && (
              <ElementRenderer
                element={tempElement}
                isSelected={true}
                onSelect={() => {}}
                onUpdate={() => {}}
                zoom={zoom}
                setSelectedTool={setSelectedTool}
                selectedElementIds={[]}
                moveSelectedElements={() => {}}
                isMultiMoving={false}
              />
            )}
          </div>
        </div>
      </div>      {isPreviewOpen && (
        <PreviewModal
          isOpen={isPreviewOpen}
          onClose={() => setIsPreviewOpen(false)}
          canvasData={previewCanvas}
          width={screenWidth}
          height={screenHeight}
          elements={localElements} // 傳遞元素資訊，以便處理資料欄位綁定
          template={selectedTemplate} // 傳遞模板信息，以便使用 colorType
        />
      )}
    </div>
  );
};