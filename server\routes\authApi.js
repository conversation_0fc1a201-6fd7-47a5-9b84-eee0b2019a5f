const express = require('express');
const router = express.Router();
const User = require('../models/User');
const { generateToken, hashPassword, comparePassword } = require('../utils/auth');
const { authenticate } = require('../middleware/auth');

// 數據庫連接函數引用
let connectDBFunction;

// 初始化數據庫連接
function initDB(connectDB) {
  connectDBFunction = connectDB;
  return connectDBFunction;
}

/**
 * 用戶登入
 * POST /api/auth/login
 */
router.post('/auth/login', async (req, res) => {
  try {
    const { username, password, rememberMe } = req.body;

    console.log('收到登入請求:', { username, rememberMe });

    if (!username || !password) {
      return res.status(400).json({ error: '用戶名和密碼不能為空' });
    }

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 查找用戶
    const user = await User.findByUsername(db, username);

    if (!user) {
      return res.status(401).json({ error: '用戶名或密碼錯誤' });
    }

    // 檢查用戶狀態
    if (user.status !== 'active') {
      return res.status(403).json({ error: '用戶已被禁用，請聯繫管理員' });
    }

    // 驗證密碼
    const isPasswordValid = await comparePassword(password, user.password);

    if (!isPasswordValid) {
      return res.status(401).json({ error: '用戶名或密碼錯誤' });
    }

    // 生成 token
    const token = generateToken({ userId: user._id.toString() });
    console.log('生成 token 成功');

    // 設置 cookie
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    };

    // 如果選擇記住我，設置較長的過期時間
    if (rememberMe) {
      cookieOptions.maxAge = 30 * 24 * 60 * 60 * 1000; // 30 天
    }

    res.cookie('token', token, cookieOptions);
    console.log('設置 cookie 成功');

    // 返回用戶信息（不包含密碼）
    const { password: _, ...userWithoutPassword } = user;

    console.log('登入成功，返回用戶信息');
    res.json({
      message: '登入成功',
      user: userWithoutPassword,
      token
    });
  } catch (error) {
    console.error('登入錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 用戶登出
 * POST /api/auth/logout
 */
router.post('/auth/logout', (req, res) => {
  // 清除 cookie
  res.clearCookie('token');

  res.json({ message: '登出成功' });
});

/**
 * 檢查登入狀態
 * GET /api/auth/check
 */
router.get('/auth/check', authenticate, async (req, res) => {
  try {
    // 用戶信息已在認證中間件中添加到請求對象
    const { password: _, ...userWithoutPassword } = req.user;

    res.json({
      isAuthenticated: true,
      user: userWithoutPassword
    });
  } catch (error) {
    console.error('檢查登入狀態錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 修改密碼
 * PUT /api/auth/password
 */
router.put('/auth/password', authenticate, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: '當前密碼和新密碼不能為空' });
    }

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 驗證當前密碼
    const isPasswordValid = await comparePassword(currentPassword, req.user.password);

    if (!isPasswordValid) {
      return res.status(401).json({ error: '當前密碼錯誤' });
    }

    // 加密新密碼
    const hashedPassword = await hashPassword(newPassword);

    // 更新密碼
    await User.updateUser(db, req.user._id.toString(), { password: hashedPassword });

    res.json({ message: '密碼修改成功' });
  } catch (error) {
    console.error('修改密碼錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 系統初始化檢查
 * GET /api/auth/init-check
 */
router.get('/auth/init-check', async (req, res) => {
  try {
    console.log('收到系統初始化檢查請求');

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 檢查是否有任何用戶
    const users = await User.findAll(db, {}, { limit: 1 });

    res.json({
      initialized: users.length > 0
    });
  } catch (error) {
    console.error('系統初始化檢查錯誤:', error);
    res.status(500).json({ error: '伺服器錯誤' });
  }
});

/**
 * 系統初始化（創建管理員）
 * POST /api/auth/initialize
 */
router.post('/auth/initialize', async (req, res) => {
  try {
    const { username, password, confirmPassword, name, email, phone } = req.body;

    console.log('收到系統初始化請求:', { username });

    if (!username || !password || !confirmPassword) {
      return res.status(400).json({ error: '所有字段都是必填的' });
    }

    if (password !== confirmPassword) {
      return res.status(400).json({ error: '兩次輸入的密碼不一致' });
    }

    // 獲取數據庫連接
    const { db } = await connectDBFunction();

    // 檢查是否已經初始化
    const users = await User.findAll(db, {}, { limit: 1 });

    if (users.length > 0) {
      return res.status(400).json({ error: '系統已經初始化' });
    }

    // 加密密碼
    const hashedPassword = await hashPassword(password);

    // 創建管理員用戶
    const adminUser = await User.createUser(db, {
      username,
      password: hashedPassword,
      name: name || '',
      email: email || '',
      phone: phone || '',
      status: 'active'
    });

    // 創建管理員角色
    const Role = require('../models/Role');
    const adminRole = await Role.createRole(db, {
      name: '系統管理員',
      description: '擁有所有權限的系統管理員角色',
      type: 'system',
      permissions: ['all'] // 所有權限
    });

    // 分配權限
    const Permission = require('../models/Permission');
    await Permission.createPermission(db, {
      userId: adminUser._id.toString(),
      roleId: adminRole._id.toString(),
      scope: 'system',
      scopeType: 'system'
    });

    console.log('系統初始化成功');
    res.json({
      message: '系統初始化成功',
      user: {
        username: adminUser.username,
        name: adminUser.name,
        email: adminUser.email,
        phone: adminUser.phone
      }
    });
  } catch (error) {
    console.error('系統初始化錯誤:', error);

    // 根據錯誤類型返回不同的錯誤訊息
    if (error.message === '角色名稱已存在') {
      return res.status(400).json({
        error: '初始化失敗：系統中已存在「系統管理員」角色。請先清空資料庫中的 roles 集合後再試。'
      });
    } else if (error.message === '用戶名已存在') {
      return res.status(400).json({
        error: '初始化失敗：該用戶名已被使用。請嘗試使用其他用戶名。'
      });
    } else if (error.message === '用戶已有相同範圍的權限分配') {
      return res.status(400).json({
        error: '初始化失敗：用戶已有系統範圍的權限分配。請先清空資料庫中的 permissions 集合後再試。'
      });
    }

    // 其他未知錯誤
    res.status(500).json({ error: '伺服器錯誤：' + error.message });
  }
});

module.exports = { router, initDB };
