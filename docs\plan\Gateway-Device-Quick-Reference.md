# Gateway/Device 快速參考手冊

## 🔄 操作流程

```mermaid
flowchart TD
    A[Server Web 新增 Gateway]
    B[點擊鑰匙圖標]
    C[複製 Config 資訊]
    D[貼到 Gateway 設備]
    E[建立 WebSocket 連接]
    F[收到 welcome]
    G[發送 gatewayInfo]
    H[開始 deviceStatus]

    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H

    style A fill:#e1f5fe
    style F fill:#e8f5e8
    style H fill:#e8f5e8
```

## 🚀 快速開始

### 1. 連接流程
```
1. 用戶在 Server Web 新增 Gateway
2. 點擊鑰匙圖標獲取 Config 資訊
3. 將 Config 貼到 Gateway 設備
4. Gateway 建立 WebSocket 連接: ws://server:port/ws?token=JWT_TOKEN
5. 收到 welcome 消息後發送 gatewayInfo
6. 開始定期發送 ping (25秒) 和 deviceStatus (5秒)
```

### 2. 必要的消息類型
- **發送**: `ping`, `gatewayInfo`, `deviceStatus`
- **接收**: `welcome`, `pong`, `gatewayInfoAck`, `deviceStatusAck`, `update_preview`

## 📨 消息格式速查

### 發送消息 (Gateway → Server)

#### ping (心跳)
```json
{
  "type": "ping",
  "timestamp": 1640995200000
}
```
**頻率**: 每 25 秒

#### gatewayInfo (網關信息)
```json
{
  "type": "gatewayInfo",
  "info": {
    "macAddress": "AA:BB:CC:DD:EE:FF",  // 必須與 Token 中的 MAC 一致
    "model": "Gateway Model 003",
    "wifiFirmwareVersion": "1.0.0",
    "btFirmwareVersion": "2.0.0",
    "ipAddress": "*************"
  }
}
```
**發送時機**: 收到 welcome 後立即發送，之後每 30 秒

#### deviceStatus (設備狀態)
```json
{
  "type": "deviceStatus",
  "devices": [
    {
      "macAddress": "11:22:33:44:55:66",
      "status": "online",
      "data": {
        "size": "2.9\"",
        "battery": 85,
        "rssi": -65,
        "colorType": "BW",
        "imageCode": "12345678"  // 可選，只有本地有時才包含
      }
    }
  ]
}
```
**頻率**: 每 5 秒
**注意**: 不包含 `dataId`，這是由前端或API控制的欄位

### 接收消息 (Server → Gateway)

#### welcome (歡迎)
```json
{
  "type": "welcome",
  "message": "WebSocket 連接成功",
  "timestamp": 1640995200000,
  "gatewayInfo": {
    "gatewayId": "gateway_id",
    "storeId": "store_id",
    "macAddress": "AA:BB:CC:DD:EE:FF"
  }
}
```
**處理**: 收到後立即發送 gatewayInfo

#### pong (心跳回應)
```json
{
  "type": "pong",
  "timestamp": 1640995200000,
  "serverTime": 1640995200100
}
```

#### update_preview (圖像更新)
```json
{
  "type": "update_preview",
  "deviceMac": "11:22:33:44:55:66",
  "imageData": "data:image/png;base64,iVBORw0KGgo...",
  "imageCode": "87654321",
  "rawdata": [255, 255, 0, 128, 64, ...],  // EPD 原始數據陣列 (Uint8Array)
  "timestamp": "2021-12-31T16:00:00.000Z"
}
```
**處理**:
- 更新本地 imageCode，下次 deviceStatus 時包含新值
- `rawdata` 包含轉換後的 EPD 二進制數據，可直接發送到設備顯示

## ⚠️ 重要注意事項

### 1. MAC 地址安全
- `gatewayInfo` 中的 `macAddress` 必須與 JWT Token 中的完全一致
- 不匹配會導致連線被強制中斷並記錄安全事件

### 2. dataId 和 imageCode 處理
- **dataId**: 不應包含在設備回報中，這是由前端或API控制的欄位
- **imageCode**: 設備回報時不主動包含 `imageCode`
- 只有在收到 Server 圖像更新後才在本地存儲 `imageCode`
- 下次 `deviceStatus` 回報時包含更新後的 `imageCode`

### 3. 錯誤處理
```json
{
  "type": "gatewayInfoAck",
  "success": false,
  "fatal": true,  // 如果為 true，連線將被中斷
  "message": "MAC地址不匹配，連線已中斷"
}
```

## 🔧 實作檢查清單

### 連接建立
- [ ] 正確解析 JWT Token
- [ ] 使用正確的 WebSocket URL 格式
- [ ] 處理連接失敗和重連邏輯

### 消息處理
- [ ] 實作所有必要的消息類型
- [ ] 正確的消息發送頻率
- [ ] JSON 格式驗證和錯誤處理

### 設備管理
- [ ] 設備掃描和狀態更新
- [ ] imageCode 本地存儲和同步
- [ ] 設備生命週期管理

### 安全性
- [ ] MAC 地址驗證
- [ ] Token 過期處理
- [ ] 錯誤日誌記錄

## ❌ 失敗狀況速查

### 連接階段失敗
| 錯誤 | 狀態碼 | 原因 | 解決方法 |
|------|--------|------|----------|
| Token 驗證失敗 | 401 | Token 無效/過期 | 重新獲取 Config |
| Token 類型錯誤 | 401 | 非 gateway 類型 | 檢查 Token 來源 |
| Gateway ID 不匹配 | 403 | URL 與 Token 不符 | 檢查 Gateway 配置 |
| Store ID 不匹配 | 403 | 門店 ID 不符 | 檢查門店權限 |
| MAC 地址缺失 | 403 | Token 無 MAC | 重新生成 Token |

### 運行階段失敗
| 消息類型 | success | fatal | 錯誤原因 | 處理方式 |
|----------|---------|-------|----------|----------|
| gatewayInfoAck | false | true | MAC 地址不匹配 | 停止重連，檢查配置 |
| gatewayInfoAck | false | false | 網關被刪除 | 重新註冊網關 |
| gatewayInfoAck | false | false | 信息格式錯誤 | 檢查消息格式 |
| deviceStatusAck | false | - | 設備列表無效 | 檢查設備數據 |
| error | - | - | 未知消息類型 | 檢查消息格式 |

### 心跳檢測失敗
| 狀況 | 觸發條件 | 關閉代碼 | 處理方式 |
|------|----------|----------|----------|
| 心跳超時 | >30秒無ping | 1000 | 檢查網絡，重連 |
| 長時間無活動 | >60秒無消息 | 1000 | 檢查程序狀態 |

## 🐛 常見問題

### Q: 為什麼收到 "MAC地址不匹配" 錯誤？
A: 確保 `gatewayInfo` 消息中的 `macAddress` 與 JWT Token 中的完全一致。

### Q: 設備狀態應該包含哪些欄位？
A: 只包含 `macAddress`、`status` 和 `data` 對象。不包含 `dataId`（由前端/API控制）。

### Q: 設備狀態什麼時候包含 imageCode？
A: 只有在收到 Server 的 `update_preview` 消息並更新本地 imageCode 後。

### Q: 心跳超時怎麼辦？
A: 檢查網絡連接，確保每 25 秒發送一次 ping 消息。

### Q: 如何處理連接斷開？
A: 實作重連邏輯，等待 5-10 秒後重新嘗試連接。

### Q: fatal: true 錯誤如何處理？
A: 這是致命錯誤，應停止重連並檢查配置，通常是安全問題。

## 📋 測試步驟

1. **連接測試**
   ```bash
   # 使用測試腳本驗證連接
   node server/tests/ws-client-from-copied-info.js
   ```

2. **消息格式測試**
   - 驗證所有發送消息的 JSON 格式
   - 檢查必要字段是否完整

3. **錯誤處理測試**
   - 故意發送錯誤的 MAC 地址
   - 測試網絡中斷恢復

4. **性能測試**
   - 監控消息發送頻率
   - 檢查內存和 CPU 使用率

## 🔗 相關文檔

- [完整實作指南](./Gateway-Device-Implementation-Guide.md)
- [WebSocket 安全增強](../server/services/websocket-security-enhancement.md)
- [測試腳本說明](../server/tests/ws-client-from-copied-info-修正說明.md)

---

**最後更新**: 2024年1月
**版本**: 1.0.0
