import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Search, Import, Upload, Lightbulb, Plus, Trash2, Grid, Link, Trash, AlertCircle, RefreshCw, Edit } from 'lucide-react';
import { DataField, DataFieldSectionType } from '../types';
import { SystemSpecificData, getAllSystemSpecificData, deleteSystemSpecificData, syncDataFieldsToSystemSpecificData } from '../utils/api/systemSpecificDataApi';
import { getAllDataFields } from '../utils/api/dataFieldApi';
import { saveFieldsViewConfig, getFieldsViewConfig } from '../utils/api/sysConfigApi';
import { AddSystemSpecificDataModal } from './AddSystemSpecificDataModal';
import { EditSystemSpecificDataModal } from './EditSystemSpecificDataModal';
import { useTranslation } from 'react-i18next';

export function SystemSpecificDataPage() {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [systemSpecificData, setSystemSpecificData] = useState<SystemSpecificData[]>([]);
  const [dataFields, setDataFields] = useState<DataField[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [syncingFields, setSyncingFields] = useState(false);
  const [notification, setNotification] = useState<{ message: string, type: 'success' | 'error' } | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showFieldManager, setShowFieldManager] = useState(false);
  const [visibleFields, setVisibleFields] = useState<Record<string, boolean>>({});
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedSystemSpecificData, setSelectedSystemSpecificData] = useState<SystemSpecificData | null>(null);

  // 新增：過濾後的數據（搜索結果）
  const filteredSystemSpecificData = useMemo(() => {
    if (!searchTerm) return systemSpecificData;

    return systemSpecificData.filter(item => {
      // 在所有欄位中搜索
      return Object.entries(item).some(([key, value]) => {
        // 忽略 sn 欄位，只在其他欄位中搜索
        if (key === 'sn') return false;

        // 將值轉換為字符串後進行不區分大小寫的搜索
        return String(value).toLowerCase().includes(searchTerm.toLowerCase());
      });
    });
  }, [systemSpecificData, searchTerm]);

  // 新增：搜索條件變更時的處理
  useEffect(() => {
    // 當搜索條件變更時，調整選中狀態，只保留在過濾結果中的選中項
    const filteredUids = filteredSystemSpecificData.map(item => item.uid).filter(uid => uid !== undefined);
    const newSelectedItems = selectedItems.filter(uid => filteredUids.includes(uid));

    // 如果選中項發生了變化，更新選中狀態
    if (newSelectedItems.length !== selectedItems.length) {
      setSelectedItems(newSelectedItems);
    }
  }, [searchTerm, filteredSystemSpecificData]);

  // 初始化時獲取數據
  useEffect(() => {
    fetchData();
  }, []);

  // 檢查是否所有欄位都被選中
  const areAllFieldsSelected = () => {
    const selectableFields = dataFields.filter(field => field.id !== 'sn' && field.id !== 'id');
    return selectableFields.length > 0 &&
      selectableFields.every(field => visibleFields[field.id]);
  };

  // 計算是否有可顯示的浮動欄位（非 sn 和 id）
  const hasVisibleFloatingFields = Object.entries(visibleFields)
    .some(([fieldId, isVisible]) => isVisible && fieldId !== 'sn' && fieldId !== 'id');

  // 獲取系統專屬數據和資料欄位定義
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 同時獲取系統專屬數據、資料欄位定義和已保存的欄位顯示設定
      const [systemItems, fieldItems, savedConfig] = await Promise.all([
        getAllSystemSpecificData(),
        getAllDataFields(),
        getFieldsViewConfig().catch(err => {
          console.error('載入欄位視圖設定失敗:', err);
          return null; // 如果載入失敗，返回 null
        })
      ]);

      // 為系統專屬數據添加序號
      const systemItemsWithSn = systemItems.map((item, index) => ({
        ...item,
        sn: index + 1
      }));

      setSystemSpecificData(systemItemsWithSn);

      // 只保留一般資料欄位
      const ordinaryFields = fieldItems.filter(field => field.section === DataFieldSectionType.ORDINARY);
      setDataFields(ordinaryFields);

      // 初始化欄位顯示狀態，預設所有欄位都顯示
      const initialVisibility: Record<string, boolean> = {};

      // 先設置 sn 和 id 欄位為可見
      initialVisibility['sn'] = true;
      initialVisibility['id'] = true;

      // 設置其他欄位的可見性
      ordinaryFields.forEach(field => {
        // 如果有已保存的設定，使用已保存的設定
        if (savedConfig && savedConfig.visibleFields) {
          initialVisibility[field.id] = savedConfig.visibleFields.includes(field.id);
        } else {
          // 否則預設所有欄位都顯示
          initialVisibility[field.id] = true;
        }
      });

      setVisibleFields(initialVisibility);
    } catch (err: any) {
      console.error('獲取資料失敗:', err);
      setError(err.message || '獲取資料失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 顯示通知消息
  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({ message, type });
    // 5秒後自動關閉通知
    setTimeout(() => {
      setNotification(null);
    }, 5000);
  };

  // 處理欄位顯示設定變更
  const handleFieldVisibilityChange = (fieldId: string, isVisible: boolean) => {
    setVisibleFields(prev => ({
      ...prev,
      [fieldId]: isVisible
    }));
  };

  // 處理全選/取消全選
  const handleSelectAllFields = (isSelected: boolean) => {
    const newVisibility = { ...visibleFields };

    // 保持 sn 和 id 欄位的可見性不變
    dataFields.forEach(field => {
      if (field.id !== 'sn' && field.id !== 'id') {
        newVisibility[field.id] = isSelected;
      }
    });

    setVisibleFields(newVisibility);
  };

  // 保存欄位顯示設定
  const saveFieldsView = async () => {
    try {
      // 獲取所有可見欄位的 ID
      const visibleFieldIds = Object.entries(visibleFields)
        .filter(([_, isVisible]) => isVisible)
        .map(([fieldId]) => fieldId);

      // 保存設定
      await saveFieldsViewConfig({
        visibleFields: visibleFieldIds,
        columnOrder: [], // 暫不實現欄位排序
        columnWidths: {} // 暫不實現欄位寬度
      });

      showNotification('欄位顯示設定已保存', 'success');
    } catch (err: any) {
      console.error('保存欄位顯示設定失敗:', err);
      showNotification(err.message || '保存欄位顯示設定失敗', 'error');
    }
  };

  // 同步資料欄位到系統專屬數據結構
  const syncDataFields = async () => {
    try {
      setSyncingFields(true);
      const success = await syncDataFieldsToSystemSpecificData();
      if (success) {
        showNotification('資料欄位同步成功', 'success');
        // 重新獲取數據
        fetchData();
      } else {
        showNotification('資料欄位同步失敗', 'error');
      }
    } catch (err: any) {
      console.error('同步資料欄位失敗:', err);
      showNotification(err.message || '同步資料欄位失敗', 'error');
    } finally {
      setSyncingFields(false);
    }
  };

  // 處理編輯按鈕點擊
  const handleEdit = (item: SystemSpecificData) => {
    setSelectedSystemSpecificData(item);
    setShowEditModal(true);
  };

  // 處理單筆資料刪除
  const handleDelete = async (uid: string) => {
    if (window.confirm(t('common.confirm') + '?')) {
      try {
        await deleteSystemSpecificData(uid);
        showNotification('刪除系統專屬數據成功', 'success');
        // 更新本地數據並重新計算SN
        setSystemSpecificData(prevData => {
          const filteredData = prevData.filter(item => item.uid !== uid);
          // 重新計算SN
          return filteredData.map((item, index) => ({
            ...item,
            sn: index + 1
          }));
        });
      } catch (err: any) {
        showNotification(err.message || '刪除系統專屬數據失敗', 'error');
      }
    }
  };

  // 新增：處理批量刪除
  const handleBatchDelete = async () => {
    const count = selectedItems.length;
    if (count === 0) {
      showNotification('請先選擇要刪除的系統專屬數據', 'error');
      return;
    }

    if (window.confirm(`${t('common.confirm')}${count}?`)) {
      try {
        let deletedCount = 0;
        let errorCount = 0;

        // 依序刪除所選系統專屬數據
        for (const uid of selectedItems) {
          try {
            if (!uid) {
              console.error(`無效的資料ID: ${uid}`);
              errorCount++;
              continue;
            }

            await deleteSystemSpecificData(uid);
            deletedCount++;
          } catch (error) {
            console.error(`刪除系統專屬數據失敗 (ID: ${uid}):`, error);
            errorCount++;
          }
        }

        // 更新本地數據並重新計算SN
        setSystemSpecificData(prevData => {
          const filteredData = prevData.filter(item => !selectedItems.includes(item.uid));
          // 重新計算SN
          return filteredData.map((item, index) => ({
            ...item,
            sn: index + 1
          }));
        });

        // 清空選擇
        setSelectedItems([]);
        setSelectAll(false);

        // 顯示結果
        if (errorCount === 0) {
          showNotification(`已成功刪除 ${deletedCount} 筆系統專屬數據`, 'success');
        } else {
          showNotification(`成功刪除 ${deletedCount} 筆，失敗 ${errorCount} 筆系統專屬數據`, 'error');
        }
      } catch (err: any) {
        showNotification(err.message || '批量刪除系統專屬數據失敗', 'error');
      }
    }
  };

  return (
    <div className="p-8 lg:px-4 py-2">
      <div className="max-w-7xl mx-auto">
        {/* 通知消息 */}
        {notification && (
          <div
            className={`mb-4 p-4 rounded-md ${
              notification.type === 'success' ? 'bg-green-100 border-l-4 border-green-500 text-green-700' :
              'bg-red-100 border-l-4 border-red-500 text-red-700'
            } flex items-center justify-between`}
          >
            <div className="flex items-center">
              {notification.type === 'success' ? null : <AlertCircle className="w-5 h-5 mr-2" />}
              <span>{notification.message}</span>
            </div>
            <button
              className="text-gray-500 hover:text-gray-700"
              onClick={() => setNotification(null)}
            >
              &times;
            </button>
          </div>
        )}

        {/* 錯誤消息 */}
        {error && (
          <div className="mb-4 p-4 bg-red-100 border-l-4 border-red-500 text-red-700 rounded-md">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 mr-2" />
              <span>{error}</span>
            </div>
          </div>
        )}

        {/* Search and Actions Bar */}
        <div className="mb-6 flex items-center space-x-4">
          <div className="relative flex-1">
            <input
              type="text"
              placeholder={t('database.searchPlaceholder')}
              className="w-full pl-4 pr-10 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Search className="absolute right-3 top-2.5 text-gray-400 w-5 h-5" />
          </div>

          <div className="flex items-center space-x-2">
            <button
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              onClick={() => setShowAddModal(true)}
            >
              <Plus className="w-5 h-5" />
              {t('common.add')}
            </button>

            <button
              className="flex items-center gap-2 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
              onClick={handleBatchDelete}
              disabled={selectedItems.length === 0}
            >
              <Trash2 className="w-5 h-5" />
              {t('database.batchDelete')}
            </button>

            <button
              className="flex items-center gap-2 px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600"
              onClick={() => setShowFieldManager(!showFieldManager)}
            >
              <Grid className="w-5 h-5" />
              {t('database.fieldManager')}
            </button>

            <button
              className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
              onClick={syncDataFields}
              disabled={syncingFields}
            >
              <RefreshCw className={`w-5 h-5 ${syncingFields ? 'animate-spin' : ''}`} />
              {syncingFields ? t('database.syncing') : t('database.syncFields')}
            </button>
          </div>
        </div>

        {/* 欄位管理器 */}
        {showFieldManager && (
          <div className="mb-6 p-4 bg-gray-100 rounded-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">{t('database.fieldManagerTitle')}</h3>
              <div className="flex items-center gap-2">
                <button
                  className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm"
                  onClick={() => handleSelectAllFields(true)}
                  disabled={areAllFieldsSelected()}
                >
                  {t('common.selectAll')}
                </button>
                <button
                  className="px-3 py-1 bg-gray-500 text-white rounded-md hover:bg-gray-600 text-sm"
                  onClick={() => handleSelectAllFields(false)}
                >
                  {t('database.deselectAll')}
                </button>
                <button
                  className="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm"
                  onClick={saveFieldsView}
                >
                  {t('database.saveFieldsView')}
                </button>
              </div>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {dataFields.map(field => (
                <div key={field.id} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`field-${field.id}`}
                    checked={visibleFields[field.id] || false}
                    onChange={(e) => handleFieldVisibilityChange(field.id, e.target.checked)}
                    className="mr-2"
                    disabled={field.id === 'sn' || field.id === 'id'} // sn 和 id 欄位不可隱藏
                  />
                  <label htmlFor={`field-${field.id}`} className="text-sm">
                    {field.name}
                  </label>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Table */}
        <div className="bg-white rounded-lg shadow relative">
          {/* 使用三個固定位置的表格實現左右固定，中間滾動的效果 */}
          <div className="relative overflow-x-auto" style={{ maxWidth: "100%" }}>
            <div className="flex relative">
              {/* 左側固定欄位（勾選框和 SN） */}
              <div className="sticky left-0 z-20 bg-white shadow-sm" style={hasVisibleFloatingFields ? { width: "230px" } : { width: "130px" }}>
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 w-12 border-b border-gray-200 text-white">
                        <input
                          type="checkbox"
                          className="rounded"
                          checked={selectAll}
                          onChange={(e) => {
                            setSelectAll(e.target.checked);
                            setSelectedItems(e.target.checked ?
                              filteredSystemSpecificData.map(item => item.uid) :
                              []
                            );
                          }}
                        />
                      </th>
                      <th className="bg-amber-600 px-4 py-3 border-b border-gray-200 text-white text-left">
                        SN
                      </th>
                      {hasVisibleFloatingFields && (
                        <th className="bg-amber-600 px-4 py-3 border-b border-gray-200 text-white text-left">
                          ID
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={hasVisibleFloatingFields ? 3 : 2} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8">
                            <RefreshCw className="w-5 h-5 animate-spin mx-auto mb-2" />
                          </div>
                        </td>
                      </tr>
                    ) : filteredSystemSpecificData.length === 0 ? (
                      <tr>
                        <td colSpan={hasVisibleFloatingFields ? 3 : 2} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8">{t('database.noData')}</div>
                        </td>
                      </tr>
                    ) : (
                      filteredSystemSpecificData.map((item, index) => (
                        <tr key={`left-${item.sn}`} className={`h-[53px] ${index % 2 === 0 ? 'bg-amber-50' : 'bg-blue-50'}`}>
                          <td className={`px-4 py-3 border-b border-gray-200 align-middle`}>
                            <input
                              type="checkbox"
                              className="rounded"
                              checked={selectedItems.includes(item.uid)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedItems(prev => [...prev, item.uid]);
                                } else {
                                  setSelectedItems(prev => prev.filter(uid => uid !== item.uid));
                                  setSelectAll(false);
                                }
                              }}
                            />
                          </td>
                          <td className="px-4 py-3 border-b border-gray-200 align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                            {item.sn}
                          </td>
                          {hasVisibleFloatingFields && (
                            <td className="px-4 py-3 border-b border-gray-200 align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {item.id}
                            </td>
                          )}
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* 中間可滾動欄位 */}
              <div className="overflow-x-auto flex-1">
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      {!hasVisibleFloatingFields && (
                        <th className="bg-amber-600 px-4 py-3 border-b border-gray-200 text-white text-left">
                          ID
                        </th>
                      )}
                      {dataFields.map(field => {
                        // 排除 sn 和 id 欄位，它們已經在左側固定欄位中顯示
                        if (field.id === 'sn' || field.id === 'id') return null;

                        // 只顯示選中的欄位
                        if (!visibleFields[field.id]) return null;

                        return (
                          <th key={field.id} className="bg-amber-600 px-4 py-3 border-b border-gray-200 text-white text-left">
                            {field.name}
                          </th>
                        );
                      })}
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={!hasVisibleFloatingFields ? 1 : Object.values(visibleFields).filter(Boolean).length} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : filteredSystemSpecificData.length === 0 ? (
                      <tr>
                        <td colSpan={!hasVisibleFloatingFields ? 1 : Object.values(visibleFields).filter(Boolean).length} className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8">
                            {t('database.noSystemSpecificData')}
                          </div>
                        </td>
                      </tr>
                    ) : (
                      filteredSystemSpecificData.map((item, index) => (
                        <tr key={`mid-${item.sn}`} className={`h-[53px] ${index % 2 === 0 ? 'bg-amber-50' : 'bg-blue-50'}`}>
                          {!hasVisibleFloatingFields && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {item.id}
                            </td>
                          )}
                          {dataFields.map(field => {
                            // 排除 sn 和 id 欄位
                            if (field.id === 'sn' || field.id === 'id') return null;

                            // 只顯示選中的欄位
                            if (!visibleFields[field.id]) return null;

                            return (
                              <td key={field.id} className="px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                                {item[field.id] !== undefined ? String(item[field.id]) : ''}
                              </td>
                            );
                          })}
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* 右側固定欄位（操作） */}
              <div className="sticky right-0 z-20 bg-white shadow-sm" style={{ width: "100px" }}>
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 border-b border-gray-200 text-white text-left">
                        {t('database.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : filteredSystemSpecificData.length === 0 ? (
                      <tr>
                        <td className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : (
                      filteredSystemSpecificData.map((item, index) => (
                        <tr key={`right-${item.sn}`} className={`h-[53px] align-middle ${index % 2 === 0 ? 'bg-amber-50' : 'bg-blue-50'}`}>
                          <td className="px-4 py-3 border-b border-gray-200 whitespace-nowrap">
                            <div className="flex items-center gap-2">
                              <button
                                className="text-gray-500 hover:text-blue-600"
                                onClick={() => handleEdit(item)}
                              >
                                <Edit className="w-5 h-5" />
                              </button>
                              <button
                                className="text-gray-500 hover:text-red-600"
                                onClick={() => handleDelete(item.uid)}
                              >
                                <Trash className="w-5 h-5" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        {/* Pagination */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              className="px-4 py-2 bg-violet-500 text-white rounded-md"
              onClick={() => setCurrentPage(1)}
            >
              1
            </button>
            <select
              className="border rounded-md px-2 py-1"
              value={itemsPerPage}
            >
              <option value="10">10 / {t('common.page')}</option>
            </select>
            <span>1 {t('common.pagesTotal')}</span>
          </div>
          <div className="flex items-center gap-2">
            <span>{t('common.goTo')}</span>
            <input
              type="number"
              className="w-20 border rounded-md px-2 py-1"
              min={1}
              max={1}
              value={currentPage}
              onChange={(e) => setCurrentPage(Number(e.target.value))}
            />
            <span>{t('common.page')}</span>
            <button className="px-4 py-1 border rounded-md hover:bg-gray-50">
              {t('common.confirm')}
            </button>
          </div>
        </div>
      </div>

      {/* 新增系統專屬數據模態窗口 */}
      <AddSystemSpecificDataModal
        isOpen={showAddModal}
        dataFields={dataFields}
        onClose={() => setShowAddModal(false)}
        onSuccess={() => {
          // 重新加載系統專屬數據
          fetchData();
          // 顯示成功訊息
          showNotification('系統專屬數據新增成功', 'success');
        }}
      />

      {/* 編輯系統專屬數據模態窗口 */}
      <EditSystemSpecificDataModal
        isOpen={showEditModal}
        dataFields={dataFields}
        systemSpecificData={selectedSystemSpecificData}
        onClose={() => {
          setShowEditModal(false);
          setSelectedSystemSpecificData(null);
        }}
        onSuccess={() => {
          // 重新加載系統專屬數據
          fetchData();
          // 顯示成功訊息
          showNotification('系統專屬數據修改成功', 'success');
        }}
      />
    </div>
  );
}
