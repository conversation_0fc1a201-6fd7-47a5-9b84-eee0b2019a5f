import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import App from './App.tsx';
import './index.css';

// 導入 i18n 配置
import './i18n';

// 添加全局錯誤處理器
window.addEventListener('error', (event) => {
  console.error('全局錯誤:', event.error);
});

// 添加未處理的 Promise 拒絕處理器
window.addEventListener('unhandledrejection', (event) => {
  console.error('未處理的 Promise 拒絕:', event.reason);
});

// 確保 DOM 已加載
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM 已加載，開始渲染 React 應用');

  const rootElement = document.getElementById('root');

  if (rootElement) {
    try {
      console.log('掛載 React 應用');
      const root = createRoot(rootElement);
      root.render(
        <StrictMode>
          <BrowserRouter>
            <App />
          </BrowserRouter>
        </StrictMode>
      );
      console.log('React 應用掛載成功');
    } catch (error) {
      console.error('React 渲染錯誤:', error);
      // 如果 React 無法渲染，顯示錯誤信息
      rootElement.innerHTML = `
        <div style="display:flex; justify-content:center; align-items:center; height:100vh; flex-direction:column; padding:20px;">
          <h2 style="color:red; margin-bottom:20px;">無法渲染應用</h2>
          <p>發生錯誤: ${String(error)}</p>
          <button onclick="location.reload()" style="margin-top:20px; padding:10px 20px; background:#3b82f6; color:white; border:none; border-radius:4px; cursor:pointer;">
            重新加載頁面
          </button>
        </div>
      `;
    }
  } else {
    console.error('找不到 root 元素');
  }
});
