/**
 * 螢幕配置 - 後端版本
 * 與前端 src/screens/ 中的配置保持同步
 */

// DisplayColorType 枚舉 (對應前端的 DisplayColorType)
const DisplayColorType = {
  BW: 'Gray16',
  BWR: 'Black & White & Red',
  BWRY: 'Black & White & Red & Yellow',
  UNKNOWN: 'UNKNOWN'
};

// 螢幕配置定義
const screenConfigs = [
  {
    id: "2_13inch",
    name: "2.13\"",
    displayName: "2.13\" (122x250)",
    width: 122,
    height: 250,
    supportedColors: [DisplayColorType.BWR]
  },
  {
    id: "2_9inch",
    name: "2.9\"",
    displayName: "2.9\" (128x296)",
    width: 128,
    height: 296,
    supportedColors: [DisplayColorType.BWR]
  },
  {
    id: "3_7inch",
    name: "3.7\"",
    displayName: "3.7\" (240x416)",
    width: 240,
    height: 416,
    supportedColors: [DisplayColorType.BWRY]
  },
  {
    id: "6inch",
    name: "6\"",
    displayName: "6\" (1024x758)",
    width: 1024,
    height: 758,
    supportedColors: [DisplayColorType.BW]
  },
  {
    id: "7_5inch",
    name: "7.5\"",
    displayName: "7.5\" (800x480)",
    width: 800,
    height: 480,
    supportedColors: [DisplayColorType.BWR]
  }
];

/**
 * 獲取所有螢幕配置
 */
function getScreenConfigs() {
  return screenConfigs;
}

/**
 * 從 screens 目錄動態生成的尺寸映射關係
 * 鍵為尺寸標識符，如 "2.9"，值為實際分辨率，如 "128x296"
 */
function getScreenSizeMap() {
  const sizeMap = {};
  
  screenConfigs.forEach(config => {
    // 提取尺寸標識符，如 "2.9"（移除英寸單位）
    const sizeIdentifier = config.name.replace('"', '');
    
    // 生成分辨率字符串
    const resolution = `${config.width}x${config.height}`;
    
    // 添加映射關係
    sizeMap[sizeIdentifier] = resolution;
  });

  return sizeMap;
}

/**
 * 獲取尺寸標識符到寬高的映射
 * 鍵為尺寸標識符，如 "2.9"，值為 {width, height} 對象
 */
function getScreenDimensionsMap() {
  const dimensionsMap = {};
  
  screenConfigs.forEach(config => {
    // 提取尺寸標識符，如 "2.9"（移除英寸單位）
    const sizeIdentifier = config.name.replace('"', '');
    
    // 添加寬高映射
    dimensionsMap[sizeIdentifier] = {
      width: config.width,
      height: config.height
    };
  });

  return dimensionsMap;
}

/**
 * 根據尺寸標識符獲取螢幕配置
 */
function getScreenConfigBySize(sizeIdentifier) {
  return screenConfigs.find(config => {
    const configSizeIdentifier = config.name.replace('"', '');
    return configSizeIdentifier === sizeIdentifier;
  });
}

module.exports = {
  DisplayColorType,
  getScreenConfigs,
  getScreenSizeMap,
  getScreenDimensionsMap,
  getScreenConfigBySize
};
