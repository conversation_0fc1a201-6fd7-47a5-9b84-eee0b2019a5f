// 門店專屬數據（原 StoreData）
export interface StoreSpecificData {
  _uid: string;       // 唯一識別碼（用於識別和查找特定的門店數據項）
  id: string;
  [key: string]: any; // 動態欄位（包括 id, name, description, price, quantity, date 等）
}

// 門店數據模型
export interface Store {
  _id?: string;
  id: string;        // 門店編號，如 TP001
  name: string;      // 門店名稱  
  address: string;   // 門店地址
  phone?: string;    // 門店電話
  managerId?: string; // 門店管理員ID
  status?: 'active' | 'inactive'; // 門店狀態
  createdAt?: string; // 創建時間
  updatedAt?: string; // 更新時間

  // 新增字段
  storeSpecificData?: StoreSpecificData[]; // 門店專屬數據（原 StoreData）
  gatewayManagement?: Record<string, any>; // 網關管理數據（預留）
  deviceManagement?: Record<string, any>;  // 設備管理數據（預留）
  storeSettings?: Record<string, any>;     // 門店設置（預留）
}

// 門店列表響應
export interface StoreListResponse {
  stores: Store[];
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}
